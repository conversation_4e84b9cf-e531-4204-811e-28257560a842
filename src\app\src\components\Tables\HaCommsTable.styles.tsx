import styled from 'styled-components';
import { PhlexTable } from 'phlex-core-ui';

const Table = styled(PhlexTable)`
  margin-top: 1.5rem;
  background-color: ${(props) => props.theme.colors.backgroundSec};
  color: ${(props) => props.theme.colors.textPri};

  --kendo-scrollbar-width: 0;
  &.k-grid .k-grid-content {
    overflow: hidden;
  }
  .k-table-tbody {
    color: ${(props) => props.theme.colors.textPri};
  }
  .k-grid-norecords {
    background-color: ${(props) => props.theme.colors.backgroundSec};
    color: ${(props) => props.theme.colors.textPri};
  }
  .k-table-thead {
    background-color: ${(props) => props.theme.colors.backgroundSec};
    color: ${(props) => props.theme.colors.textPri};
  }
  .k-grid-content tr.k-master-row. {
    cursor: pointer;
  }
  .k-grid-content tr.k-master-row td.wrapText {
    text-wrap: wrap;
  }
  .k-grid-content .k-detail-cell tr.k-master-row {
    cursor: default;
  }
`;

export { Table };
