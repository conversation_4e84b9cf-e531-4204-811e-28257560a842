import { ManageCommunicationFormProps, Comment } from 'components/Communication/communication-form-types';
import { FormValidationError } from 'components/shared/types/types';

export const validateForm = (formValues: ManageCommunicationFormProps) => {
  let errors: FormValidationError[] = [];

  if (!formValues.comments || formValues.comments.length === 0) {
    errors.push({
      fieldName: `comments`,
      message: 'ManageCommunication.ProductCommentsRequired',
      label: '',
    });
  }

  formValues.comments?.forEach((c, index) => {
    errors = [...checkForDuplicateProductExtensions(c, index)];
  });

  return errors;
};

export const checkForDuplicateProductExtensions = (comment: Comment, commentIx: number) => {
  const errors: FormValidationError[] = [];

  if (comment.productExtensions?.map((pe) => pe.id).some((e, i, arr) => arr.indexOf(e) !== i)) {
    errors.push({
      fieldName: `comments.${commentIx}.productExtensions`,
      message: 'Forms.DuplicateProductExtension',
      label: '',
    });
  }
  return errors;
};
