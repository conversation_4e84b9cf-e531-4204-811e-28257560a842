import { CommentDtoModel, ProductDtoModel, SearchDetailsModel } from 'axon-hacomms-api-sdk';

export interface SearchCommunicationOverviewProps {
  communication: SearchDetailsModel | undefined;
  tenant: string;
}

export interface SearchCommentDetailsProps {
  comment: CommentDtoModel | undefined;
  number: number;
}

export interface SearchOtherRelatedCommentsProps {
  communicationId: number | undefined;
  productId: number | undefined;
  excludedCommentId: number | undefined;
  tenant: string;
}

export interface SearchCommentsPerProductProps {
  isOpen: boolean;
  communicationId: number | undefined;
  product: ProductDtoModel | undefined;
  tenant: string;
  onClose: () => void;
}

export interface PagedProps {
  page: number;
  skip: number;
  take: number;
}
