import { ProductTypeModel, SearchCommentCommandRequest } from 'axon-hacomms-api-sdk';
import { MultiselectData } from 'components/shared/types/types';
import { PhlexMultiSelect } from 'phlex-core-ui';
import React, { FC, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { productTypeApiService } from 'shared/api';
import { PRODUCT_TYPE_KEY, SearchFiltersProps } from './search-filter-types';
import { getFiltersFromStorage, updateFiltersInStorage } from './search-filter-service';

const ProductTypesFilterComponent: FC<SearchFiltersProps> = (props: SearchFiltersProps) => {
  const { tenant, setSearchModel, disable, clear } = props;
  const { t } = useTranslation();
  const [selectedProductTypes, setSelectedProductTypes] = useState<MultiselectData>([]);

  const fetchProductTypes = async (): Promise<MultiselectData> => {
    const response = await productTypeApiService.getProductTypesList(tenant);
    const data = response.data.data?.map((item: ProductTypeModel) => ({ value: item.id?.toString() ?? '', label: item.name ?? '' })) ?? [];
    return data;
  };

  const updateSearchModel = (data: MultiselectData) => {
    setSearchModel((request: SearchCommentCommandRequest) => ({
      ...request,
      productTypes: data.map((x) => ({ id: Number(x.value), name: x.label })),
      skip: 0,
      take: 10,
    }));
  };

  const onProductTypeFilterChange = (data: MultiselectData) => {
    setSelectedProductTypes(data);
    updateFiltersInStorage(data, PRODUCT_TYPE_KEY);
    updateSearchModel(data);
  };

  useEffect(() => {
    const searchFilters = getFiltersFromStorage();
    if (searchFilters?.filters[PRODUCT_TYPE_KEY] && searchFilters.filters[PRODUCT_TYPE_KEY].length > 0) {
      const filterData = searchFilters.filters[PRODUCT_TYPE_KEY];
      setSelectedProductTypes(filterData);
      updateSearchModel(filterData);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (disable) {
      setSelectedProductTypes([]);
      updateFiltersInStorage([], PRODUCT_TYPE_KEY);
    }
  }, [disable]);

  useEffect(() => {
    if (clear) setSelectedProductTypes([]);
  }, [clear]);

  return (
    <PhlexMultiSelect
      data-testid={'axon-input-search-filter-product-type'}
      name={`productTypeSearchFilter`}
      label={t('SearchFilters.ProductType')}
      value={selectedProductTypes}
      defaultValues={selectedProductTypes}
      width="fullwidth"
      compactMode={true}
      fetchData={() => fetchProductTypes()}
      onlyFetchOnOpen={true}
      onChange={onProductTypeFilterChange}
      disabled={disable}
    />
  );
};

export default ProductTypesFilterComponent;
