import { PayloadAction } from '@reduxjs/toolkit';
import { useDispatch, useSelector } from 'react-redux';
import type { TypedUseSelectorHook } from 'react-redux';

import type { RootState, AppDispatch } from '.';

export const useAppDispatch: () => AppDispatch = useDispatch;
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;

export const useAppDispatchWithCallback = (callback: () => void): ((action: PayloadAction<any, any>) => void) => {
  const dispatch = useAppDispatch();
  return (action: PayloadAction<any, any>) => {
    dispatch(action);
    callback();
  };
};
