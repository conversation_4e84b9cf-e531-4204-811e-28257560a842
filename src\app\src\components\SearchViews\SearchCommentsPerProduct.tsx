import React, { FC, useCallback, useEffect, useState } from 'react';
import { PagedProps, SearchCommentsPerProductProps } from './search-view-types';
import { PhlexButton, PhlexLoader, PhlexSlideout } from 'phlex-core-ui';
import { commentApiService } from 'shared/api';
import { CommentDtoModel } from 'axon-hacomms-api-sdk';
import SearchCommentDetails from './SearchCommentDetails';
import { useTranslation } from 'react-i18next';
import components from './SearchCommentsPerProduct.styles';

const initialPagedModel: PagedProps = { page: 0, skip: 0, take: 10 };

const SearchCommentsPerProductComponent: FC<SearchCommentsPerProductProps> = (props: SearchCommentsPerProductProps) => {
  const { tenant, communicationId, product, onClose } = props;
  const { t } = useTranslation();
  const [comments, setComments] = useState<CommentDtoModel[]>([]);
  const [total, setTotal] = useState<number | null | undefined>();
  const [page, setPage] = useState<number>(0);
  const [loading, setLoading] = useState(false);
  const [pagedModel, setPagedModel] = useState<PagedProps>(initialPagedModel);
  const { LoadMoreButtonWrapper, CommentWrapper } = components;

  const getComments = useCallback(
    async (communicationId: number, productId: number | undefined, pagedModel: PagedProps) => {
      setLoading(true);
      const response = await commentApiService.getCommentsByCommunicationId(
        communicationId,
        tenant,
        productId,
        undefined,
        pagedModel.skip,
        pagedModel.take
      );
      setComments((prevState) => [...prevState, ...(response.data.data ?? [])]);
      setTotal(response.data.paging?.totalItemCount);
      setPage((page) => page + 1);
      setLoading(false);
    },
    [tenant]
  );

  const loadMore = () => {
    setPagedModel((request: PagedProps) => ({
      ...request,
      page: page,
      skip: page * pagedModel.take,
    }));
  };

  useEffect(() => {
    if (communicationId) {
      getComments(communicationId, product?.id, pagedModel);
    }
  }, [communicationId, product, getComments, pagedModel]);

  return (
    <PhlexSlideout
      open={props.isOpen}
      onClose={() => {
        onClose();
      }}
      position="right"
      width="60rem"
      heading={t('SearchCommunicationOtherCommentsForProduct.SlideoutHeading', { productName: product?.name })}
      showScrollbar={true}
    >
      {comments?.map((comment, index) => {
        return (
          <CommentWrapper key={index + 1}>
            <SearchCommentDetails comment={comment} number={index + 1} />
          </CommentWrapper>
        );
      })}
      <LoadMoreButtonWrapper>
        {loading ? (
          <PhlexLoader position="center" />
        ) : (
          <PhlexButton
            title={t('Common.LoadMoreButtonLabel')}
            label={t('Common.LoadMoreButtonLabel')}
            hidden={total != null && total <= pagedModel.take + pagedModel.skip}
            onClick={() => {
              loadMore();
            }}
          />
        )}
      </LoadMoreButtonWrapper>
    </PhlexSlideout>
  );
};

export default SearchCommentsPerProductComponent;
