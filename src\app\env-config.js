window._env_ = {
  PUBLIC_URL: 'https://localhost:4000/',
  HACOMMS_API_BASE_URL: 'https://localhost',
  AXON_CORE_API_BASE_URL: 'https://app-dev.smartphlex.com/api/core',
  AXON_SHARED_LOCAL_TOKEN: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsImtpZCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.R5V_K-SmkeXbr-mlFYuSv1IEzGaQOseSUmfzjb0CW27IUO06qgbEx6tOKPQVBTmosj1FrpidsPwi_yUCOk5A7UTjpzwhiIjMCh87u1hLC6fUOlDpvHxfucfxhd9tNhxyVmBYRSarNC4XZy9eit17PMDyREP7U5nF6ti89k1h48LpvH8E92PtJ7fj1DSkINX3eHMwc_VZiMRuRkD2j4f-bPXYlbMVWpJLXLw7102hr1NN7JSVJvXkrjuMFbEGYT8dgMMeTf9j_66lsZX3g3mGqYk6VIaM1qMZRkbnYhoTNhIUs1hIojKjiY0WR8Da5m2Ur-SZZUHqfbVC2pzRwRvfdA',
  AXON_SHARED_LOCAL_COOKIE: true,
  LOCAL_PERMISSIONS: [
    'CanSearchComments',
    'CreateComment',
    'EditComment',
    'DeleteComment',
    'ViewCommunication',
    'EditCommunication',
    'CreateCommunication',
    'DeleteCommunication',
    'ViewCountry',
    'ViewProduct',
    'ViewProductList',
    'EditProduct',
    'CreateProduct',
    'DeleteProduct',
    'ViewProductCode',
    'ViewDosageFormList',
    'ViewRouteOfAdministrationList',
    'ViewTagList',
    'ViewSubstance',
    'ViewSubstanceList',
    'EditSubstance',
    'CreateSubstance',
    'DeleteSubstance',
    'ViewDrugType',
    'ViewApplicationNumber',
    'ViewSubmissionNumber',
    'ViewSubmissionType',
    'ViewProductType',
    'ViewProductType',
    'ViewRouteOfAdministration',
    'CreateRouteOfAdministration',
    'EditRouteOfAdministration',
    'DeleteRouteOfAdministration',
    'ViewDosageForm',
    'CreateDosageForm',
    'EditDosageForm',
    'DeleteDosageForm',
    'ViewTag',
    'CreateTag',
    'EditTag',
    'DeleteTag',
  ],
};

