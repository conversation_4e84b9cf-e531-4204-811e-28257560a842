import styled from 'styled-components';

export default {
  Wrapper: styled.div`
    box-sizing: border-box;
    display: flex;
    flex: 2;
    flex-direction: column;
    height: calc(100vh - 6.25rem);
    padding: 1rem;
  `,
  OtherCommentWrapper: styled.div`
    margin-bottom: 1rem;
  `,
  HeadingWrapper: styled.div`
    display: flex;
    justify-content: space-between;
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  `,
  Heading: styled.h2`
    font-size: ${(props) => props.theme.headingM};
    font-weight: ${(props) => props.theme.bold};
    margin-top: 0;
    margin-bottom: 1rem;
  `,
  BoldProduct: styled.span`
    font-weight: ${(props) => props.theme.bold};
  `,
  LoadMoreButtonWrapper: styled.div`
    display: grid;
    place-items: center;
    margin-top: 2rem;
  `,
};
