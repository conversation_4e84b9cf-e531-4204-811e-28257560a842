import { TableIcon } from 'components/Content/Content.styles';
import { TableColumn } from 'phlex-core-ui/build/src/controls/PhlexTable/PhlexTable.types';
import React, { ComponentType, FunctionComponent, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { GridCellProps, GridPageChangeEvent, GridRowClickEvent, GridSortChangeEvent } from '@progress/kendo-react-grid';
import { SearchComment } from 'axon-hacomms-api-sdk';
import { toDateTimeFormat } from 'shared/services/date-time-format';
import { SortDescriptor } from '@progress/kendo-data-query';
import { routes } from 'pages';
import { useNavigate } from 'react-router';
import { generateDrugSubstanceLabelFromList } from 'shared/services/drug-substance-srv';
import { PhlexIcon } from 'phlex-core-ui';
import { getFiltersFromStorage } from 'components/SearchFilters/search-filter-service';
import TableCellWithHighlight from './TableCellWithHighlight';
import HaCommsTable from 'components/Tables/HaCommsTable';

interface SearchTableProps {
  onPageChange: ((event: GridPageChangeEvent) => void) | undefined;
  onSortChange: (event: GridSortChangeEvent) => void;
  sort: SortDescriptor[];
  data: SearchComment[];
  total: number;
  skip: number;
  take: number;
  cleanBasePath: string;
  columns: TableColumn[];
  height?: number;
}

interface ISearchDetails {
  communicationId: number;
  commentId: number;
}

const SearchTableComponent: FunctionComponent<SearchTableProps> = (props: SearchTableProps) => {
  const { cleanBasePath, columns, data, height, onPageChange, onSortChange, sort, total, skip, take } = props;
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [navigateToSearchDetails, setNavigateToSearchDetails] = useState<ISearchDetails | null>(null);
  const [nameOfRows] = useState(t('SearchTable.SearchTablePager'));

  useEffect(() => {
    if (navigateToSearchDetails) {
      navigate(`${cleanBasePath}${routes.viewComment}`, {
        state: { communicationId: navigateToSearchDetails.communicationId, commentId: navigateToSearchDetails.commentId },
      });
    }
  }, [navigateToSearchDetails, navigate, cleanBasePath]);

  const viewCell = (e: GridCellProps) => {
    return (
      <TableIcon
        style={e.style}
        className={e.className}
        title={t('SearchTable.ViewDetails')}
        onClick={() => setNavigateToSearchDetails({ communicationId: e.dataItem.communicationId, commentId: e.dataItem.commentId })}
      >
        chevron_right
      </TableIcon>
    );
  };

  const linkCellBirdsLinkToBISAMP = (e: GridCellProps) => {
    return (
      <td>
        {e.dataItem.birdsLinkToBISAMP && (
          <a href={e.dataItem.birdsLinkToBISAMP} target="_blank" rel="noreferrer" style={e.style} className={e.className}>
            <PhlexIcon name="open_in_new" tooltip={e.dataItem.birdsLinkToBISAMP} />
          </a>
        )}
      </td>
    );
  };

  const linkCellBIRDSLinkToBIResponse = (e: GridCellProps) => {
    return (
      <td>
        {e.dataItem.birdsLinkToBIResponse && (
          <a href={e.dataItem.birdsLinkToBIResponse} target="_blank" rel="noreferrer" style={e.style} className={e.className}>
            <PhlexIcon name="open_in_new" tooltip={e.dataItem.birdsLinkToBIResponse} />
          </a>
        )}
      </td>
    );
  };

  const onRowClick = (e: GridRowClickEvent) =>
    setNavigateToSearchDetails({ communicationId: e.dataItem.communicationId, commentId: e.dataItem.commentId });

  const commentsCell = (gridCell: GridCellProps) => {
    return (
      <TableCellWithHighlight
        cellText={gridCell.dataItem.description}
        searchText={getFiltersFromStorage()?.searchText as string}
        onRowClick={onRowClick}
        gridCell={gridCell}
        style={gridCell.style}
        className={gridCell.className}
      ></TableCellWithHighlight>
    );
  };

  const questionCell = (gridCell: GridCellProps) => {
    return (
      <TableCellWithHighlight
        cellText={gridCell.dataItem.question}
        searchText={getFiltersFromStorage()?.searchText as string}
        onRowClick={onRowClick}
        gridCell={gridCell}
        style={gridCell.style}
        className={gridCell.className}
      ></TableCellWithHighlight>
    );
  };

  const responseCell = (gridCell: GridCellProps) => {
    return (
      <TableCellWithHighlight
        cellText={gridCell.dataItem.response}
        searchText={getFiltersFromStorage()?.searchText as string}
        onRowClick={onRowClick}
        gridCell={gridCell}
        style={gridCell.style}
        className={gridCell.className}
      ></TableCellWithHighlight>
    );
  };

  const tableColumns = [...columns];

  const replaceColumn = (
    identifier: string,
    title: string,
    width: string | undefined,
    locked: boolean,
    cell: ComponentType<GridCellProps>,
    tableColumns: TableColumn[]
  ) => {
    const index = tableColumns.findIndex((x) => x.identifier === identifier);
    if (index > -1) {
      tableColumns.splice(index, 1);
      if (index === 0) {
        tableColumns.unshift({
          identifier: identifier,
          title: title,
          cell: cell,
          width: width,
          lock: locked,
        });
      } else {
        tableColumns.push({
          identifier: identifier,
          title: title,
          cell: cell,
          width: width,
          lock: locked,
        });
      }
    }
  };

  replaceColumn('birdsLinkToBISAMP', t('SearchTable.BirdsLinkToBISAMP'), '200', false, linkCellBirdsLinkToBISAMP, tableColumns);
  replaceColumn('birdsLinkToBIResponse', t('SearchTable.BirdsLinkToBIResponse'), '200', false, linkCellBIRDSLinkToBIResponse, tableColumns);
  replaceColumn('description', t('SearchTable.Comments'), '300', true, commentsCell, tableColumns);
  replaceColumn('question', t('SearchTable.Question'), '200', false, questionCell, tableColumns);
  replaceColumn('response', t('SearchTable.Response'), '200', false, responseCell, tableColumns);

  tableColumns.push({ identifier: '', title: '', cell: viewCell, width: '56' });

  return (
    <HaCommsTable
      data={data.map((x) => ({
        ...x,
        description: x.description ?? t('Common.NotApplicable'),
        dateOfCommunication: toDateTimeFormat(x.dateOfCommunication),
        drugSubstanceNames: x.isGeneralGuidance
          ? t('Common.NotApplicable')
          : generateDrugSubstanceLabelFromList(
              x.drugSubstanceNames?.split(', ').map((x: string) => {
                const drugSubstanceArray = x.split('|');
                return { name: drugSubstanceArray[0], code: drugSubstanceArray[1] };
              }) ?? [],
              t('Common.DrugSubstanceCode'),
              t('Common.DrugSubstanceName')
            ),
        productName: x.isGeneralGuidance ? t('Common.NotApplicable') : x.productName,
        productCodeNames: x.isGeneralGuidance ? t('Common.NotApplicable') : x.productCodes,
        productTypeNames: x.isGeneralGuidance ? t('Common.NotApplicable') : x.productTypeNames,
        dosageFormNames: x.isGeneralGuidance ? t('Common.NotApplicable') : x.dosageFormNames,
        routeOfAdministrationNames: x.isGeneralGuidance ? t('Common.NotApplicable') : x.routeOfAdministrationNames,
        isQuestionIncluded: x.isQuestionIncluded
          ? t('SearchFilters.QuestionFilterOptions.QuestionIncluded')
          : t('SearchFilters.QuestionFilterOptions.CommentsOnly'),
        isGeneralGuidance: x.isGeneralGuidance
          ? t('SearchFilters.GeneralGuidanceFilterOptions.GeneralGuidance')
          : t('SearchFilters.GeneralGuidanceFilterOptions.NongeneralGuidance'),
        question: x.question ?? t('Common.NotApplicable'),
        response: x.response ?? t('Common.NotApplicable'),
      }))}
      uniqueIdField={'id'}
      pageable={true}
      onPageChange={onPageChange}
      sortable={true}
      onSortChange={onSortChange}
      sort={sort}
      onRowClick={onRowClick}
      total={total}
      columns={tableColumns}
      skip={skip}
      take={take}
      nameOfRows={nameOfRows}
      isSearchTable={true}
      height={height}
    />
  );
};

export default SearchTableComponent;
