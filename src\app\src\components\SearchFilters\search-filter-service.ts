import { SelectionRange } from '@progress/kendo-react-dateinputs';
import { MultiselectData } from 'components/shared/types/types';
import { TableColumn } from 'phlex-core-ui/build/src/controls/PhlexDataGrid/PhlexDataGrid';
import { SEARCH_TABLE_COLUMNS_KEY, SearchFilterState, USER_SEARCH_FILTER, defaultFilterState } from './search-filter-types';

export const getFiltersFromStorage = () => {
  const searchFiltersFromStorage = localStorage.getItem(USER_SEARCH_FILTER);
  if (searchFiltersFromStorage) {
    const searchFilters: SearchFilterState = JSON.parse(searchFiltersFromStorage);
    return searchFilters;
  }

  return null;
};

export const removeFiltersFromStorage = () => {
  clearDefaultFilterState();
  localStorage.removeItem(USER_SEARCH_FILTER);
};

export const clearDefaultFilterState = () => {
  defaultFilterState.filters = {};
  defaultFilterState.generalGuidance = undefined;
  defaultFilterState.isQuestionIncluded = undefined;
  defaultFilterState.searchText = undefined;
  defaultFilterState.communicationDateRange = undefined;
};

export const getSearchColumnsFromStorage = () => {
  const searchTableColumns = localStorage.getItem(SEARCH_TABLE_COLUMNS_KEY);
  if (searchTableColumns) {
    const tableColumns: TableColumn[] = JSON.parse(searchTableColumns);
    return tableColumns;
  }

  return null;
};

export const updateSearchColumnsFromStorage = (searchColumns: TableColumn[]) => {
  localStorage.setItem(SEARCH_TABLE_COLUMNS_KEY, JSON.stringify(searchColumns));
};

const updateSearchFiltersState = (searchFilters: SearchFilterState) => {
  localStorage.setItem(USER_SEARCH_FILTER, JSON.stringify(searchFilters));
};

export const updateFiltersInStorage = (data: MultiselectData, fieldName: string) => {
  const searchFilters = getFiltersFromStorage() ?? defaultFilterState;
  searchFilters.filters[fieldName] = data;
  updateSearchFiltersState(searchFilters);
};

export const updateSearchTextInStorage = (searchText: string) => {
  const searchFilters = getFiltersFromStorage() ?? defaultFilterState;
  searchFilters.searchText = searchText;
  updateSearchFiltersState(searchFilters);
};

export const updateFuzzySearchInStorage = (isFuzzySearch: boolean) => {
  const searchFilters = getFiltersFromStorage() ?? defaultFilterState;
  searchFilters.fuzzy = isFuzzySearch;
  updateSearchFiltersState(searchFilters);
};

export const updateCommunicationDatesInStorage = (communicationDateRange: SelectionRange) => {
  const searchFilters = getFiltersFromStorage() ?? defaultFilterState;
  searchFilters.communicationDateRange = communicationDateRange;
  updateSearchFiltersState(searchFilters);
};

export const updateIsQuestionIncludedInStorage = (value: boolean | undefined) => {
  const searchFilters = getFiltersFromStorage() ?? defaultFilterState;
  searchFilters.isQuestionIncluded = value;
  updateSearchFiltersState(searchFilters);
};

export const updateGeneralGuidanceInStorage = (value: boolean | undefined) => {
  const searchFilters = getFiltersFromStorage() ?? defaultFilterState;
  searchFilters.generalGuidance = value;
  updateSearchFiltersState(searchFilters);
};
