import { InputChangeEvent, SwitchChangeEvent } from '@progress/kendo-react-inputs';
import { SearchCommentCommandRequest } from 'axon-hacomms-api-sdk';
import { debounce } from 'lodash';
import { PhlexInput } from 'phlex-core-ui';
import React, { FC, useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { getFiltersFromStorage, updateFuzzySearchInStorage, updateSearchTextInStorage } from './search-filter-service';
import { SearchFiltersProps } from './search-filter-types';

const TextFilterComponent: FC<SearchFiltersProps> = (props: SearchFiltersProps) => {
  const { setSearchModel, clear } = props;
  const { t } = useTranslation();
  const [searchText, setSearchText] = useState<string>('');
  const [isFuzzySearch, setIsFuzzySearch] = useState<boolean>(false);

  const updateSearchModel = useCallback(
    (value: string, isFuzzySearch: boolean) => {
      setSearchModel((request: SearchCommentCommandRequest) => ({
        ...request,
        searchText: value,
        fuzzy: isFuzzySearch,
        skip: 0,
        take: 10,
      }));
    },
    [setSearchModel]
  );

  const debouncedSetSearchModel = useMemo(() => {
    return updateSearchModel ? debounce(updateSearchModel, 500) : null;
  }, [updateSearchModel]);

  useEffect(() => {
    const searchFilters = getFiltersFromStorage();
    if (searchFilters?.searchText) {
      const fuzzy = searchFilters?.fuzzy ?? false;
      setIsFuzzySearch(fuzzy);
      setSearchText(searchFilters?.searchText);
      updateSearchModel(searchFilters?.searchText, fuzzy);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const onSearchTextChange = (e: InputChangeEvent) => {
    setSearchText(e.value);
    debouncedSetSearchModel?.(e.value, isFuzzySearch);
    updateSearchTextInStorage(e.value);
  };

  const onFuzzySwitchChange = (e: SwitchChangeEvent) => {
    setIsFuzzySearch(e.value);
    debouncedSetSearchModel?.(searchText, e.value);
    updateFuzzySearchInStorage(e.value);
  };

  useEffect(() => {
    if (clear) setSearchText('');
  }, [clear]);

  return (
    <PhlexInput
      data-testid={'axon-input-search-text'}
      label={t('SearchFilters.SearchText')}
      name="searchText"
      width="fullwidth"
      showSearchIcon={true}
      value={searchText}
      onChange={onSearchTextChange}
      switchLabel="Fuzzy Search"
      switchChecked={isFuzzySearch}
      onSwitchChange={onFuzzySwitchChange}
    />
  );
};

export default TextFilterComponent;
