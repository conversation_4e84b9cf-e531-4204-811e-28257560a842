{"name": "hacomms", "version": "1.0.0", "private": true, "dependencies": {"@hookform/resolvers": "^3.4.0", "@progress/kendo-react-excel-export": "^6.1.1", "@reduxjs/toolkit": "^1.9.7", "axon-core-ui-shared": "^1.1.112-dev", "axon-hacomms-api-sdk": "^1.0.596-dev", "history": "^5.3.0", "i18next": "^22.5.1", "material-icons": "^1.13.12", "moment": "^2.30.1", "phlex-core-ui": "1.1.761", "process": "^0.11.10", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "7.43.7", "react-i18next": "12.2.0", "react-is": "^18.3.1", "react-redux": "^8.1.3", "react-router-dom": "6.26.2", "react-toastify": "^10.0.5", "redux": "^4.2.1", "redux-persist": "^6.0.0", "styled-components": "^6.1.14", "url-loader": "^4.1.1", "yup": "^1.4.0"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/plugin-transform-runtime": "^7.26.10", "@babel/preset-env": "^7.26.9", "@babel/preset-react": "^7.26.3", "@babel/preset-typescript": "^7.27.0", "@babel/runtime": "^7.27.0", "@progress/kendo-data-query": "^1.7.0", "@progress/kendo-drawing": "^1.20.1", "@progress/kendo-licensing": "^1.5.1", "@progress/kendo-react-animation": "^5.12.0", "@progress/kendo-react-buttons": "^5.12.0", "@progress/kendo-react-data-tools": "^5.19.0", "@progress/kendo-react-dropdowns": "^5.19.0", "@progress/kendo-react-grid": "^5.19.0", "@progress/kendo-react-inputs": "^5.12.0", "@progress/kendo-react-intl": "^5.19.0", "@progress/kendo-react-treeview": "^5.19.0", "@progress/kendo-theme-default": "6.2.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^14.3.1", "@testing-library/user-event": "^14.5.2", "@types/copy-webpack-plugin": "^10.1.0", "@types/enzyme": "3.1.10", "@types/fork-ts-checker-webpack-plugin": "^0.4.5", "@types/jest": "^29.5.12", "@types/lodash": "^4.17.4", "@types/react": "^18.3.2", "@types/react-dom": "^18.3.0", "@types/react-redux": "^7.1.33", "@types/react-router-dom": "^5.3.3", "@types/react-toastify": "^4.1.0", "@types/redux": "^3.6.0", "@types/styled-components": "^5.1.34", "@types/webpack": "^5.28.5", "@types/yup": "^0.32.0", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "babel-jest": "^29.7.0", "babel-loader": "^9.1.3", "copy-webpack-plugin": "^11.0.0", "css-loader": "^6.11.0", "dotenv": "^16.4.5", "dotenv-webpack": "^8.1.0", "enzyme": "^3.11.0", "eslint": "^8.57.0", "eslint-config-prettier": "^8.10.0", "eslint-plugin-jest-dom": "^4.0.3", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-testing-library": "^5.11.1", "eslint-webpack-plugin": "^4.1.0", "fork-ts-checker-webpack-plugin": "^8.0.0", "html-webpack-plugin": "^5.6.0", "jest": "29.5.0", "jest-junit": "^15.0.0", "jest-localstorage-mock": "^2.4.26", "prettier": "^2.8.8", "style-loader": "^3.3.4", "ts-jest": "29.0.5", "ts-node": "^10.9.2", "tsconfig-paths-webpack-plugin": "^4.1.0", "typescript": "^5.4.5", "webpack": "^5.91.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^5.2.1", "webpack-shell-plugin-next": "^2.3.1"}, "overrides": {"nanoid": "3.3.8", "glob": "^10.4.5"}, "jest-junit": {"outputDirectory": "/testResults", "outputName": "test-report.xml"}, "scripts": {"start": "bash ./config/generate-ssl.sh && webpack serve --config webpack.dev.config.ts", "build:local": "webpack --config webpack.prod.config.ts --watch", "build": "webpack --config webpack.prod.config.ts", "test": "jest --passWithNoTests", "test:ci": "jest --ci --reporters=default --reporters=jest-junit --passWithNoTests", "lint": "eslint ./src", "lint-fix": "eslint ./src --fix", "vs-code-launch": "npm install && npm start"}}