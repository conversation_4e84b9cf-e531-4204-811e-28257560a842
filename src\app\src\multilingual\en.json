{"Forms": {"RequiredField": "{{field}} is required", "RequiredShortText": "Required", "FieldAlreadyInUse": "{{field}} already in use", "InsertMaxCharacters": "Maximum {{number}} characters", "InsertMax150Characters": "Maximum 150 characters", "InsertAlphanumeric": "Alphanumeric characters only", "InvalidUrl": "Please enter a valid URL", "InvalidEmail": "Please enter a valid email address", "SafeCharacters": "Please use safe characters only", "SaveButton": "Save", "AddButton": "Add", "UpdateButton": "Update", "CancelButton": "Cancel", "DeleteButton": "Delete", "ContinueButton": "Continue", "CompleteButton": "Complete", "ReinstateButton": "Reinstate", "CreateButton": "Create", "ActiveLabel": "Active", "InActiveLabel": "Inactive", "AddProductExtensionButton": "Add Extension", "UnsavedChangesMessage": "There are unsaved changes! Are you sure you want to leave?", "DuplicateProductExtension": "Duplicate product extensions are not allowed."}, "Common": {"TagsLabel": "Tags", "RouteOfAdminLabel": "Routes of Administration", "DrugSubstancesLabel": "Drug Substances", "ProductCode": "Product code", "DosageFormLabel": "Dosage Form", "NameField": "Name", "SelectDosageForm": "Select dosage form:", "NotAssigned": "Not Assigned", "GenericProductName": "NONE", "GeneralGuidance": "General Guidance", "ProductTypesLabel": "Product Types", "Columns": "Columns", "ModifyButton": "Modify", "FirstPage": "First Page", "PreviousPage": "Previous Page", "NextPage": "Next Page", "LastPage": "Last Page", "DeleteText": "Delete", "NotApplicable": "N/A", "ClearAllFitlers": "Clear All Filters", "CreatedDateField": "Created Date", "CreatedOnField": "Created On {{createdOn}} by {{createdBy}}", "CreatedByField": "Created By", "LastUpdatedDateField": "Last Modified Date", "LastUpdatedOnField": "Updated On {{lastUpdatedOn}} by {{lastUpdatedBy}}", "LastUpdatedByField": "Last Modified By", "AddTooltipLabel": "Add", "DeleteTooltipLabel": "Delete", "SaveTooltipLabel": "Save", "UndoTooltipLabel": "Undo", "DatePlaceholder": "day/month/year", "DrugSubstanceCode": "Code", "DrugSubstanceName": "INN", "LoadMoreButtonLabel": "Load more", "LoadCommentsButtonLabel": "Load {{count}} comments of {{remaining}} remaining", "LoadRemainingCommentsButtonLabel": "Load remaining {{count}} comment(s)", "LoadCommentsButtonTitle": "<PERSON>ad comments"}, "ManageSubstance": {"Substance": "Drug substance", "CodeLabel": "Code", "NameLabel": "INN", "DescriptionLabel": "Description", "DeleteHeading": "Delete Drug Substance", "DeleteDescription": "Are you sure you wish to delete drug substance '{{substanceCode}}'?", "DeleteSucceeded": "Drug substance deleted successfully!", "DeleteFailedErrorMessage": "An error occurred - drug substance was not deleted.", "CreateSucceeded": "Drug substance created successfully!", "CreateFailedErrorMessage": "An error occurred - drug substance was not saved.", "UpdateSucceeded": "Drug substance updated successfully!"}, "SubstanceTable": {"Title": "Drug Substances", "PagerLabel": "Drug Substances", "AddSubstanceButton": "Add Drug Substance", "AddModalHeading": "Add New Drug Substance", "UpdateModalHeading": "Update Drug Substance", "CodeField": "Code", "NameField": "INN"}, "RoutesOfAdministrationTable": {"Title": "Routes Of Administration", "PagerLabel": "Routes Of Administration", "AddButton": "Add Route Of Administration", "AddModalHeading": "Add New Route Of Administration", "UpdateModalHeading": "Update Route Of Administration"}, "ManageRouteOfAdministration": {"RouteOfAdministration": "Route Of Administration", "NameLabel": "Name", "DescriptionLabel": "Description", "DeleteHeading": "Delete Route Of Administration", "DeleteDescription": "Are you sure you wish to delete Route Of Administration '{{routeOfAdministrationName}}'?", "DeleteSucceeded": "Route Of Administration deleted successfully!", "DeleteFailedErrorMessage": "An error occurred - route of administration was not deleted.", "CreateSucceeded": "Route Of Administration created successfully!", "CreateFailedErrorMessage": "An error occurred - route of administration was not saved.", "UpdateSucceeded": "Route Of Administration updated successfully!"}, "DosageFormsTable": {"Title": "Dosage Forms", "PagerLabel": "Dosage Forms", "AddButton": "Add Dosage Form", "AddModalHeading": "Add New Dosage Form", "UpdateModalHeading": "Update Dosage Form"}, "ManageDosageForms": {"DosageForm": "Dosage Form", "NameLabel": "Name", "DescriptionLabel": "Description", "DeleteHeading": "Delete Dosage Form", "DeleteDescription": "Are you sure you wish to delete Dosage Form '{{dosageFormName}}'?", "DeleteSucceeded": "Dosage form deleted successfully!", "DeleteFailedErrorMessage": "An error occurred - dosage form was not deleted.", "CreateSucceeded": "Dosage Form created successfully!", "CreateFailedErrorMessage": "An error occurred - dosage form was not saved.", "UpdateSucceeded": "Dosage Form updated successfully!"}, "TagsTable": {"Title": "Tags", "PagerLabel": "Tags", "AddButton": "Add Tag", "AddModalHeading": "Add New Tag", "UpdateModalHeading": "Update Tag"}, "ManageTags": {"DescriptionLabel": "Description", "NameLabel": "Name", "DeleteHeading": "Delete Tag", "DeleteDescription": "Are you sure you wish to delete Tag '{{tagName}}'?", "DeleteSucceeded": "Tag deleted successfully!", "DeleteFailedErrorMessage": "An error occurred - tag was not deleted.", "CreateSucceeded": "Tag created successfully!", "CreateFailedErrorMessage": "An error occurred - tag was not saved.", "UpdateSucceeded": "Tag updated successfully!"}, "ManageProduct": {"Product": "Product", "CreateProduct": "Create product", "ProductNameLabel": "Product Name", "DrugSubstancesLabel": "Drug Substances (API)", "DrugSubstancesField": "Drug Substances (API) field", "ProductDetails": "Product Details", "CreateSucceeded": "Product created successfully!", "UpdateSucceeded": "Product updated successfully!", "ProductExtensions": "Product Extensions", "CreateFailedErrorMessage": "An error occurred - drug product was not saved.", "ProductDisableWarning": "Drug product and all its mappings will be disabled.", "DuplicateDrugSubstancesErrorMessage": "A product with these substances already exists.", "AssociateProductWithCommentWarningMessage": "This product is associated with at least one comment. You cannot modify \"Drug Substances\" field.", "AssociateExtensionWithCommentWarningMessage": "This product extension has comments associated with it. The \"Dosage Form\" and \"Routes of Administration\" fields cannot be modified nor delete the entire extension.", "InvalidProductTypeCombination": "Not Categorized cannot be in combination with other product types.", "DeleteSucceededMessage": "Product deleted successfully!", "NumberOfUpdatedComments": "Number of updated comments: "}, "ProductExtension": {"ProductCodeLabel": "Product Code", "DosageFormLabel": "Dosage Form", "SelectDosageForm": "Select dosage form:", "RouteOfAdminLabel": "Route of Administration", "AllInactiveError": "At least one product extension must be active"}, "ProductTable": {"Title": "Products", "PagerLabel": "products", "AddProductButton": "Add Product", "AddModalHeading": "Add new product", "UpdateModalHeading": "Update product", "ProductField": "Product Name", "SubstancesField": "Drug Substances (API)", "ProductCodeField": "Product Code", "RouteOfAdministrationField": "Routes of Administration", "DosageFormField": "Dosage Form", "IsActiveField": "Product Status"}, "CommunicationTable": {"Title": "Communications", "PagerLabel": "communications", "AddCommunicationButton": "Add Communication", "AddModalHeading": "Add new communication", "UpdateModalHeading": "Update communication", "SubjectField": "Subject", "CountryField": "Country", "ProductField": "Product", "DateOfCommunicationField": "Date of Communication", "OpenTabField": "Open", "CompletedTabField": "Completed"}, "Nav": {"Home": "Home", "Manage": "Manage", "ManageSubstances": "Manage Drug Substances", "ManageTags": "Manage Tags", "ManageRoutesOfAdministration": "Manage Routes Of Administration", "ManageDosageForms": "Manage Dosage Forms", "ManageProducts": "Manage Products", "ManageCommunications": "Manage Communications", "Search": "Search", "Communications": "Communications", "Comments": "Comments"}, "Table": {"Active": "Active", "Inactive": "Inactive"}, "ManageCommunication": {"Communication": "Communication", "CreateCommunication": "Create Communication", "Overview.ApplicationNumberLabel": "Application Number(s)", "Overview.SubmissionNumberLabel": "Submission Number(s) for Application Number", "Overview.CountryLabel": "Country", "Overview.SubmissionTypeLabel": "Submission Type", "Overview.SubjectLabel": "Subject", "DateOfCommunicationLabel": "Date", "Overview.SelectCountry": "Select Country...", "Overview.SelectSubmissionType": "Select Submission Type...", "Overview.SelectDateOfCommunication": "Select Date of Communication...", "CreateFailedErrorMessage": "An error occurred - communication was not saved.", "DeleteFailedErrorMessage": "An error occurred - communication was not deleted.", "CompleteFailedErrorMessage": "An error occurred - communication was not completed.", "ReinstateFailedErrorMessage": "An error occurred - communication was not reinstated.", "CreateCommunicationSucceeded": "Communication created successfully!", "UpdateCommunicationSucceeded": "Communication updated successfully!", "DeleteCommunicationSucceeded": "Communication deleted successfully!", "CompleteSucceeded": "Communication completed successfully!", "ReinstateSucceeded": "Communication reinstated successfully!", "CreateCommentSucceeded": "Comment created successfully!", "UpdateCommentSucceeded": "Comment updated successfully!", "DeleteCommentSucceeded": "Comment deleted successfully!", "DeleteProductSucceeded": "Product and all associated comments deleted successfully from communication!", "CreateUpdateValidationErrorsMessage": "Please fill the required fields!", "CommentsHeading": "Comments", "GeneralGuidanceSubHeading": "Non Product Specific", "GeneralGuidance": "General Guidance", "DrugProducts": "Drug Products", "Description": "HA Comment", "SelectPCID": "Select Product code", "AddComment": "Add Comment", "CopyComment": "Copy Comment", "ProductsHeading": "Products", "DrugSubstancesLabel": "Drug Substances (API)", "AddProductTitle": "Add product to the communication", "DeleteProductTooltip": "Remove product from communication", "ProductCommentsRequired": "The communication must have at least 1 comment", "ProductExtension": "Product Extension", "SelectProductExtension": "Select Product Extension", "ProductCodeField": "Product Code", "RouteOfAdministrationField": "Routes of Administration", "DosageFormField": "Dosage Form", "QuestionIncludedLabel": "Is Sponsor Question Included?", "BirdsLinkToBISAMPLabel": "RIM link to scientific advice/meeting package", "BirdsLinkToBIResponseLabel": "RIM link to response", "CommentOnlyLabel": "Comment only", "RequiredFieldsWarning": "Please complete all required fields", "NoEmptyValuesValidationMessage": "Empty values are not allowed.", "DuplicateValuesValidationMessage": "Duplicate values are not allowed.", "ChipListNoItemsMessage": "No items", "OpenUrlTooltip": "Open", "Question": "Sponsor Question", "Answer": "HA Response", "UrlFormatPlaceholder": "http(s)://-Enter Source URL", "Edit": "Edit", "Save": "Save", "Cancel": "Cancel", "CommentCreatedOnField": "Created on {{createdOn}} by {{createdBy}}. ", "CommentLastUpdatedOnField": "Updated on {{lastUpdatedOn}} by {{lastUpdatedBy}}."}, "CommunicationModals": {"DeleteProductHeading": "Delete Product", "DeleteProductDescription": "Are you sure you wish to delete this product? All comments associated with it will be lost after saving your changes.", "DeleteCommentHeading": "Delete Comment", "DeleteCommentDescription": "Are you sure you wish to delete this comment?", "DeleteCommunicationHeading": "Delete Communication", "DeleteCommunicationDescription": "Are you sure you wish to delete this communication? All associated products and comments will be deleted.", "CompleteCommunicationHeading": "Complete Communication", "CompleteCommunicationDescription": "Are you sure you wish to complete this communication?", "ReinstateCommunicationHeading": "Reinstate Communication", "ReinstateCommunicationDescription": "Are you sure you wish to reinstate this communication?", "DeleteProductExtensionHeading": "Delete Product Extension", "DeleteProductExtensionDescription": "Are you sure you wish to delete this product extension?"}, "NotFound": {"Heading": "Oops!", "Subheading": "404", "PageMissing": "We can't seem to find the page you were looking for.", "Suggestions": "You may have typed the address incorrectly or visited an outdated link.", "BackButton": "Back to Search"}, "ForbiddenPage": {"Heading": "Oops!", "Subheading": "401 Forbidden", "AccessMissing": "Seems like you have no access to page you were looking for.", "Suggestions": "You may have typed the address incorrectly or your account access expired. Please contact the administrator.", "BackButton": "Back to Search"}, "AppError": {"Heading": "Oops!", "Subheading": "Error", "ErrorPage": "An error has occured.", "Suggestions": "Please contact the administrator.", "BackButton": "Back to Search"}, "ProductModals": {"Heading": "Add a Product", "SelectProduct": "Select product:", "ProductExistsValidationError": "{{product}} has already been added to the communication.", "DeleteProductHeading": "Delete Product", "DeleteProductDescription": "Are you sure you wish to delete this product?"}, "SearchFilters": {"DrugProduct": "Drug Product", "SubmissionNumber": "Submission Number", "SubmissionType": "Submission Type", "ApplicationNumber": "Application Number", "ProductCode": "Product Code", "DosageForm": "Dosage Form", "Country": "Country", "RouteOfAdministration": "Route of Administration", "DrugSubstance": "Drug Substances (API)", "Tag": "Tags", "ProductType": "Product Type", "SearchText": "Search Text", "QuestionFilterOptions.Label": "Is Sponsor Question Included?", "QuestionFilterOptions.All": "All Comments", "QuestionFilterOptions.CommentsOnly": "HA Comments only", "QuestionFilterOptions.QuestionIncluded": "Sponsor Questions only", "GeneralGuidanceFilterOptions.Label": "General Guidance", "GeneralGuidanceFilterOptions.All": "All Comments", "GeneralGuidanceFilterOptions.GeneralGuidance": "General Guidance", "GeneralGuidanceFilterOptions.NongeneralGuidance": "Nongeneral Guidance"}, "SearchExport": {"SearchCriteria": "Search Criteria: Max exported comments are ", "AdjustSearchCriteria": "If comments exceed this number, adjust your search criteria to export less comments.", "ExportButton": "Export to Excel", "ExportCompleted": "Export completed."}, "SearchTable": {"Title": "Comments", "ProductName": "Product Name", "ProductCode": "Product Codes", "Country": "Country", "Comments": "Comments", "DateOfCommunication": "Date of Communication", "DrugSubstances": "Drug Substances (API)", "Tags": "Tags", "DosageForm": "Dosage Forms", "RoutesOfAdministration": "Routes of Administration", "SubmissionNumber": "Submission Number", "ApplicationNumber": "Application Number", "SubmissionType": "Submission Type", "QuestionIncluded": "Is Sponsor Question Included?", "GeneralGuidance": "General Guidance", "BirdsLinkToBISAMP": "RIM link to scientific advice/meeting package", "BirdsLinkToBIResponse": "RIM link to response", "SearchTablePager": "comments", "ViewDetails": "View Details", "Question": "Sponsor Question", "Response": "HA Response", "ProductTypes": "Product Types"}, "SearchModal": {"Heading": "Modify Table Columns", "SelectColumns": "Select columns:"}, "SearchCommentDetails": {"CommentLabel": "Comment", "QuestionLabel": "Sponsor Question", "AnswerLabel": "HA Response", "ProductExtensionLabel": "Product Extension", "DosageFormLabel": "Dosage Form", "RouteOfAdministrationLabel": "Route of Administration", "DrugSubstancesLabel": "Drug Substances (API)", "TagsLabel": "Tags", "BirdsLinkToBISAMPLabel": "RIM link to scientific advice/meeting package", "BirdsLinkToBIResponseLabel": "RIM link to response", "CreatedOnField": "Created on {{createdOn}} by {{createdBy}}. ", "LastUpdatedOnField": "Updated on {{lastUpdatedOn}} by {{lastUpdatedBy}}."}, "SearchOtherRelatedComments": {"CommentLabel": "Other comments from the communication related to "}, "SearchCommunicationOverview": {"OverviewLabel": "Communication Overview", "SubjectLabel": "Subject", "ApplicationNumberLabel": "Application Number", "SubmissionNumberLabel": "Submission Number", "CountryLabel": "Country", "SubmissionTypeLabel": "Submission Type", "DateOfCommunicationLabel": "Date of Communication", "ProductsLabel": "Product(s)", "GeneralGuidanceCommentsLabel": "Non Product Specific"}, "SearchCommunicationOtherCommentsForProduct": {"SlideoutHeading": "Comments for {{productName}}"}, "SearchCommentView": {"ProductLabel": "Product"}}