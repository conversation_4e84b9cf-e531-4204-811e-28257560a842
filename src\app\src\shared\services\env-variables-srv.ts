/* eslint-disable @typescript-eslint/no-explicit-any */

type EnvironmentVariable =
  | 'AXON_CORE_API_BASE_URL'
  | 'HACOMMS_API_BASE_URL'
  | 'LOCAL_PERMISSIONS'
  | 'LOCAL_TOKEN'
  | 'IS_LOCAL'
  | 'AXON_SHARED_LOCAL_TOKEN'
  | 'AXON_SHARED_LOCAL_COOKIE';

export default {
  getVariable: (variable: EnvironmentVariable): string => {
    if (!(window as any)._env_) return variable;
    return (window as any)._env_[variable];
  },
};
