import React, { FC, Fragment } from 'react';
import { PhlexButton, PhlexLoader } from 'phlex-core-ui';
import { useTranslation } from 'react-i18next';
import { ManageProductPageActionsProps } from './product-form-types';

const ManageProductPageActionsComponent: FC<ManageProductPageActionsProps> = ({
  isNewProduct,
  isaAssociatedToComments,
  methods,
  handleDeleteProduct,
  handleAddProductExtension,
  data,
}) => {
  const { t } = useTranslation();

  return (
    <Fragment>
      <PhlexButton
        color="tertiary"
        label={t('Forms.DeleteButton')}
        onClick={(e) => {
          e.preventDefault();
          handleDeleteProduct(e);
        }}
        hidden={isNewProduct || isaAssociatedToComments}
        disabled={methods.formState.isSubmitting}
      />
      <PhlexButton
        label={t('Forms.AddProductExtensionButton')}
        onClick={() => {
          return handleAddProductExtension();
        }}
        type="button"
        icon="add"
        disabled={data.product && !data.product.isActive}
      />
      {methods.formState.isSubmitting ? <PhlexLoader position="center" /> : <PhlexButton label={t('Forms.SaveButton')} type="submit" />}
    </Fragment>
  );
};

export default ManageProductPageActionsComponent;
