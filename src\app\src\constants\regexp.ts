export const REGULAR_EXPRESSIONS = {
  IllegalCharacters: /^[^<>]*$/,
  AlphaNumerical: /^[a-zA-Z0-9]+$/,
};

export const FIELD_LENGTH_LIMITS = {
  // Drug Substance fields
  SUBSTANCE_CODE: 50,
  SUBSTANCE_NAME: 100,
  SUBSTANCE_DESCRIPTION: 500,

  // Tag fields
  TAG_NAME: 50,
  TAG_DESCRIPTION: 500,

  // Route of Administration fields
  ROUTE_OF_ADMINISTRATION_NAME: 50,

  // Dosage Form fields
  DOSAGE_FORM_NAME: 50,

  // Product fields (existing validation reference)
  PRODUCT_NAME: 30,
  PRODUCT_CODE_IDENTIFIER: 15,
};
