import { createGlobalStyle } from 'styled-components';

const GlobalStyle = createGlobalStyle`
  #root,
  html,
  body {
    background-color: ${(props) => props.theme.colors.backgroundSec};
    color: ${(props) => props.theme.colors.textPri};
    cursor: default;
    font-family: 'Segoe UI', sans-serif;
    line-height: 1;
    margin: 0;
    min-height: 100%;
  }

  /* Scrollbars */
  ::-webkit-scrollbar {
    width: ${(props) => props.theme.spaceS};
  }
  ::-webkit-scrollbar-thumb {
    background-color: ${(props) => props.theme.colors.scrollbar};
    border-radius: ${(props) => props.theme.radiusRound};
  }
  ::-webkit-scrollbar-thumb:hover {
    background-color: ${(props) => props.theme.colors.scrollbarHover};
  }

  /* Kendo dropdowns */

  .k-animation-container {
    z-index: 10005 !important;
  }

  /* Toastify */
  
  .Toastify__toast {
    box-shadow: ${(props) => props.theme.shadowHover};
    font-family: inherit;
    line-height: ${(props) => props.theme.lineHeightHeading};
  }
  .Toastify__close-button {
    color: inherit;
    float: right;
  }
  .Toastify__close-button--default {
    opacity: 0.7;
  }
  .Toastify__toast--success {
    background: ${(props) => props.theme.colors.feedbackGreen};
    color: ${(props) => props.theme.colors.textLight};
  }
  .Toastify__toast--warning {
    background: ${(props) => props.theme.colors.feedbackAmber};
    color: ${(props) => props.theme.colors.textDark};
  }
  .Toastify__toast--error {
    background: ${(props) => props.theme.colors.feedbackRed};
    color: ${(props) => props.theme.colors.textLight};
  }
  .Toastify__toast--default,
  .Toastify__toast--info {
    background: ${(props) => props.theme.colors.buttonPri};
    color: ${(props) => props.theme.colors.textLight};
  }

  /*Scroll view*/
.scrollview-inactive  {
    visibility: hidden;
    opacity: 0;
    max-height: 0 !important;
 }
.scrollview-active { 
    transition: visibility 1s, opacity 1s;
    visibility: visible;
    opacity: 1;
}
`;

export default GlobalStyle;
