import Axios, { AxiosPromise } from 'axios';
import appConfig from '../../../shared/config';
import { sharedVariables } from 'shared/variables';

const apiCoreUrl = appConfig.AXON_CORE_API_BASE_URL;
const { appCodeName } = sharedVariables;

const getUserPermissions = (permissions: string[], tenant: string): AxiosPromise<any> => {
  const requestUrl = `${apiCoreUrl}/v1/Users/<USER>/Validate`;
  const request = { permissions, appCodeName, orgCodeName: tenant };
  return Axios.post<{ id: string }>(requestUrl, request);
};

const permissionApi = {
  getUserPermissions,
};

export { permissionApi };
