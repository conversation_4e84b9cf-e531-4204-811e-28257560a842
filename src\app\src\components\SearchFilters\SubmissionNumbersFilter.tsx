import { MultiselectData } from 'components/shared/types/types';
import { PhlexMultiSelect } from 'phlex-core-ui';
import React, { FC, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { submissionApiService } from 'shared/api';
import { SUBMISSION_NUMBER_KEY, SearchFiltersProps } from './search-filter-types';
import { SearchCommentCommandRequest } from 'axon-hacomms-api-sdk';
import { getFiltersFromStorage, updateFiltersInStorage } from './search-filter-service';

const SubmissionNumbersFilterComponent: FC<SearchFiltersProps> = (props: SearchFiltersProps) => {
  const { tenant, setSearchModel, clear } = props;
  const { t } = useTranslation();
  const [selectedSubmissionNumbers, setSelectedSubmissionNumbers] = useState<MultiselectData>([]);

  const fetchSubmissionNumbers = async (): Promise<MultiselectData> => {
    const response = await submissionApiService.getAllSubmissionNumbers(tenant);
    const data = response.data.data?.map((number: string) => ({ value: number ?? '', label: number ?? '' })) ?? [];
    return data;
  };

  const updateSearchModel = (data: MultiselectData) => {
    setSearchModel((request: SearchCommentCommandRequest) => ({
      ...request,
      submissionNumbers: data.map((x) => x.label),
      skip: 0,
      take: 10,
    }));
  };

  const onSubmissionNumberFilterChange = (data: MultiselectData) => {
    setSelectedSubmissionNumbers(data);
    updateFiltersInStorage(data, SUBMISSION_NUMBER_KEY);
    updateSearchModel(data);
  };

  useEffect(() => {
    const searchFilters = getFiltersFromStorage();
    if (searchFilters?.filters[SUBMISSION_NUMBER_KEY] && searchFilters.filters[SUBMISSION_NUMBER_KEY].length > 0) {
      const filterData = searchFilters.filters[SUBMISSION_NUMBER_KEY];
      setSelectedSubmissionNumbers(filterData);
      updateSearchModel(filterData);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (clear) setSelectedSubmissionNumbers([]);
  }, [clear]);

  return (
    <PhlexMultiSelect
      data-testid={'axon-input-search-filter-submission-number'}
      name={`submissionNumberSearchFilter`}
      label={t('SearchFilters.SubmissionNumber')}
      value={selectedSubmissionNumbers}
      defaultValues={selectedSubmissionNumbers}
      width="fullwidth"
      compactMode={true}
      fetchData={() => fetchSubmissionNumbers()}
      onlyFetchOnOpen={true}
      onChange={onSubmissionNumberFilterChange}
    />
  );
};

export default SubmissionNumbersFilterComponent;
