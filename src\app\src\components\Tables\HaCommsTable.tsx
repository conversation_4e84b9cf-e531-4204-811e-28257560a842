import React, { ComponentType, FunctionComponent, useMemo } from 'react';
import TablePager from '../TablePager/TablePager';
import { TableColumn } from 'phlex-core-ui/build/src/controls/PhlexTable/PhlexTable.types';
import {
  GridDetailRowProps,
  GridExpandChangeEvent,
  GridPageChangeEvent,
  GridRowClickEvent,
  GridSortChangeEvent,
} from '@progress/kendo-react-grid';
import { SortDescriptor, CompositeFilterDescriptor } from '@progress/kendo-data-query';
import { debounce } from 'lodash';
import { Table } from './HaCommsTable.styles';
import { SearchTable } from 'components/Tables/HaCommsSearchTable.styles';

type TableProps = {
  data: any[];
  detail?: null | ComponentType<GridDetailRowProps>;
  uniqueIdField: string;
  onPageChange?: (event: GridPageChangeEvent) => void;
  onFilterChange?: (event: CompositeFilterDescriptor) => void;
  onSortChange?: (e: GridSortChangeEvent) => void;
  filterable?: boolean;
  sortable?: boolean;
  pageable?: boolean;
  skip?: number;
  take?: number;
  total?: number;
  isLoading?: boolean;
  columns: TableColumn[];
  nameOfRows: string;
  sort?: SortDescriptor[];
  onRowClick: ((event: GridRowClickEvent) => void) | undefined;
  expandField?: string;
  onExpandChange?: (event: GridExpandChangeEvent) => void;
  filter?: CompositeFilterDescriptor;
  setFilter?: React.Dispatch<React.SetStateAction<CompositeFilterDescriptor>>;
  isSearchTable?: boolean;
  height?: number;
};

const HaCommsTable: FunctionComponent<TableProps> = ({
  data,
  detail,
  uniqueIdField,
  onPageChange,
  onFilterChange,
  onSortChange,
  filterable,
  sortable,
  pageable,
  skip,
  take,
  total,
  columns,
  nameOfRows,
  sort,
  onRowClick,
  expandField,
  onExpandChange,
  filter,
  setFilter,
  isSearchTable,
  height,
}: TableProps) => {
  const debouncedFilterChanged = useMemo(() => {
    return onFilterChange ? debounce(onFilterChange, 500) : null;
  }, [onFilterChange]);

  const tablePager = (p: any) => <TablePager {...p} nameOfRows={nameOfRows} />;
  const customFilterOperators = {
    text: [{ text: 'grid.filterContainsOperator', operator: 'contains' }],
    numeric: [{ text: 'grid.filterGtOperator', operator: 'gt' }],
    date: [{ text: 'grid.filterAfterOperator', operator: 'gt' }],
  };

  return (
    <React.Fragment>
      {(isSearchTable && (
        <SearchTable
          isLoading={false}
          data={data}
          columns={columns}
          uniqueIDField={uniqueIdField}
          filterable={filterable}
          filterOperators={customFilterOperators}
          sortable={sortable}
          sort={sort}
          onSortChange={onSortChange}
          skip={skip}
          take={take}
          total={total}
          pageable={pageable}
          onFilterChange={(event: any) => {
            setFilter?.(event.filter);
            debouncedFilterChanged?.(event.filter);
          }}
          onPageChange={onPageChange}
          pager={tablePager}
          filter={filter}
          onRowClick={onRowClick}
          detail={detail}
          expandField={expandField}
          onExpandChange={onExpandChange}
          style={{ height: height }}
        ></SearchTable>
      )) ?? (
        <Table
          isLoading={false}
          data={data}
          columns={columns}
          uniqueIDField={uniqueIdField}
          filterable={filterable}
          filterOperators={customFilterOperators}
          sortable={sortable}
          sort={sort}
          onSortChange={onSortChange}
          skip={skip}
          take={take}
          total={total}
          pageable={pageable}
          onFilterChange={(event: any) => {
            setFilter?.(event.filter);
            debouncedFilterChanged?.(event.filter);
          }}
          onPageChange={onPageChange}
          pager={tablePager}
          filter={filter}
          onRowClick={onRowClick}
          detail={detail}
          expandField={expandField}
          onExpandChange={onExpandChange}
        ></Table>
      )}
    </React.Fragment>
  );
};

export default HaCommsTable;
