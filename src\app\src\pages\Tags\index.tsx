import { CompositeFilterDescriptor, SortDescriptor } from '@progress/kendo-data-query';
import { GridCellProps, GridRowClickEvent, GridSortChangeEvent } from '@progress/kendo-react-grid';
import { TagModel, TagPagedListModel } from 'axon-hacomms-api-sdk';
import { MainContent, ClearFilters, TableIcon, SearchInput } from 'components/Content/Content.styles';
import documentsApi from 'components/shared/api/documentsApi';
import useDataGridPagination, { Pagination } from 'components/shared/hooks/useDataGridPagination';
import TagModal from 'components/Modals/TagModal';
import HaCommsTable from 'components/Tables/HaCommsTable';
import { TableColumn } from 'phlex-core-ui/build/src/controls/PhlexTable/PhlexTable.types';
import React, { Fragment, FunctionComponent, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { toFilterDateFormat, toDateTimeFormat } from 'shared/services/date-time-format';
import { PhlexBreadcrumb, PhlexButton, PhlexDatePicker, PhlexLayout, PhlexLoader } from 'phlex-core-ui';
import { DateFormats, tableFilters } from 'shared/consts';
import { withRestrictedPermissions } from 'hoc';
import { Permissions } from 'constants/permissions';
import sharedComponents from 'pages/index.styles';

const initialSort: Array<SortDescriptor> = [{ field: tableFilters.NAME, dir: 'asc' }];

const { HaCommsPageTopBar } = sharedComponents;

const initialFilter: CompositeFilterDescriptor = {
  logic: 'and',
  filters: [
    {
      field: tableFilters.NAME,
      operator: 'contains',
      value: '',
    },
    {
      field: tableFilters.CREATED_DATE,
      operator: 'contains',
      value: '',
    },
    {
      field: tableFilters.CREATED_BY,
      operator: 'contains',
      value: '',
    },
    {
      field: tableFilters.LAST_UPDATED_DATE,
      operator: 'contains',
      value: '',
    },
    {
      field: tableFilters.LAST_UPDATED_BY,
      operator: 'contains',
      value: '',
    },
  ],
};

const { PageActions, FilterPanel } = PhlexLayout;

const Component: FunctionComponent<{ basepath: string }> = ({ basepath }) => {
  const initState = {
    skip: 0,
    data: [] as TagPagedListModel[],
    total: 0,
    take: 20,
    filter: '',
    orderBy: 'name=>asc',
    refresh: false,
    cleanBasePath: basepath,
  } as Pagination<TagPagedListModel>;

  const [pagination, onPageChange, changeFilter, sortChange] = useDataGridPagination<TagPagedListModel>(
    documentsApi.tags.getList,
    initState
  );
  const { t } = useTranslation();

  const breadcrumbs = [
    {
      key: 'manage',
      text: t('Nav.Manage'),
      active: 'false' as const,
    },
    {
      key: 'tag',
      text: t('TagsTable.Title'),
      active: 'true' as const,
    },
  ];

  const cellClick = (e: GridCellProps) => {
    setVisible(true);
    setTag(pagination.data.find((x: TagPagedListModel) => x.id === e.dataItem.id) as TagModel);
  };

  const viewCell = (e: GridCellProps) => {
    return (
      <TableIcon title="View Details" onClick={() => cellClick(e)}>
        chevron_right
      </TableIcon>
    );
  };

  const columns: TableColumn[] = [
    { identifier: tableFilters.NAME, title: t('Common.NameField'), showTooltip: true },
    { identifier: tableFilters.CREATED_DATE, title: t('Common.CreatedDateField'), showTooltip: true },
    { identifier: tableFilters.CREATED_BY, title: t('Common.CreatedByField'), showTooltip: true },
    { identifier: tableFilters.LAST_UPDATED_DATE, title: t('Common.LastUpdatedDateField'), showTooltip: true },
    { identifier: tableFilters.LAST_UPDATED_BY, title: t('Common.LastUpdatedByField'), showTooltip: true },
    { identifier: '', title: '', cell: viewCell, width: '56px' },
  ];

  const [filters, setFilters] = useState(initialFilter);
  const [updateResults, setUpdateResults] = useState(false);
  const [nameFilter, setNameFilter] = useState('');
  const [createdDateFilter, setCreatedDateFilter] = useState<Date | null>(null);
  const [createdByFilter, setCreatedByFilter] = useState('');
  const [lastModifiedDateFilter, setLastModifiedDateFilter] = useState<Date | null>(null);
  const [lastModifiedByFilter, setLastModifiedByFilter] = useState('');
  const [isInitial, setIsInitial] = useState(true);

  const [nameOfRows] = useState(t('TagsTable.PagerLabel'));
  const [sort, setSort] = useState(initialSort);
  const onSortChange = (e: GridSortChangeEvent) => {
    setSort(e.sort);
    sortChange(e.sort);
  };

  const [visible, setVisible] = useState(false);
  const [tag, setTag] = useState<TagModel | undefined>();

  const onClose = () => {
    setVisible(false);
    setTag(undefined);
  };

  const onRowClick = (e: GridRowClickEvent) => {
    setVisible(true);
    setTag(pagination.data.find((x: TagPagedListModel) => x.id === e.dataItem.id) as TagPagedListModel);
  };

  const clearFilters = () => {
    setNameFilter('');
    setCreatedDateFilter(null);
    setCreatedByFilter('');
    setLastModifiedDateFilter(null);
    setLastModifiedByFilter('');
    setUpdateResults(true);
    setFilters(initialFilter);
  };

  useEffect(() => {
    if (updateResults) {
      const timer = setTimeout(() => {
        changeFilter(filters);
        setUpdateResults(false);
      }, 500);
      return () => clearTimeout(timer);
    }
  }, [changeFilter, filters, updateResults]);

  useEffect(() => {
    if (
      nameFilter.length === 0 &&
      createdDateFilter === null &&
      createdByFilter.length === 0 &&
      lastModifiedDateFilter === null &&
      lastModifiedByFilter.length === 0 &&
      isInitial
    ) {
      setIsInitial(false);
      return;
    }

    const filterValues: CompositeFilterDescriptor = {
      logic: 'and',
      filters: [
        {
          field: tableFilters.NAME,
          operator: 'contains',
          value: nameFilter,
        },
        {
          field: tableFilters.CREATED_DATE,
          operator: 'contains',
          value: toFilterDateFormat(createdDateFilter),
        },
        {
          field: tableFilters.CREATED_BY,
          operator: 'contains',
          value: createdByFilter,
        },
        {
          field: tableFilters.LAST_UPDATED_DATE,
          operator: 'contains',
          value: toFilterDateFormat(lastModifiedDateFilter),
        },
        {
          field: tableFilters.LAST_UPDATED_BY,
          operator: 'contains',
          value: lastModifiedByFilter,
        },
      ],
    };

    setUpdateResults(true);
    setFilters(filterValues);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [nameFilter, createdDateFilter, createdByFilter, lastModifiedDateFilter, lastModifiedByFilter]);

  return (
    <Fragment>
      <HaCommsPageTopBar>
        <PhlexBreadcrumb options={breadcrumbs} />
        <PageActions>
          <PhlexButton data-testid="axon-button-tag.add-button" label={t('TagsTable.AddButton')} onClick={() => setVisible(true)} />
        </PageActions>
      </HaCommsPageTopBar>
      <MainContent>
        <FilterPanel>
          <SearchInput name="name" onChange={(e) => setNameFilter(e.value)} value={nameFilter} label={t('Common.NameField')} />
          <PhlexDatePicker
            name="createdDate"
            onChange={(e) => setCreatedDateFilter(e.value)}
            value={createdDateFilter}
            placeholder={t('Common.DatePlaceholder')}
            format={DateFormats.ddMMMyyyy}
            label={t('Common.CreatedDateField')}
          />
          <SearchInput
            name="createdBy"
            onChange={(e) => setCreatedByFilter(e.value)}
            value={createdByFilter}
            label={t('Common.CreatedByField')}
          />
          <PhlexDatePicker
            name="lastModifiedDate"
            onChange={(e) => setLastModifiedDateFilter(e.value)}
            value={lastModifiedDateFilter}
            placeholder={t('Common.DatePlaceholder')}
            format={DateFormats.ddMMMyyyy}
            label={t('Common.LastUpdatedDateField')}
          />
          <SearchInput
            name="lastModifiedBy"
            onChange={(e) => setLastModifiedByFilter(e.value)}
            value={lastModifiedByFilter}
            label={t('Common.LastUpdatedByField')}
          />
          <ClearFilters name="filter_alt_off" clickHandler={clearFilters} tooltip={t('Common.ClearAllFitlers')} />
        </FilterPanel>
        {pagination.isLoading ? (
          <PhlexLoader position="page" />
        ) : (
          <HaCommsTable
            {...pagination}
            data={pagination.data.map((x) => ({
              ...x,
              name: x.name,
              lastUpdatedDate: toDateTimeFormat(x.lastUpdatedDate),
              createdDate: toDateTimeFormat(x.createdDate),
            }))}
            uniqueIdField={tableFilters.NAME}
            filterable={false}
            sortable={true}
            pageable={true}
            onPageChange={onPageChange}
            onSortChange={onSortChange}
            isLoading={pagination.isLoading}
            total={pagination.total}
            columns={columns}
            nameOfRows={nameOfRows}
            sort={sort}
            filter={initialFilter}
            onRowClick={(e: GridRowClickEvent) => onRowClick(e)}
          />
        )}
        {visible && <TagModal onClose={onClose} tag={tag} refreshTable={() => (pagination.refresh = !pagination.refresh)} />}
      </MainContent>
    </Fragment>
  );
};

export default withRestrictedPermissions(Component, [
  Permissions.ViewTag,
  Permissions.EditTag,
  Permissions.CreateTag,
  Permissions.DeleteTag,
]);
