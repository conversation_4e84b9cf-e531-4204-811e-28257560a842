import { TagModel, SearchCommentCommandRequest } from 'axon-hacomms-api-sdk';
import { MultiselectData } from 'components/shared/types/types';
import { PhlexMultiSelect } from 'phlex-core-ui';
import React, { FC, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import tagApiService from 'shared/api/tag-api-service';
import { SearchFiltersProps, TAG_KEY } from './search-filter-types';
import { getFiltersFromStorage, updateFiltersInStorage } from './search-filter-service';

const TagsFilterComponent: FC<SearchFiltersProps> = (props: SearchFiltersProps) => {
  const { tenant, setSearchModel, clear } = props;
  const { t } = useTranslation();
  const [selectedTags, setSelectedTags] = useState<MultiselectData>([]);

  const fetchTag = async (): Promise<MultiselectData> => {
    const response = await tagApiService.getTagsList(tenant);
    const data = response.data.data?.map((item: TagModel) => ({ value: item.id?.toString() ?? '', label: item.name ?? '' })) ?? [];
    return data;
  };

  const updateSearchModel = (data: MultiselectData) => {
    setSearchModel((request: SearchCommentCommandRequest) => ({
      ...request,
      tags: data.map((x) => ({ id: Number(x.value), name: x.label })),
      skip: 0,
      take: 10,
    }));
  };

  const onTagFilterChange = (data: MultiselectData) => {
    setSelectedTags(data);
    updateFiltersInStorage(data, TAG_KEY);
    updateSearchModel(data);
  };

  useEffect(() => {
    const searchFilters = getFiltersFromStorage();
    if (searchFilters?.filters[TAG_KEY] && searchFilters.filters[TAG_KEY].length > 0) {
      const filterData = searchFilters.filters[TAG_KEY];
      setSelectedTags(filterData);
      updateSearchModel(filterData);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (clear) setSelectedTags([]);
  }, [clear]);

  return (
    <PhlexMultiSelect
      data-testid={'axon-input-search-filter-tag'}
      name={`tagSearchFilter`}
      label={t('SearchFilters.Tag')}
      value={selectedTags}
      defaultValues={selectedTags}
      width="fullwidth"
      compactMode={true}
      fetchData={() => fetchTag()}
      onlyFetchOnOpen={true}
      onChange={onTagFilterChange}
    />
  );
};

export default TagsFilterComponent;
