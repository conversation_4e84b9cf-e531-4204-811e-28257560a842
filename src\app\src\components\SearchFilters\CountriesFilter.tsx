import { CountryModel, SearchCommentCommandRequest } from 'axon-hacomms-api-sdk';
import { MultiselectData } from 'components/shared/types/types';
import { PhlexMultiSelect } from 'phlex-core-ui';
import React, { FC, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import countryApiService from 'shared/api/country-api-service';
import { COUNTRY_KEY, SearchFiltersProps } from './search-filter-types';
import { getFiltersFromStorage, updateFiltersInStorage } from './search-filter-service';

const CountriesFilterComponent: FC<SearchFiltersProps> = (props: SearchFiltersProps) => {
  const { tenant, setSearchModel, clear } = props;
  const { t } = useTranslation();
  const [selectedCountries, setSelectedCountries] = useState<MultiselectData>([]);

  const fetchCountries = async (): Promise<MultiselectData> => {
    const response = await countryApiService.getCountryList(tenant);
    const data = response.data.data?.map((item: CountryModel) => ({ value: item.id?.toString() ?? '', label: item.name ?? '' })) ?? [];
    return data;
  };

  const updateSearchModel = (data: MultiselectData) => {
    setSearchModel((request: SearchCommentCommandRequest) => ({
      ...request,
      countries: data.map((x) => ({ id: Number(x.value), name: x.label })),
      skip: 0,
      take: 10,
    }));
  };

  const onCountryFilterChange = (data: MultiselectData) => {
    setSelectedCountries(data);
    updateFiltersInStorage(data, COUNTRY_KEY);
    updateSearchModel(data);
  };

  useEffect(() => {
    const searchFilters = getFiltersFromStorage();
    if (searchFilters?.filters[COUNTRY_KEY] && searchFilters.filters[COUNTRY_KEY].length > 0) {
      const filterData = searchFilters.filters[COUNTRY_KEY];
      setSelectedCountries(filterData);
      updateSearchModel(filterData);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (clear) setSelectedCountries([]);
  }, [clear]);

  return (
    <PhlexMultiSelect
      data-testid={'axon-input-search-filter-country'}
      name={`countrySearchFilter`}
      label={t('SearchFilters.Country')}
      value={selectedCountries}
      defaultValues={selectedCountries}
      width="fullwidth"
      compactMode={true}
      fetchData={() => fetchCountries()}
      onlyFetchOnOpen={true}
      onChange={onCountryFilterChange}
    />
  );
};

export default CountriesFilterComponent;
