import { CommunicationModel, CountryModel, ProductModel, SubmissionTypeModel } from 'axon-hacomms-api-sdk';
import { PagedProps } from 'components/SearchViews/search-view-types';
import { NullableString } from 'components/shared/types/types';
import { GlobalEditModeState } from 'context/CommunicationContext';
import { ChipItemProps } from 'phlex-core-ui/build/src/controls/PhlexChipList/PhlexChipList';
import { IOption } from 'phlex-core-ui/build/src/controls/PhlexMultiSelect/PhlexMultiSelect';
import { Control, FieldArrayWithId, UseFieldArrayUpdate, UseFormReturn } from 'react-hook-form';

export interface ManageCommunicationFormProps {
  applications: ApplicationChipItemProps[];
  subject?: string;
  submissionType?: { id: number; name: string } | null;
  country?: { id: number; name: string | null } | null;
  dateOfCommunication?: Date | null;
  comments?: Comment[];
}

export interface CommunicationOverviewProps {
  methods: UseFormReturn<ManageCommunicationFormProps, any>;
}

export interface CommunicationProductsTabContainerProps {
  methods: UseFormReturn<ManageCommunicationFormProps, any>;
}

export interface ProductTabsProps {
  methods: UseFormReturn<ManageCommunicationFormProps, any>;
}

export interface CommunicationCommentsProps {
  isGeneralGuidance: boolean;
  handleLoadMoreComments: () => void;
  onCommentDeleted: () => void;
  isCommentsLoading: boolean;
  commentsPagedModel: PagedProps;
  isLoadMoreHidden: boolean;
  totalComments: number;
  productTabsCount: number;
  methods: UseFormReturn<ManageCommunicationFormProps, any>;
}

interface IBaseCommentItemProps {
  newCommentAdded: boolean;
  commentIx: number;
  methods: UseFormReturn<ManageCommunicationFormProps, any>;
  showDeleteComment: boolean;
  confirmCommentDelete: (commentId: number, commentIx: number) => void;
  copyComment: (commentIx: number) => void;
  commentFormValues: Comment | undefined;
  setCommentFormValues: React.Dispatch<React.SetStateAction<Comment | undefined>>;
}

export interface CommentItemProps extends IBaseCommentItemProps {
  commentField: FieldArrayWithId<ManageCommunicationFormProps, `comments`, 'key'>;
  update: UseFieldArrayUpdate<ManageCommunicationFormProps, `comments`>;
  drugSubstanceOptionList: IOption[];
}

export interface NonProductCommentItemProps extends IBaseCommentItemProps {
  nonProductCommentField: FieldArrayWithId<ManageCommunicationFormProps, 'comments', 'key'>;
}

export interface CommentActionsProps {
  isEditMode: boolean;
  commentIx: number;
  showDeleteCommentIcon: boolean;
  showCopyCommentIcon: boolean;
  control: Control<ManageCommunicationFormProps, any>;
  handleCopyComment: () => void;
  handleSaveCommentItem: () => void;
  handleCancelEditCommentItem: () => void;
  handleEditCommentItem: () => void;
  handleDeleteCommentItem: () => void;
}

export interface PcidModel {
  id: number | undefined;
  name: NullableString;
}

export interface IProductExtension {
  id?: number;
  pcid?: string | null;
  dosageFormName?: string | null;
  routeOfAdministrations?: IOption[];
}

export interface Comment {
  id?: number;
  description?: NullableString;
  question?: NullableString;
  response?: NullableString;
  productExtensions?: IProductExtension[] | null;
  birdsLinkToBIResponse?: NullableString;
  birdsLinkToBISAMP?: NullableString;
  productId?: number;
  drugSubstances?: IOption[];
  tags?: IOption[];
  isQuestionIncluded?: boolean;
  createdDate?: string;
  lastUpdatedDate?: string;
  createdBy?: string | null;
  lastUpdatedBy?: string | null;
}

export interface ManageCommunicationPageActionsProps {
  isFormSubmitting: boolean;
  onCommunicationDelete: () => Promise<void>;
  onCommunicationCreate: () => Promise<void>;
  onCommunicationComplete: () => Promise<void>;
  onCommunicationReinstate: () => Promise<void>;
}

export const defaultProductExtension: IProductExtension = {
  id: 0,
  dosageFormName: null,
  pcid: null,
  routeOfAdministrations: [],
};

export const defaultComment: Comment = {
  id: 0,
  description: null,
  question: null,
  response: null,
  productExtensions: [],
  birdsLinkToBIResponse: null,
  birdsLinkToBISAMP: null,
  productId: undefined,
  drugSubstances: [],
  tags: [],
  isQuestionIncluded: false,
  createdDate: '',
  lastUpdatedDate: '',
  createdBy: '',
  lastUpdatedBy: '',
};

export interface DeleteProductDialogSettings {
  productId?: number;
  index?: number;
  isOpen: boolean;
}

export interface DeleteCommentDialogSettings {
  commentId?: number;
  index?: number;
  isOpen: boolean;
}

export interface ApplicationChipItemProps extends ChipItemProps {
  submissions: ChipItemProps[] | undefined | null;
}

export interface CommunicationApplicationsProps {
  methods: UseFormReturn<ManageCommunicationFormProps, any>;
  isEditMode?: boolean;
}

export interface CommentTextProps {
  commentIndex: number;
  methods: UseFormReturn<ManageCommunicationFormProps, any>;
  field: FieldArrayWithId<ManageCommunicationFormProps, `comments`, 'key'>;
  isGeneralGuidance: boolean;
  setQuestionIncludedValue?: (index: number, val: boolean) => void;
  isCommentInEditMode: boolean;
}

export type ManageCommunicationPageData = {
  communication?: CommunicationModel;
  selectedProduct?: ProductModel;
  products?: ProductModel[];
  countries?: CountryModel[];
  submissionTypes?: SubmissionTypeModel[];
  formPromptState?: { isVisible: boolean; onConfirm?: () => void };
  newCommunicationId?: number;
  isDeleteCommunicationDialogOpen?: boolean;
  isCompleteCommunicationDialogOpen?: boolean;
  isReinstateCommunicationDialogOpen?: boolean;
  communicationStateChanged?: boolean;
  isCountriesLoading?: boolean;
  isSubmissionTypesLoading?: boolean;
  globalEditModeState?: GlobalEditModeState;
  generalGuidanceCommentsCount?: number;
};

export interface CommunicationCommentProductExtensionsProps {
  productExtensionTableValues: IProductExtension[];
  commentFieldIndex: number;
  isCommentInEditMode: boolean;
  isCommunicationCompleted: boolean;
  methods: UseFormReturn<ManageCommunicationFormProps, any>;
}
