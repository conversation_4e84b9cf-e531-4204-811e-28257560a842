import { combineReducers, configureStore } from '@reduxjs/toolkit';
import hacommsPermissionsStore from './permissions/permissionsStore';
import storage from 'redux-persist/lib/storage';
import { FLUSH, PAUSE, PERSIST, PURGE, REGISTER, REHYDRAT<PERSON>, persistReducer, persistStore } from 'redux-persist';

const rootReducer = combineReducers({
  hacommsPermissions: hacommsPermissionsStore.reducer,
});

// Exclude permissions from being persisted.
const persistConfig = {
  key: 'hacomms',
  version: 1,
  blacklist: ['hacommsPermissions'],
  storage,
};

const persistedReducer = persistReducer(persistConfig, rootReducer);

export const store = configureStore({
  reducer: persistedReducer,
  devTools: true,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({ serializableCheck: { ignoredActions: [FLUSH, REHY<PERSON>ATE, PAUSE, PERSIST, PURGE, REGISTER] } }),
});

export const persistor = persistStore(store);

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
export { useAppSelector, useAppDispatch } from './hooks';
