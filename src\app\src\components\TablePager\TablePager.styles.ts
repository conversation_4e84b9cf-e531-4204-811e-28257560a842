import styled from 'styled-components';

export default {
  Row: styled.div`
    background-color: ${(props) => props.theme.colors.backgroundPri};
    display: flex;
    align-items: center;
    gap: 1rem;
    justify-content: space-between;
    height: 3rem;
    padding-left: 1rem;
    padding-right: 1rem;
  `,
  ActionsBox: styled.div`
    display: flex;
    align-items: center;
    gap: 0.5rem;
    justify-content: center;

    span {
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    input {
      border: ${(props) => props.theme.border} ${(props) => props.theme.colors.borderPri};
      border-radius: ${(props) => props.theme.radius};
      box-sizing: border-box;
      height: 2rem;
      padding-left: 0.5rem;
      padding-right: 0.5rem;
      text-align: center;
      transition: border-color ${(props) => props.theme.transition};
      width: 3rem;

      :hover,
      :focus {
        border-color: ${(props) => props.theme.colors.brandHighlight};
        outline: none;
      }
    }

    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
      appearance: none;
      margin: 0;
    }

    input[type='number'] {
      -moz-appearance: textfield;
    }

    button {
      background-color: transparent;
      border: none;
      border-radius: ${(props) => props.theme.radiusRound};
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0;
      height: 2rem;
      transition: background-color ${(props) => props.theme.transition};
      width: 2rem;

      :hover {
        background-color: ${(props) => props.theme.colors.backgroundTer};
      }

      :not(:disabled) {
        cursor: pointer;
      }

      :disabled {
        opacity: 0.25;
        pointer-events: none;
      }

      span {
        font-size: 1.25rem;
      }
    }
  `,
};
