import React, { FC } from 'react';
import useHighlightText from 'components/shared/hooks/useHighlightText';
import { GridCellProps } from '@progress/kendo-react-grid';

interface TableCellWithHighlightProps {
  cellText: string;
  searchText: string;
  onRowClick: (event: any) => void;
  gridCell: GridCellProps;
  className?: string;
  style?: React.CSSProperties;
}

const TableCellWithHighlight: FC<TableCellWithHighlightProps> = (props: TableCellWithHighlightProps) => {
  const highlightedDescription = useHighlightText(props.cellText, props.searchText);

  return (
    <td onClick={() => props.onRowClick(props.gridCell)} style={props.style} className={props.className}>
      {highlightedDescription}
    </td>
  );
};

export default TableCellWithHighlight;
