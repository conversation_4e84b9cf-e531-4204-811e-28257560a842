import React, { FC } from 'react';
import { useForm<PERSON>ontext, useController } from 'react-hook-form';
import { PhlexDropdown } from 'phlex-core-ui';
import { IPhlexDropdownProps } from 'phlex-core-ui/build/src/controls/PhlexDropdown/PhlexDropdown';
import { DropDownListChangeEvent, DropDownListCloseEvent } from '@progress/kendo-react-dropdowns';
import { useTranslation } from 'react-i18next';

const Component: FC<IPhlexDropdownProps> = ({ name, required, onChange, label, ...rest }) => {
  const { t } = useTranslation();
  const { control, trigger } = useFormContext();

  const {
    field: { ...inputProps },
  } = useController({
    name,
    control,
    rules: { required: required ? t('Forms.RequiredField', { field: label }) : '' },
  });

  const onChangeHandler = (e: DropDownListChangeEvent) => {
    inputProps.onChange(e);
    onChange?.(e);
    trigger(name).catch((e) => console.log(`Error in select onchangehandler ${e}`));
  };

  return (
    <PhlexDropdown
      {...rest}
      value={inputProps.value}
      required={required}
      onChange={onChangeHandler}
      onClose={(e: DropDownListCloseEvent) => e.syntheticEvent.stopPropagation()}
      name={name}
      label={label}
    />
  );
};
export default Component;
