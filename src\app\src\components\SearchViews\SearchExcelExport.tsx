import React, { RefObject, useEffect, useState, FunctionComponent } from 'react';
import { SearchComment, SearchCommentCommandRequest } from 'axon-hacomms-api-sdk';
import { ExcelExport, ExcelExportColumn } from '@progress/kendo-react-excel-export';
import { commentApiService } from 'shared/api';
import { getFiltersFromStorage } from 'components/SearchFilters/search-filter-service';
import { useTranslation } from 'react-i18next';
import {
  APPLICATION_NUMBER_KEY,
  COUNTRY_KEY,
  DOSAGE_FORM_KEY,
  DRUG_SUBSTANCE_KEY,
  PRODUCT_CODE_KEY,
  PRODUCT_KEY,
  ROUTE_OF_ADMINISTRATION_KEY,
  SUBMISSION_NUMBER_KEY,
  SUBMISSION_TYPE_KEY,
  TAG_KEY,
  PRODUCT_TYPE_KEY,
} from '../SearchFilters/search-filter-types';
import { toDateTimeFormat } from 'shared/services/date-time-format';
import { WorkbookSheetRow } from '@progress/kendo-ooxml';
import { PhlexButton, PhlexLoader } from 'phlex-core-ui';
import { toast } from 'react-toastify';
import { generateDrugSubstanceLabelFromList } from 'shared/services/drug-substance-srv';
import { auditEventSource } from 'shared/consts';

interface ExportProps {
  tenant: string;
  disabled: boolean;
  searchModel: SearchCommentCommandRequest;
}

const SearchExcelExportComponent: FunctionComponent<ExportProps> = (props: ExportProps) => {
  const maxExportedComments = 5000;
  const { t } = useTranslation();
  const [data, setData] = useState<SearchComment[] | undefined>(undefined);
  const _exporter = React.createRef<ExcelExport>();
  const [isLoading, setIsLoading] = useState(false);
  const [disableExport, setDisableExport] = useState(false);

  const searchFiltersKeyNameMapper = new Map([
    [APPLICATION_NUMBER_KEY, `${t('SearchFilters.ApplicationNumber')}`],
    [COUNTRY_KEY, `${t('SearchFilters.Country')}`],
    [DOSAGE_FORM_KEY, `${t('SearchFilters.DosageForm')}`],
    [DRUG_SUBSTANCE_KEY, `${t('SearchFilters.DrugSubstance')}`],
    [PRODUCT_KEY, `${t('SearchFilters.DrugProduct')}`],
    [PRODUCT_CODE_KEY, `${t('SearchFilters.ProductCode')}`],
    [PRODUCT_TYPE_KEY, `${t('SearchFilters.ProductType')}`],
    [ROUTE_OF_ADMINISTRATION_KEY, `${t('SearchFilters.RouteOfAdministration')}`],
    [SUBMISSION_NUMBER_KEY, `${t('SearchFilters.SubmissionNumber')}`],
    [SUBMISSION_TYPE_KEY, `${t('SearchFilters.SubmissionType')}`],
    [TAG_KEY, `${t('SearchFilters.Tag')}`],
  ]);

  const searchFilters = getFiltersFromStorage();

  const fetchData = async () => {
    const response = await commentApiService.searchComments(props.tenant, {
      ...props.searchModel,
      skip: 0,
      take: maxExportedComments,
      requestType: auditEventSource.SEARCH_EXPORT,
    });
    setData(response.data.comments ?? []);
  };

  const excelExport = async () => {
    setIsLoading(true);
    await fetchData();
  };

  useEffect(() => {
    let hasFiltersApplied = false;
    for (const filterKey in searchFilters?.filters) {
      if (searchFilters?.filters[filterKey].length != 0) {
        hasFiltersApplied = true;
        break;
      }
    }

    if (
      !hasFiltersApplied &&
      !searchFilters?.searchText &&
      searchFilters?.communicationDateRange?.start == null &&
      searchFilters?.communicationDateRange?.end == null &&
      searchFilters?.isQuestionIncluded == null &&
      searchFilters?.generalGuidance == null
    ) {
      setDisableExport(true);
    } else {
      setDisableExport(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [props.searchModel]);

  useEffect(() => {
    if (data) {
      save(_exporter);
      setIsLoading(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data]);

  const getShiftedRows = () => {
    const filtersValues = Array<WorkbookSheetRow>();
    filtersValues.push({
      cells: [{ value: `${t('SearchExport.SearchCriteria')}${maxExportedComments}. ${t('SearchExport.AdjustSearchCriteria')}` }],
    });

    if (searchFilters?.searchText && searchFilters?.searchText.length > 0) {
      filtersValues.push({
        cells: [{ value: `${t('SearchFilters.SearchText')}` }, { value: `${searchFilters?.searchText}` }],
      });
    }

    let rangeFilter = ' - ';
    if (searchFilters?.communicationDateRange?.start != null) {
      rangeFilter = `${toDateTimeFormat(new Date(searchFilters?.communicationDateRange?.start).toISOString())} - `;
    }
    if (searchFilters?.communicationDateRange?.end != null) {
      rangeFilter += `${toDateTimeFormat(new Date(searchFilters?.communicationDateRange?.end).toISOString())}`;
    }
    if (rangeFilter.length > 3) {
      filtersValues.push({
        cells: [{ value: `${t('SearchTable.DateOfCommunication')}` }, { value: `${rangeFilter}` }],
      });
    }

    for (const filterKey in searchFilters?.filters) {
      if (searchFilters?.filters[filterKey].length != 0) {
        const searchArray = searchFilters?.filters[filterKey].map((x) => x.label).join(', ');
        filtersValues.push({
          cells: [
            {
              value: `${searchFiltersKeyNameMapper.get(filterKey)}`,
            },
            {
              value: `${searchArray}`,
            },
          ],
        });
      }
    }
    if (searchFilters?.isQuestionIncluded != null) {
      filtersValues.push({
        cells: [{ value: `${t('SearchFilters.QuestionFilterOptions.Label')}` }, { value: `${searchFilters?.isQuestionIncluded}` }],
      });
    }
    if (searchFilters?.generalGuidance != null) {
      filtersValues.push({
        cells: [{ value: `${t('SearchFilters.GeneralGuidanceFilterOptions.Label')}` }, { value: `${searchFilters?.generalGuidance}` }],
      });
    }
    return filtersValues;
  };

  const save = (component: RefObject<ExcelExport>) => {
    if (component.current) {
      const options = component.current.workbookOptions(
        data?.map((x: SearchComment) => ({
          ...x,
          description: x.description ?? t('Common.NotApplicable'),
          dateOfCommunication: toDateTimeFormat(x.dateOfCommunication),
          drugSubstanceNames: x.isGeneralGuidance
            ? t('Common.NotApplicable')
            : generateDrugSubstanceLabelFromList(
                x.drugSubstanceNames?.split(', ').map((x: string) => {
                  const drugSubstanceArray = x.split('|');
                  return { name: drugSubstanceArray[0], code: drugSubstanceArray[1] };
                }) ?? [],
                t('Common.DrugSubstanceCode'),
                t('Common.DrugSubstanceName')
              ),
          productName: x.isGeneralGuidance ? t('Common.NotApplicable') : x.productName,
          productCodeNames: x.isGeneralGuidance ? t('Common.NotApplicable') : x.productCodes,
          productTypeNames: x.isGeneralGuidance ? t('Common.NotApplicable') : x.productTypeNames,
          dosageFormNames: x.isGeneralGuidance ? t('Common.NotApplicable') : x.dosageFormNames,
          routeOfAdministrationNames: x.isGeneralGuidance ? t('Common.NotApplicable') : x.routeOfAdministrationNames,
          isQuestionIncluded: x.isQuestionIncluded
            ? t('SearchFilters.QuestionFilterOptions.QuestionIncluded')
            : t('SearchFilters.QuestionFilterOptions.CommentsOnly'),
          isGeneralGuidance: x.isGeneralGuidance
            ? t('SearchFilters.GeneralGuidanceFilterOptions.GeneralGuidance')
            : t('SearchFilters.GeneralGuidanceFilterOptions.NongeneralGuidance'),
          question: x.question ?? t('Common.NotApplicable'),
          response: x.response ?? t('Common.NotApplicable'),
        }))
      );
      if (options?.sheets) {
        const rows = options.sheets[0].rows;
        rows?.unshift(...getShiftedRows());
        component.current.save(options);
      }
    }
  };

  const exportComplete = () => {
    toast.success(t('SearchExport.ExportCompleted'), {
      toastId: 'export-completed',
    });
  };

  return (
    <>
      <PhlexButton
        type="button"
        onClick={excelExport}
        title={t('SearchExport.ExportButton')}
        label={t('SearchExport.ExportButton')}
        disabled={disableExport || isLoading || props.disabled}
      ></PhlexButton>{' '}
      {isLoading && <PhlexLoader position="inline" />}
      <ExcelExport onExportComplete={exportComplete} collapsible={true} ref={_exporter}>
        <ExcelExportColumn field="description" title={t('SearchTable.Comments')} width={500} />
        <ExcelExportColumn field="applicationNumberNames" title={t('SearchTable.ApplicationNumber')} />
        <ExcelExportColumn field="countryName" title={t('SearchTable.Country')} />
        <ExcelExportColumn field="dateOfCommunication" title={t('SearchTable.DateOfCommunication')} />
        <ExcelExportColumn field="dosageFormNames" title={t('SearchTable.DosageForm')} />
        <ExcelExportColumn field="drugSubstanceNames" title={t('SearchTable.DrugSubstances')} />
        <ExcelExportColumn field="isGeneralGuidance" title={t('SearchTable.GeneralGuidance')} />
        <ExcelExportColumn field="productCodeNames" title={t('SearchTable.ProductCode')} />
        <ExcelExportColumn field="productName" title={t('SearchTable.ProductName')} />
        <ExcelExportColumn field="productTypeNames" title={t('SearchTable.ProductTypes')} />
        <ExcelExportColumn field="isQuestionIncluded" title={t('SearchTable.QuestionIncluded')} />
        <ExcelExportColumn field="routeOfAdministrationNames" title={t('SearchTable.RoutesOfAdministration')} />
        <ExcelExportColumn field="submissionNumberNames" title={t('SearchTable.SubmissionNumber')} />
        <ExcelExportColumn field="submissionTypeName" title={t('SearchTable.SubmissionType')} />
        <ExcelExportColumn field="tagNames" title={t('SearchTable.Tags')} />
        <ExcelExportColumn field="question" title={t('SearchTable.Question')} />
        <ExcelExportColumn field="response" title={t('SearchTable.Response')} />
        <ExcelExportColumn field="birdsLinkToBISAMP" title={t('SearchTable.BirdsLinkToBISAMP')} />
        <ExcelExportColumn field="birdsLinkToBIResponse" title={t('SearchTable.BirdsLinkToBIResponse')} />
      </ExcelExport>
    </>
  );
};

export default SearchExcelExportComponent;
