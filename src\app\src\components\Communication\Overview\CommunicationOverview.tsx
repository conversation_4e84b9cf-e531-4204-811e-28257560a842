import React, { FC, useContext, useReducer } from 'react';
import { useTranslation } from 'react-i18next';
import { CountryModel, SubmissionTypeModel } from 'axon-hacomms-api-sdk';
import { DateFormats } from 'shared/consts';
import { DatePickerChangeEvent } from '@progress/kendo-react-dateinputs';
import {
  ApplicationChipItemProps,
  CommunicationOverviewProps,
  ManageCommunicationFormProps,
} from 'components/Communication/communication-form-types';
import components from './CommunicationOverview.styles';
import { AxonInput } from 'components/AxonInput';
import { AxonSelect } from 'components/AxonSelect';
import CommunicationApplications from './CommunicationApplications';
import { communicationApiService } from 'shared/api';
import { toast } from 'react-toastify';
import CommunicationContext, { EditStatus } from 'context/CommunicationContext';
import { toDateTimeFormat } from 'shared/services/date-time-format';
import { getCommunicationFormState, mapApplicationNumbers } from '../communication-service';

type CommunicationOverviewFormValues = {
  applications: ApplicationChipItemProps[];
  subject?: string;
  submissionType?: { id: number; name: string } | null;
  country?: { id: number; name: string | null } | null;
  dateOfCommunication?: Date | null;
};

interface CommunicationOverviewData {
  selectedSubmissionType?: SubmissionTypeModel;
  selectedCountry?: CountryModel;
  dateOfCommunication?: Date;
  isEditMode?: boolean;
  isSaving?: boolean;
  communicationOverviewFormValues?: CommunicationOverviewFormValues;
  lastUpdatedBy?: string | null;
  lastUpdatedDate?: string;
}

const reducer = (state: CommunicationOverviewData, newState: CommunicationOverviewData) => {
  return { ...state, ...newState };
};

const CommunicationOverviewComponent: FC<CommunicationOverviewProps> = (props: CommunicationOverviewProps) => {
  const { methods } = props;
  const {
    communication,
    isNewCommunication,
    countries,
    submissionTypes,
    tenant,
    globalEditModeState,
    setGlobalEditModeState,
    triggerFormValidation,
  } = useContext(CommunicationContext);
  const { t } = useTranslation();
  const {
    CommunicationOverviewContainer,
    CommunicationDatePicker,
    SubjectInputWrapper,
    OverviewRightContent,
    CommunicationOverview,
    ContentWrapper,
    ContentItem,
    ActionColumn,
    ActionButton,
    ActionIcon,
    ActionLoader,
    InfoSection,
  } = components;

  const [data, setData] = useReducer(reducer, {
    isEditMode: isNewCommunication,
    isSaving: false,
    lastUpdatedBy: communication?.lastUpdatedBy,
    lastUpdatedDate: communication?.lastUpdatedDate,
  });

  const onSubmissionTypeChangeHandler = (value: SubmissionTypeModel) => setData({ selectedSubmissionType: value });

  const onCountryChangeHandler = (value: any) => setData({ selectedCountry: value });

  const handleCommunicationUpdate = () => {
    toast.success(t('ManageCommunication.UpdateCommunicationSucceeded'), {
      toastId: 'communication-updated',
    });
  };

  const onSubmit = async (values: ManageCommunicationFormProps) => {
    setData({ isSaving: true });
    const request = {
      id: communication?.id,
      applications: mapApplicationNumbers(values.applications ?? []),
      submissionTypeId: values.submissionType?.id ?? 0,
      countryId: values.country?.id ?? 0,
      subject: values.subject ?? '',
      dateOfCommunication: values.dateOfCommunication != null ? values.dateOfCommunication.toDateString() : '',
    };

    try {
      const response = await communicationApiService.updateCommunication(tenant, request);
      setData({ lastUpdatedBy: response.data.lastUpdatedBy, lastUpdatedDate: response.data.lastUpdatedDate });
      handleCommunicationUpdate();

      const formValues = getCommunicationFormState(methods.getValues());
      methods.reset({ ...formValues });
    } catch (error: any) {
      toast.error(t('ManageCommunication.CreateFailedErrorMessage'));
    }
    setEditMode(false);
    setData({ isSaving: false });
  };

  const onSubmitHandlerErrors = () => {
    toast.error(t('ManageCommunication.CreateUpdateValidationErrorsMessage'), {
      toastId: 'communication-errors',
    });
  };

  const handleEditClick = async () => {
    if (!(await triggerFormValidation())) return;

    setEditMode(true);
    const formValues = methods.getValues();
    setData({
      communicationOverviewFormValues: {
        applications: formValues.applications,
        subject: formValues.subject,
        submissionType: formValues.submissionType,
        country: formValues.country,
        dateOfCommunication: formValues.dateOfCommunication,
      },
    });
  };

  const handleSaveClick = () => {
    methods.handleSubmit(onSubmit, onSubmitHandlerErrors)();
  };

  const handleCancelClick = () => {
    methods.reset({
      ...methods.getValues(),
      ...data.communicationOverviewFormValues,
    });
    setEditMode(false);
  };

  const setEditMode = (isEdit: boolean) => {
    setData({ isEditMode: isEdit });
    setGlobalEditModeState?.({
      ...globalEditModeState,
      overviewEditState: isEdit ? EditStatus.InEdit : EditStatus.NotStarted,
    });
  };

  const onCommunicationDateChange = (event: DatePickerChangeEvent) => {
    setData({ dateOfCommunication: event.value as Date });
  };

  return (
    <CommunicationOverview>
      <CommunicationOverviewContainer>
        <CommunicationApplications methods={methods} isEditMode={data.isEditMode} />
        <OverviewRightContent>
          <ContentWrapper>
            <ContentItem>
              <AxonSelect
                data-testid={`axon-input-communication.country`}
                name="country"
                label={t('ManageCommunication.Overview.CountryLabel') || ''}
                required={true}
                data={countries}
                textField="name"
                dataItemKey="id"
                value={data.selectedCountry}
                defaultValue={data.selectedCountry}
                defaultItem={{ id: null, name: t('ManageCommunication.Overview.SelectCountry') }}
                onChange={(e) => {
                  onCountryChangeHandler(e.target.value);
                }}
                validationMessage={methods.formState.errors['country']?.message?.toString() ?? undefined}
                disabled={communication?.isCompleted || !data.isEditMode}
                width="fullwidth"
              />
            </ContentItem>
            <ContentItem>
              <AxonSelect
                data-testid="axon-input-communication.submission-type"
                name="submissionType"
                label={t('ManageCommunication.Overview.SubmissionTypeLabel') || ''}
                required={true}
                data={submissionTypes}
                textField="name"
                dataItemKey="id"
                value={data.selectedSubmissionType}
                defaultValue={data.selectedSubmissionType}
                defaultItem={{ id: null, name: t('ManageCommunication.Overview.SelectSubmissionType') }}
                onChange={(e) => {
                  onSubmissionTypeChangeHandler(e.target.value);
                }}
                validationMessage={methods.formState.errors['submissionType']?.message?.toString() ?? undefined}
                disabled={communication?.isCompleted || !data.isEditMode}
                width="fullwidth"
              />
            </ContentItem>
            <ContentItem>
              <CommunicationDatePicker
                data-testid="axon-input-communication.date-of-communication"
                name="dateOfCommunication"
                label={t('ManageCommunication.DateOfCommunicationLabel')}
                required={true}
                placeholder={t('Common.DatePlaceholder')}
                format={DateFormats.ddMMMyyyy}
                value={data.dateOfCommunication}
                onChange={onCommunicationDateChange}
                validationMessage={methods.formState.errors['dateOfCommunication']?.message?.toString() ?? undefined}
                disabled={communication?.isCompleted || !data.isEditMode}
              />
            </ContentItem>
          </ContentWrapper>
          <SubjectInputWrapper>
            <AxonInput
              data-testid="axon-input-communication.subject"
              label={t('ManageCommunication.Overview.SubjectLabel') || ''}
              required={true}
              width="fullwidth"
              name="subject"
              validationMessage={methods.formState.errors['subject']?.message?.toString() ?? undefined}
              disabled={communication?.isCompleted || !data.isEditMode}
            />
          </SubjectInputWrapper>
          {communication?.id && communication.id > 0 && (
            <InfoSection>
              <div>
                {t('Common.CreatedOnField', {
                  createdOn: toDateTimeFormat(communication?.createdDate, 'DD MMM yyyy [at] HH:mma'),
                  createdBy: communication?.createdBy,
                })}
              </div>
              <div>
                {t('Common.LastUpdatedOnField', {
                  lastUpdatedOn: toDateTimeFormat(data.lastUpdatedDate, 'DD MMM yyyy [at] HH:mma'),
                  lastUpdatedBy: data.lastUpdatedBy,
                })}
              </div>
            </InfoSection>
          )}
        </OverviewRightContent>
      </CommunicationOverviewContainer>
      {!isNewCommunication && !communication?.isCompleted && (
        <ActionColumn>
          {globalEditModeState.commentEditState.editStatus !== EditStatus.InEdit && !data.isEditMode && (
            <ActionButton>
              <ActionIcon
                name={'edit'}
                size="large"
                tooltip={t('ManageCommunication.Edit')}
                fill={false}
                clickHandler={() => handleEditClick()}
              />
            </ActionButton>
          )}
          {data.isEditMode &&
            (data.isSaving ? (
              <ActionButton>
                <ActionLoader position="center" />{' '}
              </ActionButton>
            ) : (
              <>
                <ActionButton>
                  <ActionIcon
                    name={'save'}
                    size="large"
                    tooltip={t('ManageCommunication.Save')}
                    fill={false}
                    clickHandler={() => handleSaveClick()}
                  />
                </ActionButton>
                <ActionButton>
                  <ActionIcon
                    name="cancel"
                    size="large"
                    tooltip={t('ManageCommunication.Cancel')}
                    fill={false}
                    clickHandler={() => handleCancelClick()}
                  />
                </ActionButton>
              </>
            ))}
        </ActionColumn>
      )}
    </CommunicationOverview>
  );
};

export default CommunicationOverviewComponent;
