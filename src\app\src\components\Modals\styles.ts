import styled from 'styled-components';
import { Dialog } from '@progress/kendo-react-dialogs';
import { PhlexButton } from 'phlex-core-ui';

export default {
  Modal: styled(Dialog)`
    .k-window {
      background-color: ${(props) => props.theme.colors.backgroundPri};
      border-radius: ${(props) => props.theme.radius};
      box-shadow: ${(props) => props.theme.shadow};
      color: ${(props) => props.theme.colors.textPri};
      font-size: 1rem;
      overflow: hidden;
      padding: 0;
    }
    & .k-window-content {
      padding: 0;
    }
  `,
  ModalHeader: styled.div`
    display: flex;
    justify-content: space-between;
    font-size: ${(props) => props.theme.headingM};
    font-weight: ${(props) => props.theme.bold};
    padding-left: 1.5rem;
    padding-right: 1.5rem;
    padding-top: 1.5rem;
  `,
  ModalContent: styled.div`
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    padding: 0rem 1.5rem 1.5rem 1.5rem;
  `,
  ModalButtons: styled.div`
    background-color: ${(props) => props.theme.colors.backgroundTer};
    border-radius: ${(props) => `0 0 ${props.theme.shadow} ${props.theme.shadow}`};
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
    padding-top: 1rem;
    padding-bottom: 1rem;
  `,
  DeleteButton: styled(PhlexButton)`
    margin-right: auto;
  `,
};
