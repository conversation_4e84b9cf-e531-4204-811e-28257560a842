import { useBlocker } from 'components/shared/hooks/useBlocker';
import { useEffect } from 'react';

export const useUnsavedChangesPrompt = (message: string, when: boolean, confirmExit: (confirmNavigation: () => void) => void) => {
  useEffect(() => {
    if (when) {
      window.onbeforeunload = () => message;
    }

    return () => {
      window.onbeforeunload = null;
    };
  }, [message, when]);

  useBlocker(confirmExit, when);
};

export default useUnsavedChangesPrompt;
