import AppError from 'components/Error/AppError';
import React, { FunctionComponent } from 'react';
import { useTranslation } from 'react-i18next';

const AppErrorPage: FunctionComponent<{ basepath: string }> = ({ basepath }) => {
  const { t } = useTranslation();

  return (
    <AppError
      heading={t('AppError.Heading')}
      subHeading={t('AppError.Subheading')}
      suggestions={t('AppError.Suggestions')}
      pageMissing={t('AppError.ErrorPage')}
      backButton={t('AppError.BackButton')}
      basepath={basepath}
    ></AppError>
  );
};

export default AppErrorPage;
