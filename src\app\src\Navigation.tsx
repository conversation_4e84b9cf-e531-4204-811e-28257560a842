import { routes } from 'pages';
import { <PERSON><PERSON>NavItem, PhlexLoader } from 'phlex-core-ui';
import React, { FunctionComponent, Fragment, useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { removeTrailingSlashFromPath } from 'shared/services/urlUtils';
import './multilingual/i18n';
import { useOrganisationFromUrl } from 'hooks/shared';
import { permissionApi } from 'components/shared/api/permissionApi';
import { arrayOfPermissions } from 'shared/services/permissionsUtils';
import { Permissions } from 'constants/permissions';
import { authHelper } from 'shared/services';

const Navigation: FunctionComponent<{ basepath: string }> = ({ basepath }) => {
  const { t } = useTranslation();
  const cleanBasePath = removeTrailingSlashFromPath(basepath);
  const tenant = useOrganisationFromUrl();
  const [userPermissions, setUserPermissions] = useState<string[]>([]);

  const [isPermissionsLoading, setIsPermissionsLoading] = useState(false);

  const getUserPermissions = useCallback(async () => {
    const hacommsPermissions = Object.keys(Permissions);
    const response = await permissionApi.getUserPermissions(hacommsPermissions, tenant);
    setUserPermissions(arrayOfPermissions(response?.data?.data?.permissions) || ([] as string[]));
    setIsPermissionsLoading(false);
  }, [tenant]);

  useEffect(() => {
    setIsPermissionsLoading(true);
    getUserPermissions().catch(() => setIsPermissionsLoading(false));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const isAdmin = () => {
    const requiredPermissions = [
      Permissions.ViewCommunication,
      Permissions.ViewProduct,
      Permissions.ViewSubstance,
      Permissions.ViewRouteOfAdministration,
      Permissions.ViewTag,
      Permissions.ViewDosageForm,
    ];
    return authHelper.hasAccess(userPermissions, requiredPermissions);
  };

  return isPermissionsLoading ? (
    <PhlexLoader position="page" />
  ) : (
    <Fragment>
      <PhlexNavItem text={t('Nav.Search')} to={`${cleanBasePath}${routes.search}`} />
      {isAdmin() && (
        <>
          <PhlexNavItem text={t('Nav.Manage')}>
            <PhlexNavItem text={t('Nav.ManageSubstances')} to={`${cleanBasePath}${routes.substances}`} />
            <PhlexNavItem text={t('Nav.ManageProducts')} to={`${cleanBasePath}${routes.products}`} />
            <PhlexNavItem text={t('Nav.ManageRoutesOfAdministration')} to={`${cleanBasePath}${routes.routesOfAdministration}`} />
            <PhlexNavItem text={t('Nav.ManageDosageForms')} to={`${cleanBasePath}${routes.dosageForms}`} />
            <PhlexNavItem text={t('Nav.ManageTags')} to={`${cleanBasePath}${routes.tags}`} />
          </PhlexNavItem>
          <PhlexNavItem text={t('Nav.Communications')} to={`${cleanBasePath}${routes.communications}`} />
        </>
      )}
    </Fragment>
  );
};

export default Navigation;
