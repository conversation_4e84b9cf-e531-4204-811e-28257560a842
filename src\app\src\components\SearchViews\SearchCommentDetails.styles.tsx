import styled from 'styled-components';

export default {
  BoldProduct: styled.span`
    font-weight: ${(props) => props.theme.bold};
  `,
  WideColumn: styled.div`
    display: flex;
    flex: 1;
    flex-direction: column;
    max-height: calc(100vh - 11.5rem);
    overflow: auto;
  `,
  TextAreaContainer: styled.div`
    background-color: ${(props) => props.theme.colors.backgroundSec};
    border-radius: ${(props) => props.theme.radius};
    flex: 1;
    line-height: ${(props) => props.theme.lineHeight};
    margin-left: 0.5rem;
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
    max-height: calc(100vh - 14.5rem);
    padding: 1rem;
    overflow: auto;
  `,
  NumberBox: styled.div`
    background-color: ${(props) => props.theme.colors.buttonPri};
    border-radius: ${(props) => props.theme.radiusRound};
    color: ${(props) => props.theme.colors.textLight};
    font-size: ${(props) => props.theme.textS};
    padding: 0.5rem;
    position: absolute;
    left: 0.25rem;
    top: 0.25rem;
    text-align: center;
  `,
  Container: styled.div`
    background-color: ${(props) => props.theme.colors.backgroundPri};
    display: flex;
    flex: 1;
    gap: 1rem;
    padding: 0.5rem;
  `,
  MainContainer: styled.div`
    background-color: ${(props) => props.theme.colors.backgroundPri};
    border-radius: ${(props) => props.theme.radius};
    box-shadow: ${(props) => props.theme.shadow};
    gap: 1rem;
    padding: 0.5rem;
    position: relative;
  `,
  ElementContainer: styled.div`
    border-bottom: ${(props) => props.theme.border} ${(props) => props.theme.colors.borderPri};
    border-radius: ${(props) => props.theme.radius};
    display: flex;
    align-items: center;
    gap: 0.75rem;
    line-height: ${(props) => props.theme.lineHeightHeading};
    padding: 0.75rem;
  `,
  LabelWrapper: styled.div`
    flex: 1;
    font-weight: ${(props) => props.theme.bold};
  `,
  TextWrapper: styled.div`
    display: flex;
    align-items: flex-end;
    flex-direction: column;
    gap: 0.5rem;
  `,
  ChipsContainer: styled.div`
    display: flex;
    flex-wrap: wrap;
    gap: 0.375rem;
    justify-content: flex-end;
  `,
  ProductsWrapper: styled.div`
    cursor: pointer;
    border-radius: 100rem;
    padding: 0.25rem 0.5rem;
    transition: background-color ${(props) => props.theme.transition};
    &:hover {
      background-color: ${(props) => props.theme.colors.backgroundTer};
    }
  `,
  QuestionWrapper: styled.div`
    flex: 1;
    font-weight: ${(props) => props.theme.bold};
    margin-left: 2rem;
  `,
  AnswerWrapper: styled.div`
    flex: 1;
    font-weight: ${(props) => props.theme.bold};
    margin-left: 1rem;
  `,
  QuestionIncludedWrapper: styled.div``,
  InfoSection: styled.div`
    font-size: ${(props) => props.theme.textXS};
  `,
  SectionBorder: styled.div`
    border-bottom: ${({ theme }) => `${theme.border} ${theme.colors.borderPri}`};
    box-shadow: ${({ theme }) => theme.shadow};
    margin-top: 1rem;
    margin-bottom: 0.5rem;
  `,
};
