import { SelectionRange } from '@progress/kendo-react-dateinputs';
import { SearchCommentCommandRequest } from 'axon-hacomms-api-sdk';
import { MultiselectData } from 'components/shared/types/types';
import { Dispatch, SetStateAction } from 'react';

export const USER_SEARCH_FILTER = 'user-search-filter';
export const APPLICATION_NUMBER_KEY = 'applicationNumbers';
export const COUNTRY_KEY = 'countries';
export const DOSAGE_FORM_KEY = 'dosageForms';
export const DRUG_SUBSTANCE_KEY = 'drugSubstances';
export const PRODUCT_KEY = 'products';
export const PRODUCT_CODE_KEY = 'productCodes';
export const ROUTE_OF_ADMINISTRATION_KEY = 'routeOfAdministrations';
export const SUBMISSION_NUMBER_KEY = 'submissionNumbers';
export const SUBMISSION_TYPE_KEY = 'submissionTypes';
export const TAG_KEY = 'tags';
export const PRODUCT_TYPE_KEY = 'productTypes';
export const SEARCH_TABLE_COLUMNS_KEY = 'search-table-columns';

export interface SearchFiltersProps {
  tenant: string;
  setSearchModel: (value: SetStateAction<SearchCommentCommandRequest>) => void;
  disable?: boolean;
  clear?: boolean;
  setDisable?: Dispatch<SetStateAction<boolean>>;
}

export interface SearchFilterState {
  filters: {
    [key: string]: MultiselectData;
  };
  searchText?: string;
  fuzzy?: boolean;
  communicationDateRange?: SelectionRange;
  isQuestionIncluded?: boolean;
  generalGuidance?: boolean;
  question?: string;
  response?: string;
}

export const defaultFilterState: SearchFilterState = {
  filters: {},
};
