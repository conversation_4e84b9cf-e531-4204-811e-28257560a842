import React, { Fragment, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useThemeOptions, localStorageSrv, PhlexSwitch } from 'phlex-core-ui';
import { SwitchChangeEvent } from '@progress/kendo-react-inputs';

const PHLEX_THEME_KEY = 'axon_theme';
const DARK_THEME_KEY = 'dark';
const DEFAULT_THEME_KEY = 'default';

export const DarkModeSwitch = (): JSX.Element => {
  const { t } = useTranslation();
  const options = useThemeOptions(t);

  const [isDark, setIsDark] = useState<boolean>(localStorageSrv.getValue(PHLEX_THEME_KEY) === DARK_THEME_KEY);

  function onThemeChange(event: SwitchChangeEvent) {
    setIsDark(event.value);
    const themKey = event.value ? DARK_THEME_KEY : DEFAULT_THEME_KEY;
    const phlexTheme = options.find((x) => x.id === themKey);
    localStorageSrv.setValue(PHLEX_THEME_KEY, themKey);
    phlexTheme?.event();
  }

  return (
    <Fragment>
      <span style={{ marginRight: '10px' }}>Dark mode</span>
      <PhlexSwitch checked={isDark} name="dark-mode" onChange={onThemeChange} />
    </Fragment>
  );
};
