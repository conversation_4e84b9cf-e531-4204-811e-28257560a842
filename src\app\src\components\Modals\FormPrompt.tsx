import React, { <PERSON> } from 'react';
import components from './styles';
import { PhlexButton } from 'phlex-core-ui';
import { useTranslation } from 'react-i18next';

interface FormPromptProps {
  when: boolean;
  message: string;
  onContinue?: () => void;
  onClose: () => void;
}

export const FormPrompt: FC<FormPromptProps> = ({ when, message, onContinue, onClose }) => {
  const { Modal, ModalHeader, ModalContent, ModalButtons } = components;
  const { t } = useTranslation();

  const onClickHandler = () => {
    if (onContinue) {
      onContinue();
    }
  };

  return when ? (
    <Modal width={600}>
      <ModalHeader>{'Warning'}</ModalHeader>
      <ModalContent>{message}</ModalContent>
      <ModalButtons>
        <PhlexButton color="secondary" label={t('Forms.CancelButton')} onClick={onClose} />
        <PhlexButton label={t('Forms.ContinueButton')} onClick={onClickHandler} />
      </ModalButtons>
    </Modal>
  ) : null;
};
