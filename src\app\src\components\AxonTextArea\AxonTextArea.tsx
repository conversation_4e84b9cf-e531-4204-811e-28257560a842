import { TextAreaChangeEvent } from '@progress/kendo-react-inputs';
import { TextAreaBlurEvent } from '@progress/kendo-react-inputs/dist/npm/textarea/interfaces/TextAreaBlurEvent';
import { PhlexTextArea } from 'phlex-core-ui';
import { IPhlexTextAreaProps } from 'phlex-core-ui/build/src/controls/PhlexTextArea/PhlexTextArea';
import React, { FC } from 'react';
import { useController, useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

const Component: FC<IPhlexTextAreaProps> = ({ name, onChange, required, onBlur, label, ...rest }) => {
  const { t } = useTranslation();
  const { control, setValue, trigger } = useFormContext();

  const {
    field: { ...inputProps },
  } = useController({
    name,
    control,
    defaultValue: rest.value,
    rules: { required: required ? t('Forms.RequiredField', { field: label }) : '' },
  });

  const onChangeHandler = (e: TextAreaChangeEvent) => {
    setValue(name, e.value, { shouldDirty: true });
    onChange && onChange(e);
    trigger(name);
  };

  const onBlurHandler = (e: TextAreaBlurEvent) => {
    inputProps.onBlur();
    onBlur && onBlur(e);
  };

  return (
    <PhlexTextArea
      {...rest}
      required={required}
      value={inputProps.value ?? ''}
      onChange={onChangeHandler}
      onBlur={onBlurHandler}
      name={name}
      label={label}
    />
  );
};
export default Component;
