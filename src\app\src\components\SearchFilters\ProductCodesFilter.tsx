import { MultiselectData } from 'components/shared/types/types';
import { PhlexMultiSelect } from 'phlex-core-ui';
import React, { FC, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { productApiService } from 'shared/api';
import { PRODUCT_CODE_KEY, SearchFiltersProps } from './search-filter-types';
import { SearchCommentCommandRequest } from 'axon-hacomms-api-sdk';
import { getFiltersFromStorage, updateFiltersInStorage } from './search-filter-service';

const ProductCodesFilterComponent: FC<SearchFiltersProps> = (props: SearchFiltersProps) => {
  const { tenant, setSearchModel, disable, clear } = props;
  const { t } = useTranslation();
  const [selectedProductCodes, setSelectedProductCodes] = useState<MultiselectData>([]);

  const fetchProductCodes = async (): Promise<MultiselectData> => {
    const response = await productApiService.getAllProductCodes(tenant);
    const data =
      response.data.data
        ?.filter((item: string) => item !== t('Common.NotAssigned'))
        .map((item: string) => ({ value: item, label: item })) ?? [];
    return data;
  };

  const updateSearchModel = (data: MultiselectData) => {
    setSearchModel((request: SearchCommentCommandRequest) => ({
      ...request,
      productCodes: data.map((x) => x.value),
      skip: 0,
      take: 10,
    }));
  };

  const onProductCodeFilterChange = (data: MultiselectData) => {
    setSelectedProductCodes(data);
    updateFiltersInStorage(data, PRODUCT_CODE_KEY);
    updateSearchModel(data);
  };

  useEffect(() => {
    const searchFilters = getFiltersFromStorage();
    if (searchFilters?.filters[PRODUCT_CODE_KEY] && searchFilters.filters[PRODUCT_CODE_KEY].length > 0) {
      const filterData = searchFilters.filters[PRODUCT_CODE_KEY];
      setSelectedProductCodes(filterData);
      updateSearchModel(filterData);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (disable) {
      setSelectedProductCodes([]);
      updateFiltersInStorage([], PRODUCT_CODE_KEY);
    }
  }, [disable]);

  useEffect(() => {
    if (clear) setSelectedProductCodes([]);
  }, [clear]);
  return (
    <PhlexMultiSelect
      data-testid={'axon-input-search-filter-product-code'}
      name={`productCodeSearchFilter`}
      label={t('SearchFilters.ProductCode')}
      value={selectedProductCodes}
      defaultValues={selectedProductCodes}
      width="fullwidth"
      compactMode={true}
      fetchData={() => fetchProductCodes()}
      onlyFetchOnOpen={true}
      onChange={onProductCodeFilterChange}
      disabled={disable}
    />
  );
};

export default ProductCodesFilterComponent;
