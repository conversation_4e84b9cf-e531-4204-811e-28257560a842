import React, { <PERSON> } from 'react';
import { useNavigate } from 'react-router-dom';
import { routes } from 'pages';
import components from './AppError.styles';

const AppError: FC<{
  heading: string;
  subHeading: string;
  pageMissing: string;
  suggestions: string;
  backButton: string;
  basepath: string;
}> = ({ heading, subHeading, pageMissing, suggestions, backButton, basepath }) => {
  const navigate = useNavigate();
  const { Wrapper, Logo, Heading, Subheading, BackButton, Image } = components;

  return (
    <Wrapper>
      <div>
        <Logo />
        <Heading>{heading}</Heading>
        <Subheading>{subHeading}</Subheading>
        <p>{pageMissing}</p>
        <p>{suggestions}</p>
        <BackButton label={backButton} onClick={() => navigate(`${basepath}${routes.search}`)} />
      </div>
      <Image src="/assets/NotFound.png" />
    </Wrapper>
  );
};

export default AppError;
