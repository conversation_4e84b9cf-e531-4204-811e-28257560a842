import { PhlexDatePicker, PhlexDropdown, PhlexIcon, PhlexInput } from 'phlex-core-ui';
import styled from 'styled-components';

export const MainContent = styled.div`
  padding: 1rem;
`;

export const ClearFilters = styled(PhlexIcon)`
  color: ${(props) => props.theme.colors.textInvalid};
  height: ${(props) => props.theme.inputHeight};
  display: flex;
  max-width: 1.25rem;
  align-items: center;
  align-self: flex-end;
  padding-left: 0.25rem;
  padding-right: 0.25rem;
`;

export const SearchDatePicker = styled(PhlexDatePicker)`
  width: 100%;
`;

export const SearchDropDown = styled(PhlexDropdown)`
  width: 100%;
`;

export const SearchInput = styled(PhlexInput)`
  width: 100%;
`;

export const InnerTable = styled.div`
  max-height: 12.25rem;
  overflow-y: auto;

  &&&& tr {
    td {
      background-color: ${(props) => props.theme.colors.backgroundPri};
    }

    :hover td {
      background-color: ${(props) => props.theme.colors.backgroundTer};
    }
  }
`;

export const TableIcon = styled.td`
  &&& {
    cursor: pointer;
    font-family: ${(props) => props.theme.iconFont};
    font-size: 1.25rem;
    font-style: normal;
    line-height: 1.3125rem;
  }
`;
