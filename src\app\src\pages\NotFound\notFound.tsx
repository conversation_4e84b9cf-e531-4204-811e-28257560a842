import AppError from 'components/Error/AppError';
import React, { FunctionComponent } from 'react';
import { useTranslation } from 'react-i18next';

const NotFoundPage: FunctionComponent<{ basepath: string }> = ({ basepath }) => {
  const { t } = useTranslation();

  return (
    <AppError
      heading={t('NotFound.Heading')}
      subHeading={t('NotFound.Subheading')}
      suggestions={t('NotFound.Suggestions')}
      pageMissing={t('NotFound.PageMissing')}
      backButton={t('NotFound.BackButton')}
      basepath={basepath}
    ></AppError>
  );
};

export default NotFoundPage;
