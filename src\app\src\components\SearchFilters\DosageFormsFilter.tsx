import { DosageFormModel, SearchCommentCommandRequest } from 'axon-hacomms-api-sdk';
import { MultiselectData } from 'components/shared/types/types';
import { PhlexMultiSelect } from 'phlex-core-ui';
import React, { FC, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { dosageFormApiService } from 'shared/api';
import { DOSAGE_FORM_KEY, SearchFiltersProps } from './search-filter-types';
import { getFiltersFromStorage, updateFiltersInStorage } from './search-filter-service';

const DosageFormsFilterComponent: FC<SearchFiltersProps> = (props: SearchFiltersProps) => {
  const { tenant, setSearchModel, disable, clear } = props;
  const { t } = useTranslation();
  const [selectedDosageForms, setSelectedDosageForms] = useState<MultiselectData>([]);

  const fetchDosageForms = async (): Promise<MultiselectData> => {
    const response = await dosageFormApiService.getDosageFormList(tenant);
    const data = response.data.data?.map((item: DosageFormModel) => ({ value: item.id?.toString() ?? '', label: item.name ?? '' })) ?? [];
    return data;
  };

  const updateSearchModel = (data: MultiselectData) => {
    setSearchModel((request: SearchCommentCommandRequest) => ({
      ...request,
      dosageForms: data.map((x) => ({ id: Number(x.value), name: x.label })),
      skip: 0,
      take: 10,
    }));
  };

  const onDosageFormFilterChange = (data: MultiselectData) => {
    setSelectedDosageForms(data);
    updateFiltersInStorage(data, DOSAGE_FORM_KEY);
    updateSearchModel(data);
  };

  useEffect(() => {
    const searchFilters = getFiltersFromStorage();
    if (searchFilters?.filters[DOSAGE_FORM_KEY] && searchFilters.filters[DOSAGE_FORM_KEY].length > 0) {
      const filterData = searchFilters.filters[DOSAGE_FORM_KEY];
      setSelectedDosageForms(filterData);
      updateSearchModel(filterData);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (disable) {
      setSelectedDosageForms([]);
      updateFiltersInStorage([], DOSAGE_FORM_KEY);
    }
  }, [disable]);

  useEffect(() => {
    if (clear) setSelectedDosageForms([]);
  }, [clear]);

  return (
    <PhlexMultiSelect
      data-testid={'axon-input-search-filter-dosage-form'}
      name={`dosageFormSearchFilter`}
      label={t('SearchFilters.DosageForm')}
      value={selectedDosageForms}
      defaultValues={selectedDosageForms}
      width="fullwidth"
      compactMode={true}
      fetchData={() => fetchDosageForms()}
      onlyFetchOnOpen={true}
      onChange={onDosageFormFilterChange}
      disabled={disable}
    />
  );
};

export default DosageFormsFilterComponent;
