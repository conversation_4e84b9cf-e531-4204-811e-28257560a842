import styled from 'styled-components';

export default {
  ContentWrapper: styled.div`
    flex: 1;
  `,
  HeadingWrapper: styled.div`
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-left: 1.5rem;
    margin-right: 2.5rem;
  `,
  Heading: styled.h2`
    font-size: ${(props) => props.theme.headingM};
    font-weight: ${(props) => props.theme.bold};
    margin: 0;
  `,
  RightContent: styled.div``,
  Container: styled.div`
    display: flex;
    padding-left: 1.5rem;
    padding-top: 0.75rem;
    padding-bottom: 1rem;
  `,
  LoadMoreButtonWrapper: styled.div`
    display: grid;
    place-items: center;
    margin-top: 2rem;
  `,
};
