import { SearchDetailsModel } from 'axon-hacomms-api-sdk';
import { useOrganisationFromUrl } from 'hooks/shared';
import React, { Fragment, useEffect, useState, useCallback, FunctionComponent } from 'react';
import { commentApiService } from 'shared/api';
import { PhlexLayout, PhlexLoader, PhlexBreadcrumb } from 'phlex-core-ui';
import SearchCommunicationOverview from 'components/SearchViews/SearchCommunicationOverview';
import SearchCommentDetails from 'components/SearchViews/SearchCommentDetails';
import SearchOtherRelatedComments from 'components/SearchViews/SearchOtherRelatedComments';
import components from './SearchCommentView.styles';
import { useTranslation } from 'react-i18next';
import { routes } from 'pages/routes';
import { useLocation, useNavigate } from 'react-router-dom';
import { Permissions } from 'constants/permissions';
import { withRestrictedPermissions } from 'hoc';

const { PageTopBar } = PhlexLayout;

const SearchCommentViewPage: FunctionComponent<{ basepath: string }> = ({ basepath }) => {
  const { state } = useLocation();
  const { CommunicationContent, Wrapper, Heading, HeadingWrapper, ProductInfo, BoldProduct, CommentsWrapper } = components;
  const tenant = useOrganisationFromUrl();
  const [searchResult, setSearchResult] = useState<SearchDetailsModel>();
  const [isResultLoading, setIsResultLoading] = useState(false);
  const { t } = useTranslation();
  const navigate = useNavigate();

  const breadcrumbs = [
    {
      key: 'search',
      text: t('Nav.Search'),
      path: `${basepath}${routes.search}`,
      active: 'false' as const,
    },
    {
      key: 'comment',
      text: searchResult?.subject ?? t('Nav.Comments'),
      active: 'true' as const,
    },
  ];

  const getComment = useCallback(
    async (communicationId: number, commentId: number) => {
      try {
        const searchDetailsResponse = await commentApiService.searchDetails(+communicationId, +commentId, tenant);
        setSearchResult(searchDetailsResponse.data);
        setIsResultLoading(false);
      } catch (error: any) {
        if (error.response?.data.Message.includes('Internal Server Error')) {
          navigate(`${basepath}${routes.error}`);
        }

        console.error('Error fetching data:', error);
      }
    },
    [tenant, basepath, navigate]
  );

  useEffect(() => {
    if (state.communicationId && state.commentId) {
      setIsResultLoading(true);
      getComment(+state.communicationId, +state.commentId);
    }
  }, [state.communicationId, state.commentId, getComment]);

  return (
    <Fragment>
      <PageTopBar>
        <PhlexBreadcrumb options={breadcrumbs} />
      </PageTopBar>
      {isResultLoading ? (
        <PhlexLoader position="page" />
      ) : (
        <CommunicationContent>
          <CommentsWrapper>
            <Wrapper>
              <HeadingWrapper>
                <Heading>{t('SearchCommentDetails.CommentLabel')}</Heading>
                <ProductInfo>
                  {!searchResult?.comment?.isGeneralGuidance && t('SearchCommentView.ProductLabel') + ' '}
                  <BoldProduct>
                    {!searchResult?.comment?.isGeneralGuidance ? searchResult?.comment?.productName : t('Common.GeneralGuidance')}
                  </BoldProduct>
                </ProductInfo>
              </HeadingWrapper>
              <SearchCommentDetails comment={searchResult?.comment} number={0} />
            </Wrapper>
            <SearchOtherRelatedComments
              communicationId={state.communicationId}
              productId={
                !searchResult?.comment?.isGeneralGuidance
                  ? searchResult?.allProducts?.find((x) => x.name == searchResult?.comment?.productName)?.id
                  : undefined
              }
              excludedCommentId={searchResult?.comment?.id}
              tenant={tenant}
            />
          </CommentsWrapper>
          <SearchCommunicationOverview communication={searchResult} tenant={tenant} />
        </CommunicationContent>
      )}
    </Fragment>
  );
};

export default withRestrictedPermissions(SearchCommentViewPage, [Permissions.CanSearchComments]);
