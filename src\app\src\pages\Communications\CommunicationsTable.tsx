import { CompositeFilterDescriptor, SortDescriptor } from '@progress/kendo-data-query';
import { GridCellProps, GridRowClickEvent, GridSortChangeEvent } from '@progress/kendo-react-grid';
import { CommunicationPagedListModel } from 'axon-hacomms-api-sdk';
import documentsApi from 'components/shared/api/documentsApi';
import useDataGridPagination, { Pagination } from 'components/shared/hooks/useDataGridPagination';
import HaCommsTable from 'components/Tables/HaCommsTable';
import { PhlexLoader } from 'phlex-core-ui';
import { TableColumn } from 'phlex-core-ui/build/src/controls/PhlexTable/PhlexTable.types';
import React, { Fragment, FunctionComponent, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router';
import { toDateTimeFormat } from 'shared/services/date-time-format';
import { routes } from 'pages';
import { TableIcon } from 'components/Content/Content.styles';
import { tableFilters } from 'shared/consts';

const initialSort: Array<SortDescriptor> = [{ field: 'subject', dir: 'asc' }];

export interface CommunicationTableProps {
  isOpen: boolean;
  cleanBasePath: string;
  initialFilter: CompositeFilterDescriptor;
  tabOpen: number;
  setChangeFilter: (changeFilter: (filter: CompositeFilterDescriptor) => void) => void;
}

const CommunicationsTable: FunctionComponent<CommunicationTableProps> = ({
  isOpen,
  cleanBasePath,
  initialFilter,
  tabOpen,
  setChangeFilter,
}) => {
  const initState = {
    skip: 0,
    data: [] as CommunicationPagedListModel[],
    total: 0,
    take: 20,
    filter: '',
    orderBy: 'subject=>asc',
    refresh: false,
    cleanBasePath: cleanBasePath,
  } as Pagination<CommunicationPagedListModel>;

  const [pagination, onPageChange, changeFilter, sortChange] = useDataGridPagination<CommunicationPagedListModel>(
    isOpen ? documentsApi.communications.getOpenList : documentsApi.communications.getCompletedList,
    initState
  );

  useEffect(() => {
    if (changeFilter) {
      setChangeFilter(changeFilter);
    }
  }, [changeFilter, setChangeFilter]);

  const { t } = useTranslation();
  const navigate = useNavigate();

  const viewCell = (e: GridCellProps) => {
    return (
      <TableIcon title="View Details" onClick={() => navigate(`${cleanBasePath}${routes.updateCommunication(e.dataItem.id)}`)}>
        chevron_right
      </TableIcon>
    );
  };

  const columns: TableColumn[] = [
    { identifier: tableFilters.SUBJECT, title: t('CommunicationTable.SubjectField'), showTooltip: true },
    { identifier: tableFilters.COUNTRY_NAME, title: t('CommunicationTable.CountryField'), showTooltip: true },
    { identifier: tableFilters.PRODUCT_NAMES, title: t('CommunicationTable.ProductField'), showTooltip: true },
    { identifier: tableFilters.COMMUNICATION_DATE, title: t('CommunicationTable.DateOfCommunicationField'), showTooltip: true },
    { identifier: tableFilters.CREATED_DATE, title: t('Common.CreatedDateField'), showTooltip: true },
    { identifier: tableFilters.CREATED_BY, title: t('Common.CreatedByField'), showTooltip: true },
    { identifier: '', title: '', cell: viewCell, width: '56px' },
  ];

  const [nameOfRows] = useState(t('CommunicationTable.PagerLabel'));
  const [sort, setSort] = useState(initialSort);
  const onSortChange = (e: GridSortChangeEvent) => {
    setSort(e.sort);
    sortChange(e.sort);
  };

  const onRowClick = (e: GridRowClickEvent) => {
    localStorage.setItem('COMMUNICATIONS_ACTIVE_TAB', tabOpen.toString());
    navigate(`${cleanBasePath}${routes.updateCommunication(e.dataItem.id)}`);
  };

  return (
    <Fragment>
      {pagination.isLoading ? (
        <PhlexLoader position="page" />
      ) : (
        <HaCommsTable
          {...pagination}
          data={pagination.data.map((x) => ({
            ...x,
            country: x.countryName,
            product: x.productNames,
            createdDate: toDateTimeFormat(x.createdDate),
            dateOfCommunication: toDateTimeFormat(x.dateOfCommunication),
          }))}
          uniqueIdField={'name'}
          filterable={false}
          sortable={true}
          pageable={true}
          onPageChange={onPageChange}
          onSortChange={onSortChange}
          isLoading={pagination.isLoading}
          total={pagination.total}
          columns={columns}
          nameOfRows={nameOfRows}
          filter={initialFilter}
          sort={sort}
          onRowClick={(e: GridRowClickEvent) => onRowClick(e)}
        />
      )}
    </Fragment>
  );
};

export default CommunicationsTable;
