import React from 'react';
import ReactDOM from 'react-dom/client';
import { PhlexSharedThemeProvider } from 'phlex-core-ui';
import { B<PERSON>erRouter } from 'react-router-dom';
import './multilingual/i18n';
import App from './App';
import '@progress/kendo-theme-default/dist/all.css';
import { setupAxiosInterceptors } from 'axon-core-ui-shared';

const root = ReactDOM.createRoot(document.getElementById('root'));

setupAxiosInterceptors();

root.render(
  <React.StrictMode>
    <BrowserRouter basename="pharmalex">
      <PhlexSharedThemeProvider>
        <App />
      </PhlexSharedThemeProvider>
    </BrowserRouter>
  </React.StrictMode>
);
