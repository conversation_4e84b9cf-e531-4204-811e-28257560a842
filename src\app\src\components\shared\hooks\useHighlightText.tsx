import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

export const useHighlightText = (description: string, searchText: string): string | (string | React.JSX.Element)[] => {
  const [markup, setMarkup] = useState<string | (string | React.JSX.Element)[]>(description);
  const { t } = useTranslation();

  useEffect(() => {
    if (searchText?.length === 0 || description?.trim() === t('Common.NotApplicable')) {
      setMarkup(description);
      return;
    }

    const output = [];
    let index = 0;

    do {
      const phraseIndex = description?.toLowerCase().indexOf(searchText?.toLowerCase(), index);

      if (phraseIndex === -1) {
        const remainder = description?.substring(index);
        output.push(remainder);
        break;
      }

      const textUntilMatch = description?.substring(index, phraseIndex);
      output.push(textUntilMatch);

      const styles = {
        highlighText: {
          backgroundColor: '#FBC618',
        },
      };
      output.push(
        <span style={styles.highlighText} key={phraseIndex}>
          {description?.substring(phraseIndex, phraseIndex + searchText?.length)}
        </span>
      );

      index = phraseIndex + searchText?.length;
    } while (index < description?.length);
    setMarkup(output);
  }, [description, searchText, t]);

  return markup;
};

export default useHighlightText;
