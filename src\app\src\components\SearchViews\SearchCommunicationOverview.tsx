import React, { FC, useState } from 'react';
import { useTranslation } from 'react-i18next';
import components from './SearchCommunicationOverview.styles';
import { toDateTimeFormat } from 'shared/services/date-time-format';
import commentCommentStyles from './SearchCommentDetails.styles';
import { SearchCommunicationOverviewProps } from './search-view-types';
import { ApplicationModel, ProductDtoModel, SubmissionModel } from 'axon-hacomms-api-sdk';
import SearchCommentsPerProductComponent from './SearchCommentsPerProduct';

const SearchCommunicationOverviewComponent: FC<SearchCommunicationOverviewProps> = (props: SearchCommunicationOverviewProps) => {
  const { communication } = props;
  const { t } = useTranslation();
  const {
    SearchCommunicationOverviewContainer,
    SearchOverviewTitle,
    SearchCommunicationOverview,
    ApplicationNumbersContainer,
    SubmissionNumbersContainer,
    ApplicationNumberWrapper,
    ApplicationLabelWrapper,
    ValueWrapper,
  } = components;
  const { <PERSON>ement<PERSON>ontainer, LabelWrapper, TextWrapper, ProductsWrapper, BoldProduct } = commentCommentStyles;
  const [slideoutOpen, setSlideoutOpen] = useState<boolean>(false);
  const [selectedProduct, setSelectedProduct] = useState<ProductDtoModel | undefined>();

  const openSlideOut = (product: ProductDtoModel) => {
    setSelectedProduct(product);
    setSlideoutOpen(true);
  };

  return (
    <SearchCommunicationOverviewContainer>
      <SearchOverviewTitle>{t('SearchCommunicationOverview.OverviewLabel')}</SearchOverviewTitle>
      <SearchCommunicationOverview>
        {communication?.applications?.map((app: ApplicationModel) => {
          return (
            <ApplicationNumbersContainer key={app.number}>
              <ApplicationNumberWrapper>
                <ApplicationLabelWrapper>{t('SearchCommunicationOverview.ApplicationNumberLabel')}</ApplicationLabelWrapper>
                <ValueWrapper>{app.number ?? ''}</ValueWrapper>
              </ApplicationNumberWrapper>
              <SubmissionNumbersContainer>
                <ApplicationLabelWrapper>{t('SearchCommunicationOverview.SubmissionNumberLabel')}</ApplicationLabelWrapper>
                {app.submissions && app.submissions?.length > 0
                  ? app.submissions?.map((sub: SubmissionModel) => {
                      return <ValueWrapper key={sub.number}>{sub.number ?? ''}</ValueWrapper>;
                    })
                  : t('Common.NotApplicable')}
              </SubmissionNumbersContainer>
            </ApplicationNumbersContainer>
          );
        })}
        <ElementContainer>
          <LabelWrapper>{t('SearchCommunicationOverview.SubjectLabel')}</LabelWrapper>
          <TextWrapper>{communication?.subject ?? ''}</TextWrapper>
        </ElementContainer>
        <ElementContainer>
          <LabelWrapper>{t('SearchCommunicationOverview.CountryLabel')}</LabelWrapper>
          <TextWrapper>{communication?.country ?? ''}</TextWrapper>
        </ElementContainer>
        <ElementContainer>
          <LabelWrapper>{t('SearchCommunicationOverview.SubmissionTypeLabel')}</LabelWrapper>
          <TextWrapper>{communication?.submissionType ?? ''}</TextWrapper>
        </ElementContainer>
        <ElementContainer>
          <LabelWrapper>{t('SearchCommunicationOverview.DateOfCommunicationLabel')}</LabelWrapper>
          <TextWrapper>{toDateTimeFormat(communication?.dateOfCommunication) ?? ''}</TextWrapper>
        </ElementContainer>
        {communication?.containsGeneralGuidanceComments && (
          <ElementContainer>
            <LabelWrapper>{t('SearchCommunicationOverview.GeneralGuidanceCommentsLabel')}</LabelWrapper>
            <TextWrapper>
              <ProductsWrapper onClick={() => openSlideOut({ id: undefined, name: t('Common.GeneralGuidance') })}>
                {t('Common.GeneralGuidance')}
              </ProductsWrapper>
            </TextWrapper>
          </ElementContainer>
        )}
        {communication?.allProducts && communication?.allProducts.length > 0 && (
          <ElementContainer>
            <LabelWrapper>{t('SearchCommunicationOverview.ProductsLabel')}</LabelWrapper>
            <TextWrapper>
              {communication?.allProducts?.map((product: ProductDtoModel) => {
                return (
                  <ProductsWrapper key={product.id} onClick={() => openSlideOut({ id: product.id, name: product.name })}>
                    {communication.comment?.productName == product.name ? <BoldProduct>{product.name}</BoldProduct> : product.name}
                  </ProductsWrapper>
                );
              })}
            </TextWrapper>
          </ElementContainer>
        )}
      </SearchCommunicationOverview>

      {slideoutOpen && (
        <SearchCommentsPerProductComponent
          tenant={props.tenant}
          communicationId={communication?.id}
          isOpen={slideoutOpen}
          product={selectedProduct}
          onClose={() => {
            setSelectedProduct(undefined);
            setSlideoutOpen(false);
          }}
        ></SearchCommentsPerProductComponent>
      )}
    </SearchCommunicationOverviewContainer>
  );
};

export default SearchCommunicationOverviewComponent;
