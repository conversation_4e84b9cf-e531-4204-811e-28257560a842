/* eslint-disable @typescript-eslint/no-var-requires */
import HtmlWebpackPlugin from 'html-webpack-plugin';
import ForkTsCheckerWebpackPlugin from 'fork-ts-checker-webpack-plugin';
import ESLintPlugin from 'eslint-webpack-plugin';
import TsconfigPathsPlugin from 'tsconfig-paths-webpack-plugin';
import CopyWebpackPlugin from 'copy-webpack-plugin';

const ModuleFederationPlugin = require('webpack').container.ModuleFederationPlugin;
import paths from './config/paths';
const deps = require('./package.json').dependencies;

module.exports = () => {
  return {
    mode: 'production',
    devtool: 'source-map',
    entry: [paths.appIndexJs].filter(Boolean),
    output: {
      filename: 'apps/hacomms/bundle.js',
      clean: true,
    },
    module: {
      rules: [
        {
          test: /\.(ts|js)x?$/i,
          exclude: /node_modules/,
          use: {
            loader: 'babel-loader',
            options: {
              presets: ['@babel/preset-env', '@babel/preset-react', '@babel/preset-typescript'],
            },
          },
        },
        {
          test: /\.css?$/i,
          use: ['style-loader', 'css-loader'],
        },
        {
          test: /\.(png|gif|woff|woff2|eot|ttf|svg)$/,
          loader: 'url-loader',
        },
      ],
    },
    resolve: {
      plugins: [new TsconfigPathsPlugin()],
      extensions: ['.tsx', '.ts', '.js', '.jsx'],
    },
    plugins: [
      new ModuleFederationPlugin({
        name: 'hacomms',
        filename: 'remoteEntry.js',
        exposes: {
          './App': './src/App',
          './Navigation': './src/Navigation',
        },
        shared: {
          react: {
            requiredVersion: deps.react,
            import: 'react', // the "react" package will be used a provided and fallback module
            shareKey: 'react', // under this name the shared module will be placed in the share scope
            shareScope: 'default', // share scope with this name will be used
            singleton: true, // only a single version of the shared module is allowed
          },
          'react-dom': {
            requiredVersion: deps['react-dom'],
            singleton: true, // only a single version of the shared module is allowed
          },
          'styled-components': {
            singleton: true, // only a single version of the shared module is allowed
          },
          'react-router': {
            singleton: true, // only a single version of the shared module is allowed
          },
          'react-router-dom': {
            singleton: true, // only a single version of the shared module is allowed
          },
        },
      }),
      new HtmlWebpackPlugin({
        template: paths.appHtml,
      }),
      new ForkTsCheckerWebpackPlugin({
        async: false,
      }),
      new ESLintPlugin({
        extensions: ['js', 'jsx', 'ts', 'tsx'],
        failOnError: true,
        failOnWarning: true,
      }),
      new CopyWebpackPlugin({
        patterns: [{ from: './assets', to: 'assets' }],
      }),
    ],
  };
};
