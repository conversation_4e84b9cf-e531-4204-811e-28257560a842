import { AxonMultiColumnDropdown } from 'components/AxonMultiColumnDropdown';
import { CommunicationCommentProductExtensionsProps, defaultProductExtension } from '../communication-form-types';
import components from './CommunicationCommentProductExtensions.styles';
import React, { FC, useContext, useEffect, useMemo, useState } from 'react';
import { PhlexConfirmationDialog, PhlexIcon, PhlexIconButton, PhlexLayout } from 'phlex-core-ui';
import { useTranslation } from 'react-i18next';
import CommunicationContext from 'context/CommunicationContext';
import { MultiselectData } from 'components/shared/types/types';
import { AxonMultiSelect } from 'components/AxonMultiSelect';
import { FilterDescriptor, filterBy } from '@progress/kendo-data-query';
import { ComboBoxChangeEvent, ComboBoxFilterChangeEvent, MultiSelectChangeEvent } from '@progress/kendo-react-dropdowns';
import { useFieldArray } from 'react-hook-form';
import { IOption } from 'phlex-core-ui/build/src/controls/PhlexLazyMultiSelect/PhlexLazyMultiSelect';

const CommunicationCommentProductExtensionsComponent: FC<CommunicationCommentProductExtensionsProps> = (
  props: CommunicationCommentProductExtensionsProps
) => {
  const { commentFieldIndex, productExtensionTableValues, isCommentInEditMode, isCommunicationCompleted, methods } = props;

  const { fields, append, remove, update } = useFieldArray({
    control: methods.control,
    name: `comments.${commentFieldIndex}.productExtensions`,
    keyName: 'key',
  });

  const [productExtensionsFilters, setProductExtensionsFilters] = useState<FilterDescriptor[]>();
  const [activeViewIndex, setActiveViewIndex] = useState<number>(0);
  const [newProductExtensionsAdded, setNewProductExtensionsAdded] = useState<boolean>(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState<boolean>(false);
  const { t } = useTranslation();
  const {
    DosageFormContainer,
    LabelWrapper,
    TextWrapper,
    SectionWrapper,
    ProductExtensionContainer,
    ScrollViewWrapper,
    PagingInfo,
    PagingInfoWrapper,
    ActionsWrapper,
    LeftArrowContainer,
    RightArrowContainer,
    ProductExtensionWrapper,
    ErrorWrapper,
  } = components;

  const { InputLabel } = PhlexLayout;
  const { selectedProduct } = useContext(CommunicationContext);
  const productExtensionColumns = useMemo(
    () => [
      { field: 'pcid', header: t('ManageCommunication.ProductCodeField'), width: '150px' },
      { field: 'dosageFormName', header: t('ManageCommunication.DosageFormField'), width: '300px' },
      { field: 'routeOfAdministrationList', header: t('ManageCommunication.RouteOfAdministrationField'), width: '300px' },
    ],
    [t]
  );

  useEffect(() => {
    if (newProductExtensionsAdded) {
      setActiveViewIndex(fields?.length - 1);
      setNewProductExtensionsAdded(false);
    }
  }, [newProductExtensionsAdded, fields]);

  const getRoutesOfAdministration = (productExtensionId: number) => {
    const productExtension = selectedProduct?.productExtensions?.filter((pe) => pe.id === productExtensionId)[0];
    return productExtension?.routesOfAdministration?.map((r) => ({ label: r?.name ?? '', value: r?.id?.toString() ?? '' })) ?? [];
  };

  const fetchRoutesOfAdministration = (productExtensionId: number): Promise<MultiselectData> => {
    return Promise.resolve(getRoutesOfAdministration(productExtensionId));
  };

  const getProductExtensionsTableData = (index: number) => {
    if (fields?.[index]?.id && productExtensionTableValues.map((pe) => pe.id).indexOf(fields?.[index].id) === -1) {
      const data = [...productExtensionTableValues];
      data.splice(0, 0, fields?.[index]);

      return productExtensionsFilters?.[index] ? filterBy(data, productExtensionsFilters[index]) : data;
    }

    return productExtensionsFilters?.[index]
      ? filterBy(productExtensionTableValues, productExtensionsFilters[index])
      : productExtensionTableValues;
  };

  const getProductExtensionsTableDataValue = (index: number) => {
    if (!productExtensionTableValues || productExtensionTableValues.length === 0 || !fields?.[index]) return undefined;
    return productExtensionTableValues.filter((p) => p.id === fields?.[index].id)[0];
  };

  const handleFilterChange = (index: number) => {
    return (e: ComboBoxFilterChangeEvent) => {
      const filters = productExtensionsFilters;
      if (!filters) {
        setProductExtensionsFilters([{ ...e.filter }]);
        return;
      }

      filters[index] = e.filter;
      setProductExtensionsFilters({ ...filters });
    };
  };

  const onProductExtensionChanged = (e: ComboBoxChangeEvent, index: number) => {
    methods.clearErrors(`comments.${commentFieldIndex}.productExtensions`);
    update(index, e.value ?? defaultProductExtension);
  };

  const onRoutesOfAdministrationChanged = (x: IOption[], e: MultiSelectChangeEvent, index: number) => {
    // WHEN the user deselects all of the associated RoA, THEN the system deselects the entire product extension
    if (e.value.length === 0) {
      update(index, defaultProductExtension);
    }
  };

  const handleAddNewProductExtension = () => {
    methods.clearErrors(`comments.${commentFieldIndex}.productExtensions`);
    append(defaultProductExtension);
    setNewProductExtensionsAdded(true);
  };

  const deleteProductExtension = () => {
    methods.clearErrors(`comments.${commentFieldIndex}.productExtensions`);
    remove(activeViewIndex);
    setActiveViewIndex(0);
    setShowDeleteDialog(false);
  };

  const handleDeleteProductExtension = () => {
    setShowDeleteDialog(true);
  };

  const hasProductExtensionErrors = async () => {
    await methods.trigger(`comments.${commentFieldIndex}.productExtensions.${activeViewIndex}`);
    return methods.formState.errors.comments?.[commentFieldIndex]?.productExtensions?.[activeViewIndex] != undefined;
  };

  const handleLeftArrowClick = async () => {
    if (await hasProductExtensionErrors()) return;
    setActiveViewIndex(activeViewIndex - 1);
  };

  const handleRightArrowClick = async () => {
    if (await hasProductExtensionErrors()) return;
    setActiveViewIndex(activeViewIndex + 1);
  };

  return (
    <ScrollViewWrapper>
      <ActionsWrapper>
        <PhlexIconButton
          buttonType="add"
          title={t('Common.AddTooltipLabel')}
          onClick={(e: any) => {
            e.preventDefault();
            handleAddNewProductExtension();
          }}
          disabled={isCommunicationCompleted || !isCommentInEditMode || fields.map((p) => p?.id).lastIndexOf(0) > -1}
        />
        <PhlexIconButton
          buttonType="delete"
          feedbackStatus="none"
          title={t('Common.DeleteTooltipLabel')}
          onClick={(e: any) => {
            e.preventDefault();
            handleDeleteProductExtension();
          }}
          disabled={isCommunicationCompleted || !isCommentInEditMode || (fields && fields.length == 1)}
        />
      </ActionsWrapper>
      {methods.formState.errors.comments?.[commentFieldIndex]?.productExtensions?.message && (
        <ErrorWrapper>{methods.formState.errors.comments?.[commentFieldIndex]?.productExtensions?.message}</ErrorWrapper>
      )}
      {fields?.map((field, index) => (
        <ProductExtensionWrapper key={`${field.key}`} className={activeViewIndex != index ? 'scrollview-inactive' : 'scrollview-active'}>
          <ProductExtensionContainer>
            {productExtensionTableValues && productExtensionTableValues.length > 0 && (
              <AxonMultiColumnDropdown
                data-testid={`axon-input-product-with-comments.comments.productExtension_${commentFieldIndex}`}
                name={`comments[${commentFieldIndex}].productExtensions[${index}].id`}
                label={t('ManageCommunication.ProductExtension')}
                width="fullwidth"
                data={getProductExtensionsTableData(index)}
                value={getProductExtensionsTableDataValue(index)}
                defaultValue={getProductExtensionsTableDataValue(index)}
                columns={productExtensionColumns}
                textField="pcid"
                required={true}
                filterable={true}
                dataItemKey="id"
                onFilterChange={handleFilterChange(index)}
                placeholder={t('ManageCommunication.SelectProductExtension')}
                validationMessage={methods.formState.errors.comments?.[commentFieldIndex]?.productExtensions?.[index]?.message ?? undefined}
                onChange={(e) => onProductExtensionChanged(e, index)}
                disabled={isCommunicationCompleted || !isCommentInEditMode}
              />
            )}
            <DosageFormContainer>
              <LabelWrapper>
                <InputLabel>{t('Common.DosageFormLabel')}</InputLabel>
              </LabelWrapper>
              <TextWrapper>{field?.dosageFormName}</TextWrapper>
            </DosageFormContainer>
            <SectionWrapper>
              <AxonMultiSelect
                data-testid={`axon-input-product-with-comments.comments.route-of-admin_${commentFieldIndex}`}
                name={`comments[${commentFieldIndex}].productExtensions[${index}].routeOfAdministrations`}
                label={t('Common.RouteOfAdminLabel') || ''}
                defaultValues={field?.routeOfAdministrations}
                value={field?.routeOfAdministrations}
                width="fullwidth"
                required={true}
                fetchEveryTime={true}
                fetchData={() => fetchRoutesOfAdministration(field?.id ?? 0)}
                validationMessage={
                  methods.formState.errors.comments?.[commentFieldIndex]?.productExtensions?.[index]?.routeOfAdministrations?.message ??
                  undefined
                }
                disabled={
                  field == undefined ||
                  getRoutesOfAdministration(field.id ?? 0).length === 1 ||
                  isCommunicationCompleted ||
                  !isCommentInEditMode
                }
                onChange={(x, e) => onRoutesOfAdministrationChanged(x, e, index)}
              />
            </SectionWrapper>
          </ProductExtensionContainer>
          <PagingInfoWrapper>
            <LeftArrowContainer hidden={activeViewIndex < 1} onClick={() => handleLeftArrowClick()}>
              <PhlexIcon color="brand" name="arrow_left" size="large" />
            </LeftArrowContainer>
            <PagingInfo>
              {index + 1} of {fields.length}
            </PagingInfo>
            <RightArrowContainer
              hidden={activeViewIndex >= (fields?.length != undefined ? fields?.length - 1 : 0)}
              onClick={() => handleRightArrowClick()}
            >
              <PhlexIcon color="brand" name="arrow_right" size="large" />
            </RightArrowContainer>
          </PagingInfoWrapper>
        </ProductExtensionWrapper>
      ))}

      <PhlexConfirmationDialog
        isOpen={showDeleteDialog}
        onConfirm={() => deleteProductExtension()}
        onCancel={() => setShowDeleteDialog(false)}
        heading={t('CommunicationModals.DeleteProductExtensionHeading')}
        description={t('CommunicationModals.DeleteProductExtensionDescription')}
      />
    </ScrollViewWrapper>
  );
};

export default CommunicationCommentProductExtensionsComponent;
