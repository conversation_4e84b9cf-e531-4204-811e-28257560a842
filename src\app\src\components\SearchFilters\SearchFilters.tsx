import React, { FC, useEffect, useState } from 'react';
import DrugProductsFilter from './DrugProductsFilter';
import CommunicationDatesFilter from './CommunicationDatesFilter';
import SubmissionNumbersFilter from './SubmissionNumbersFilter';
import SubmissionTypesFilter from './SubmissionTypesFilter';
import ApplicationNumbersFilter from './ApplicationNumbersFilter';
import { SearchFiltersProps } from './search-filter-types';
import DosageFormsFilter from './DosageFormsFilter';
import CountriesFilter from './CountriesFilter';
import RouteOfAdministrationsFilter from './RouteOfAdministrationsFilter';
import ProductCodesFilter from './ProductCodesFilter';
import DrugSubstancesFilter from './DrugSubstancesFilter';
import TextFilter from './TextFilter';
import TagsFilterComponent from './TagsFilter';
import IsQuestionIncludedFilterComponent from './IsQuestionIncludedFilter';
import GeneralGuidanceFilterComponent from './GeneralGuidanceFilter';
import { ClearFilters } from 'components/Content/Content.styles';
import { useTranslation } from 'react-i18next';
import { removeFiltersFromStorage } from './search-filter-service';
import { PhlexLayout } from 'phlex-core-ui';
import ProductTypesFilterComponent from './ProductTypesFilter';

const SearchFiltersComponent: FC<SearchFiltersProps> = (props: SearchFiltersProps) => {
  const { tenant, setSearchModel } = props;
  const [disable, setDisable] = useState<boolean>(false);
  const [clear, setClear] = useState<boolean>(false);
  const { t } = useTranslation();
  const { FilterPanel } = PhlexLayout;

  const clearFilters = () => {
    setClear(true);
    setSearchModel({
      skip: 0,
      take: 10,
    });
    removeFiltersFromStorage();
    setDisable(false);
  };

  useEffect(() => {
    if (clear) {
      const timer = setTimeout(() => setClear(false));
      return () => clearTimeout(timer);
    }
  }, [clear]);

  return (
    <FilterPanel>
      <TextFilter tenant={tenant} setSearchModel={setSearchModel} clear={clear} />
      <CommunicationDatesFilter tenant={tenant} setSearchModel={setSearchModel} clear={clear} />
      <DrugProductsFilter tenant={tenant} setSearchModel={setSearchModel} disable={disable} clear={clear} />
      <DosageFormsFilter tenant={tenant} setSearchModel={setSearchModel} disable={disable} clear={clear} />
      <RouteOfAdministrationsFilter tenant={tenant} setSearchModel={setSearchModel} disable={disable} clear={clear} />
      <DrugSubstancesFilter tenant={tenant} setSearchModel={setSearchModel} disable={disable} clear={clear} />
      <CountriesFilter tenant={tenant} setSearchModel={setSearchModel} clear={clear} />
      <SubmissionNumbersFilter tenant={tenant} setSearchModel={setSearchModel} clear={clear} />
      <SubmissionTypesFilter tenant={tenant} setSearchModel={setSearchModel} clear={clear} />
      <ApplicationNumbersFilter tenant={tenant} setSearchModel={setSearchModel} clear={clear} />
      <ProductCodesFilter tenant={tenant} setSearchModel={setSearchModel} disable={disable} clear={clear} />
      <TagsFilterComponent tenant={tenant} setSearchModel={setSearchModel} clear={clear} />
      <ProductTypesFilterComponent tenant={tenant} setSearchModel={setSearchModel} clear={clear} />
      <IsQuestionIncludedFilterComponent tenant={tenant} setSearchModel={setSearchModel} clear={clear} />
      <GeneralGuidanceFilterComponent tenant={tenant} setSearchModel={setSearchModel} setDisable={setDisable} clear={clear} />
      <ClearFilters name="filter_alt_off" clickHandler={clearFilters} tooltip={t('Common.ClearAllFitlers')} />
    </FilterPanel>
  );
};

export default SearchFiltersComponent;
