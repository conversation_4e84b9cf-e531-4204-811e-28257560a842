import { useOrganisationFromUrl } from 'hooks/shared';
import { useSelector } from 'react-redux';
import { RootState, useAppDispatch } from 'store';
import slice, { getPermissionsAsync, PermissionsStatus } from './permissionsSlice';

export interface IHacommsPermissionsStore {
  permissionsStatus: PermissionsStatus;
  hacommsPermissions: string[];
}

export interface IHacommsPermissionsState extends IHacommsPermissionsStore {
  actions: {
    getAllPermissions: () => void;
  };
}

export const useHacommsPermissionsStore = (): IHacommsPermissionsState => {
  const tenant = useOrganisationFromUrl();
  const dispatch = useAppDispatch();
  const getAllPermissionsDispatch = () => dispatch(getPermissionsAsync({ tenant }));
  const { permissionsStatus, hacommsPermissions } = useSelector((state: RootState) => state.hacommsPermissions);

  const getAllPermissions = () => {
    getAllPermissionsDispatch().catch(() => {
      console.error('Error getting permissions.');
    });
  };

  return {
    permissionsStatus,
    hacommsPermissions,
    actions: { getAllPermissions },
  };
};

export default {
  name: slice ? slice.name : 'hacomms_permissions',
  reducer: slice,
};
