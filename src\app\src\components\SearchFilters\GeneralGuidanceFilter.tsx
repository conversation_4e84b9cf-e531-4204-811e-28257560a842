import React, { FC, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { SearchFiltersProps } from './search-filter-types';
import { SearchCommentCommandRequest } from 'axon-hacomms-api-sdk';
import { getFiltersFromStorage, updateGeneralGuidanceInStorage } from './search-filter-service';
import { DropDownListChangeEvent } from '@progress/kendo-react-dropdowns';
import { PhlexDropdown } from 'phlex-core-ui';

const GeneralGuidanceFilterComponent: FC<SearchFiltersProps> = (props: SearchFiltersProps) => {
  const { setSearchModel, setDisable, clear } = props;
  const { t } = useTranslation();
  const [selectedGuidanceOption, setSelectedGuidanceOption] = useState<boolean | undefined | null>(undefined);

  const generalGuidanceOptions = [
    { id: 1, value: null, label: t('SearchFilters.GeneralGuidanceFilterOptions.All') },
    { id: 2, value: true, label: t('SearchFilters.GeneralGuidanceFilterOptions.GeneralGuidance') },
    { id: 3, value: false, label: t('SearchFilters.GeneralGuidanceFilterOptions.NongeneralGuidance') },
  ];

  const updateSearchModel = (data: boolean | null) => {
    setSearchModel((request: SearchCommentCommandRequest) => ({
      ...request,
      isGeneralGuidance: data ?? undefined,
      products: data ? null : request.products,
      productCodes: data ? null : request.productCodes,
      dosageForms: data ? null : request.dosageForms,
      routesOfAdministration: data ? null : request.routesOfAdministration,
      drugSubstances: data ? null : request.drugSubstances,
      skip: 0,
      take: 10,
    }));
    if (setDisable) {
      setDisable(data ?? false);
    }
  };

  const onGeneralGuidanceFilterChange = (data: DropDownListChangeEvent) => {
    const selectedOption = data.value.value;
    setSelectedGuidanceOption(selectedOption);
    updateGeneralGuidanceInStorage(selectedOption);
    updateSearchModel(selectedOption);
  };

  useEffect(() => {
    const searchFilters = getFiltersFromStorage();
    if (searchFilters?.generalGuidance != undefined) {
      setSelectedGuidanceOption(searchFilters.generalGuidance);
      updateSearchModel(searchFilters.generalGuidance);
    } else {
      setSelectedGuidanceOption(undefined);
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (clear) setSelectedGuidanceOption(undefined);
  }, [clear]);

  return (
    <PhlexDropdown
      data-testid={'axon-input-search-filter-generalGuidance'}
      name={`generalGuidanceSearchFilter`}
      label={t('SearchFilters.GeneralGuidanceFilterOptions.Label')}
      value={generalGuidanceOptions.filter((x) => x.value == selectedGuidanceOption)[0]}
      width="fullwidth"
      data={generalGuidanceOptions}
      textField="label"
      dataItemKey="id"
      onChange={onGeneralGuidanceFilterChange}
    />
  );
};

export default GeneralGuidanceFilterComponent;
