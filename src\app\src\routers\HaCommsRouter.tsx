import React, { ReactNode } from 'react';
import { Navigate, Route, Routes } from 'react-router-dom';
import SearchPage from 'pages/Search';
import ManageSubstancesPage from 'pages/Substances';
import ManageRoutesOfAdministrationPage from 'pages/RoutesOfAdministration';
import ManageDosageFormPage from 'pages/DosageForms';
import ProductsPage from 'pages/Products';
import CommunicationsTabs from 'pages/Communications';
import HaCommsRoute from './HaCommsRoute';
import { routes } from 'pages';
import ManageProductPage from 'pages/Products/ManageProduct/ManageProductPage';
import ManageCommunicationPage from 'pages/Communications/ManageCommunication/ManageCommunicationPage';
import ManageTagsPage from 'pages/Tags';
import SearchCommentView from 'pages/Search/SearchCommentView/SearchCommentView';
import { ThemesExample } from 'components/ThemesExample/ThemesExample';
import NotFoundPage from 'pages/NotFound/notFound';
import ForbiddenPage from 'pages/Forbidden/ForbiddenPage';
import AppErrorPage from 'pages/AppError/appError';
import { BasePathType } from './types';

const HaCommsRouter = ({ basepath }: BasePathType): ReactNode => {
  return (
    <Routes>
      <Route
        path={routes.home}
        element={
          <HaCommsRoute>
            <SearchPage basepath={basepath}></SearchPage>
          </HaCommsRoute>
        }
      ></Route>
      <Route
        path={routes.search}
        element={
          <HaCommsRoute>
            <SearchPage basepath={basepath}></SearchPage>
          </HaCommsRoute>
        }
      ></Route>
      <Route
        path={routes.substances}
        element={
          <HaCommsRoute>
            <ManageSubstancesPage basepath={basepath}></ManageSubstancesPage>
          </HaCommsRoute>
        }
      ></Route>
      <Route
        path={routes.routesOfAdministration}
        element={
          <HaCommsRoute>
            <ManageRoutesOfAdministrationPage basepath={basepath}></ManageRoutesOfAdministrationPage>
          </HaCommsRoute>
        }
      ></Route>
      <Route
        path={routes.tags}
        element={
          <HaCommsRoute>
            <ManageTagsPage basepath={basepath}></ManageTagsPage>
          </HaCommsRoute>
        }
      ></Route>
      <Route
        path={routes.dosageForms}
        element={
          <HaCommsRoute>
            <ManageDosageFormPage basepath={basepath}></ManageDosageFormPage>
          </HaCommsRoute>
        }
      ></Route>
      <Route
        path={routes.products}
        element={
          <HaCommsRoute>
            <ProductsPage basepath={basepath}></ProductsPage>
          </HaCommsRoute>
        }
      ></Route>
      <Route
        path={routes.createProduct}
        element={
          <HaCommsRoute>
            <ManageProductPage basepath={basepath}></ManageProductPage>
          </HaCommsRoute>
        }
      ></Route>
      <Route
        path={routes.updateProduct()}
        element={
          <HaCommsRoute>
            <ManageProductPage basepath={basepath}></ManageProductPage>
          </HaCommsRoute>
        }
      ></Route>
      <Route
        path={routes.communications}
        element={
          <HaCommsRoute>
            <CommunicationsTabs basepath={basepath}></CommunicationsTabs>
          </HaCommsRoute>
        }
      ></Route>
      <Route
        path={routes.updateCommunication()}
        element={
          <HaCommsRoute>
            <ManageCommunicationPage basepath={basepath}></ManageCommunicationPage>
          </HaCommsRoute>
        }
      ></Route>
      <Route
        path={routes.createCommunication}
        element={
          <HaCommsRoute>
            <ManageCommunicationPage basepath={basepath}></ManageCommunicationPage>
          </HaCommsRoute>
        }
      ></Route>
      <Route
        path={routes.viewComment}
        element={
          <HaCommsRoute>
            <SearchCommentView basepath={basepath}></SearchCommentView>
          </HaCommsRoute>
        }
      ></Route>
      <Route
        path={routes.theme}
        element={
          <HaCommsRoute>
            <ThemesExample></ThemesExample>
          </HaCommsRoute>
        }
      ></Route>
      <Route
        path={routes.notFound}
        element={
          <HaCommsRoute>
            <NotFoundPage basepath={basepath}></NotFoundPage>
          </HaCommsRoute>
        }
      ></Route>
      <Route
        path={routes.forbidden}
        element={
          <HaCommsRoute>
            <ForbiddenPage basepath={basepath}></ForbiddenPage>
          </HaCommsRoute>
        }
      ></Route>
      <Route
        path={routes.error}
        element={
          <HaCommsRoute>
            <AppErrorPage basepath={basepath}></AppErrorPage>
          </HaCommsRoute>
        }
      ></Route>
      <Route path="/*" element={<Navigate to={`${basepath}${routes.error}`} />} />
    </Routes>
  );
};

export default HaCommsRouter;
