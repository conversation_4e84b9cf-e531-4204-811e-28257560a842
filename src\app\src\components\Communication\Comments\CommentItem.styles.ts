import styled from 'styled-components';

export default {
  CommentWrapper: styled.div`
    background-color: ${(props) => props.theme.colors.backgroundPri};
    border-radius: ${(props) => props.theme.radius};
    box-shadow: ${(props) => props.theme.shadow};
    padding-left: 2rem;
    padding-right: 1.5rem;
    padding-top: 1.25rem;
    padding-bottom: 1.5rem;
    flex: 1;
  `,
  CommentContainer: styled.div`
    display: flex;
    gap: 1.5rem;
    position: relative;
    flex: 1;
  `,
  WideColumn: styled.div`
    display: flex;
    flex: 1;
    flex-direction: column;
    gap: 1rem;
    line-height: ${(props) => props.theme.lineHeightHeading};
  `,
  LinkContainer: styled.div`
    position: relative;
    width: 100%;
    display: flex;
    align-items: start;
    padding-top: ${(props) => props.theme.spaceM};
  `,
  InfoSection: styled.div`
    font-size: ${(props) => props.theme.textXS};
  `,
  SectionBorder: styled.div`
    border-bottom: ${({ theme }) => `${theme.border} ${theme.colors.borderPri}`};
    box-shadow: ${({ theme }) => theme.shadow};
    margin-top: 1rem;
    margin-bottom: 0.5rem;
  `,
};
