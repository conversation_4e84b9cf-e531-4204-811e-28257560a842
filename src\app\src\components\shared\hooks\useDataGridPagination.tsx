import { useEffect, useState } from 'react';
import { GridPageChangeEvent } from '@progress/kendo-react-grid';
import { AxiosPromise, AxiosError } from 'axios';
import { CompositeFilterDescriptor, FilterDescriptor, SortDescriptor } from '@progress/kendo-data-query';
import { useOrganisationFromUrl } from 'hooks/shared';
import { useNavigate } from 'react-router-dom';
import { routes } from 'pages';

export interface BaseApiPaging<T> {
  data: T[];
  total: number;
}

export interface Pagination<T> extends BaseApiPaging<T> {
  skip: number;
  take: number;
  filter: string;
  orderBy: string;
  isLoading?: boolean;
  refresh?: boolean;
  cleanBasePath: string;
}

type UseDataGridPaginationReturn<ST> = [
  ST,
  (event: GridPageChangeEvent) => void,
  (filter: CompositeFilterDescriptor) => void,
  (sort: SortDescriptor[]) => void
];

const useDataGridPagination = <T, RT = BaseApiPaging<T>, ST extends Pagination<T> = Pagination<T>, ET extends T = T>(
  fetch: (skip: number, take: number, filter: string, orderBy: string, tenant: string) => AxiosPromise<RT>,
  initialState: ST,
  customFetchResponseMapper?: (data: RT) => ST,
  extendModel?: (data: T[]) => ET[]
): UseDataGridPaginationReturn<ST> => {
  const [pagination, setPagination] = useState<ST>(initialState);
  const [requestFilter, setRequestFilter] = useState('');
  const [requestOrder, setRequestOrder] = useState('');
  const tenant = useOrganisationFromUrl();
  const navigate = useNavigate();

  const onPageChange = (event: GridPageChangeEvent) => {
    setPagination((pv: ST) => ({
      ...pv,
      take: event.page.take,
      skip: event.page.skip,
      isLoading: true,
    }));
  };

  const defaultMapping = (inputData: Pagination<T>): BaseApiPaging<T> => ({
    data: !extendModel ? inputData.data : extendModel(inputData.data),
    total: inputData.total,
  });

  const onSortChange = (sort: SortDescriptor[]) => {
    if (sort && sort.length) {
      setRequestOrder(`${sort[0].field}=>${sort[0].dir}`);
      setPagination((pv: ST) => ({ ...pv, filter: `${requestFilter}`, orderBy: `${sort[0].field}=>${sort[0].dir}`, skip: 0 }));
    } else {
      setRequestOrder('');
      setPagination((pv: ST) => ({ ...pv, filter: `${requestFilter}`, orderBy: '', skip: 0 }));
    }
  };

  const changeFilter = (descriptor: CompositeFilterDescriptor) => {
    const filterOperators = descriptor.filters as FilterDescriptor[];
    const filters = filterOperators
      .filter((x) => x.value)
      .map((x: FilterDescriptor) => `filters=${x.field}=>${x.value}`)
      .join('&');
    if (filters) {
      setRequestFilter(`${filters}`);
      setPagination((pv: ST) => ({ ...pv, filter: `${filters}`, orderBy: requestOrder, skip: 0 }));
    } else {
      setRequestFilter('');
      setPagination((pv: ST) => ({ ...pv, filter: '', orderBy: requestOrder, skip: 0 }));
    }
  };

  const onPageLoad = () => {
    setPagination((pv: ST) => ({
      ...pv,
      isLoading: true,
    }));

    fetch(pagination.skip, pagination.take, pagination.filter, pagination.orderBy, tenant)
      .then((res: any) => {
        const responseData = !customFetchResponseMapper
          ? defaultMapping(res.data as unknown as Pagination<T>)
          : customFetchResponseMapper(res.data);
        responseData.total = res.data.paging.totalItemCount;
        setPagination((pv: ST) => ({
          ...pv,
          ...responseData,
          isLoading: false,
        }));
      })
      .catch((error: AxiosError) => {
        setPagination((pv: ST) => ({
          ...pv,
          isLoading: false,
        }));

        if (error.response?.status === 401 || error.response?.status === 403) {
          navigate(`${pagination.cleanBasePath}${routes.forbidden}`);
        }
      });
  };

  useEffect(onPageLoad, [pagination.skip, pagination.take, pagination.filter, pagination.orderBy, pagination.refresh]); // eslint-disable-line react-hooks/exhaustive-deps

  return [pagination, onPageChange, changeFilter, onSortChange];
};

export default useDataGridPagination;
