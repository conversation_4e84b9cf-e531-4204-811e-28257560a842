import styled from 'styled-components';

export default {
  Wrapper: styled.div`
    flex: 1;
  `,
  Heading: styled.h2`
    font-size: 1.25rem;
    font-weight: 600;
    margin-top: 2rem;
    margin-bottom: 1rem;
  `,
  Container: styled.div`
    display: flex;
  `,
  ProductExtensionWrapper: styled.span`
    background-color: ${(props) => props.theme.colors.backgroundPri};
    border-radius: ${(props) => props.theme.radius};
    box-shadow: ${(props) => props.theme.shadow};
    box-sizing: border-box;
    display: flex;
    flex: 1;
    gap: 1.5rem;
    margin-bottom: 0.75rem;
    padding: 1.5rem;
  `,
  CodeColumn: styled.div`
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    width: 15rem;
  `,
  WideColumn: styled.div`
    display: flex;
    flex-direction: column;
    flex: 1;
    gap: 1.5rem;
    width: 15rem;
  `,
  SwitchColumn: styled.div``,
  DeleteColumn: styled.div`
    color: ${(props) => props.theme.colors.textInvalid};
    align-self: top;
    margin-left: 0.75rem;
    margin-top: 0.5rem;
    min-width: 1.75rem;
  `,
  CenteredContent: styled.div`
    display: flex;
    justify-content: center;
  `,
};
