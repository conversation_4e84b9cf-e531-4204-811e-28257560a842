import { yupResolver } from '@hookform/resolvers/yup';
import { AxiosResponse } from 'axios';
import { CreateDosageFormCommandResponse, DosageFormPagedListModel } from 'axon-hacomms-api-sdk';
import { AxonInput } from 'components/AxonInput';
import { FIELD_LENGTH_LIMITS, REGULAR_EXPRESSIONS } from 'constants/regexp';
import { useOrganisationFromUrl } from 'hooks/shared';
import { PhlexButton, PhlexConfirmationDialog } from 'phlex-core-ui';
import React, { FC, useState } from 'react';
import { FieldValues, FormProvider, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { toast } from 'react-toastify';
import { dosageFormApiService } from 'shared/api';
import * as yup from 'yup';
import components from './styles';

interface DosageFormModalProps extends FieldValues {
  dosageForm?: DosageFormPagedListModel;
  onClose: () => void;
  refreshTable: () => void;
}

interface DosageFormModalFormProps {
  id?: number;
  name: string;
}

const DosageFormModal: FC<DosageFormModalProps> = ({ dosageForm, onClose, refreshTable }) => {
  const { Modal, ModalHeader, ModalContent, ModalButtons, DeleteButton } = components;
  const { t } = useTranslation();
  const width = 600;
  const tenant = useOrganisationFromUrl();
  const isEdit = !!dosageForm?.id;
  const title = isEdit ? t('DosageFormsTable.UpdateModalHeading') : t('DosageFormsTable.AddModalHeading');
  const [confirmDelete, setConfirmDelete] = useState<boolean>(false);

  const createNewDosageFormSchema = yup.object().shape({
    id: yup.number(),
    name: yup
      .string()
      .trim()
      .required(t('Forms.RequiredField', { field: t('ManageDosageForms.NameLabel') }))
      .matches(new RegExp(REGULAR_EXPRESSIONS.IllegalCharacters), t('Forms.SafeCharacters'))
      .max(
        FIELD_LENGTH_LIMITS.DOSAGE_FORM_NAME,
        t('Forms.InsertMaxCharacters', { number: FIELD_LENGTH_LIMITS.DOSAGE_FORM_NAME.toString() })
      ),
  });

  const methods = useForm<DosageFormModalFormProps>({
    resolver: yupResolver(createNewDosageFormSchema),
  });

  const setValidationError = (fieldName: 'name', message: string) => {
    methods.setError(fieldName, {
      type: 'manual',
      message: message,
    });
  };

  const onSaveSuccess = (r: AxiosResponse<CreateDosageFormCommandResponse>) => {
    if (r.status === 200) {
      onClose();
      refreshTable();
      if (isEdit) {
        toast.success(t('ManageDosageForms.UpdateSucceeded'), {
          toastId: 'dosageForm-updated',
        });
      } else {
        toast.success(t('ManageDosageForms.CreateSucceeded'), {
          toastId: 'dosageForm-created',
        });
      }
    }
  };

  const onSubmit = async (values: DosageFormModalFormProps) => {
    const requestData = {
      id: dosageForm?.id ?? 0,
      name: values.name,
    };

    try {
      const response = isEdit
        ? await dosageFormApiService.updateDosageForm(tenant, requestData)
        : await dosageFormApiService.createDosageForm(tenant, requestData);
      onSaveSuccess(response);
    } catch (error: any) {
      error.response.data.errors?.Name.some((x: any) => x.includes('exists'))
        ? setValidationError('name', t('Forms.FieldAlreadyInUse', { field: t('ManageDosageForms.NameLabel') }))
        : toast.error(t('ManageDosageForms.CreateFailedErrorMessage'));
    }
  };

  const onDelete: React.MouseEventHandler<HTMLButtonElement> = (e) => {
    e.preventDefault();
    setConfirmDelete(true);
  };

  const remove = async () => {
    const id = dosageForm?.id ?? 0;
    try {
      await dosageFormApiService.deleteDosageForm(id, tenant);
      onClose();
      refreshTable();
      toast.success(t('ManageDosageForms.DeleteSucceeded'));
      setConfirmDelete(false);
    } catch (error: any) {
      toast.error(t('ManageDosageForms.DeleteFailedErrorMessage'));
    }
  };

  return (
    <Modal onClose={onClose} width={width}>
      <ModalHeader>{title}</ModalHeader>
      <FormProvider {...methods}>
        <form onSubmit={methods.handleSubmit(onSubmit)}>
          <ModalContent>
            <AxonInput
              data-testid="axon-input-dosageForm.name"
              value={dosageForm?.name ?? ''}
              name="name"
              width="fullwidth"
              required={true}
              label={t('ManageDosageForms.NameLabel')}
              validationMessage={methods.formState.errors['name']?.message?.toString() ?? undefined}
            />
          </ModalContent>
          <ModalButtons>
            {isEdit && !dosageForm?.isAssociatedToProduct && (
              <DeleteButton color="tertiary" type="button" label={t('Forms.DeleteButton')} onClick={onDelete} />
            )}
            <PhlexButton color="secondary" type="button" label={t('Forms.CancelButton')} onClick={onClose} />
            <PhlexButton
              data-testid="axon-button-dosageForm.save-button"
              label={isEdit ? t('Forms.UpdateButton') : t('Forms.AddButton')}
              type="submit"
              disabled={methods.formState.isSubmitting}
            />
          </ModalButtons>
          <PhlexConfirmationDialog
            isOpen={confirmDelete}
            heading={t('ManageDosageForms.DeleteHeading')}
            description={t('ManageDosageForms.DeleteDescription', { dosageFormName: dosageForm?.name })}
            onConfirm={() => remove()}
            onCancel={() => setConfirmDelete(false)}
            cancelButtonText={t('Forms.CancelButton')}
            confirmButtonText={t('Forms.DeleteButton')}
          />
        </form>
      </FormProvider>
    </Modal>
  );
};

export default DosageFormModal;
