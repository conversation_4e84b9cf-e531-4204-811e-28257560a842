import { ProductFormProps } from 'components/Product/product-form-types';
import { FormValidationError } from 'components/shared/types/types';

export const validateProduct = (values: ProductFormProps) => {
  const errors: FormValidationError[] = [];

  if (values.productTypes && values.productTypes.length > 1 && values.productTypes.map((x) => x.label).includes('Not Categorized')) {
    errors.push({
      fieldName: 'productTypes',
      message: 'ManageProduct.InvalidProductTypeCombination',
      label: 'Common.ProductTypesLabel',
    });
  }

  return errors;
};
