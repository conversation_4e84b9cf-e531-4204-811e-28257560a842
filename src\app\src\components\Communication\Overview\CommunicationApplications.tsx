import React, { FC, useState, useEffect, useMemo, useContext } from 'react';
import { ChipItemProps } from 'phlex-core-ui/build/src/controls/PhlexChipList/PhlexChipList';
import { CommunicationApplicationsProps } from '../communication-form-types';
import { useTranslation } from 'react-i18next';
import components from './CommunicationApplications.styles';
import { useFieldArray } from 'react-hook-form';
import CommunicationContext from 'context/CommunicationContext';

const CommunicationApplicationsComponent: FC<CommunicationApplicationsProps> = (props: CommunicationApplicationsProps) => {
  const { methods, isEditMode } = props;
  const { communication } = useContext(CommunicationContext);
  const { t } = useTranslation();
  const { CommunicationPhlexChipList, ChipListWrapper } = components;
  const [chipItemsUpdated, setChipItemsUpdated] = useState(false);
  const [selectedApplicationItemId, setSelectedApplicationItemId] = useState<string>();

  useEffect(() => {
    if (communication) {
      const applicationId = communication.applications?.[0] !== undefined ? communication.applications?.[0].id : 0;
      setSelectedApplicationItemId(applicationId?.toString());
    }
  }, [communication]);

  const { fields } = useFieldArray({
    control: methods.control,
    name: 'applications',
    keyName: 'key',
  });

  const isApplicationsDisabled = useMemo(
    () =>
      communication?.isCompleted ||
      !isEditMode ||
      (methods.formState.errors.applications?.map?.((e) => e?.submissions)?.length !== undefined &&
        methods.formState.errors.applications?.map?.((e) => e?.submissions)?.length > 0),
    [communication, isEditMode, methods.formState.errors.applications]
  );

  const isSubmissionsDisabled = useMemo(
    () => communication?.isCompleted || !isEditMode || methods.formState.errors.applications?.root?.message !== undefined,
    [communication, isEditMode, methods.formState.errors.applications?.root?.message]
  );

  const selectApplication = (item: ChipItemProps) => {
    setSelectedApplicationItemId(item.id);
  };

  const getSubmissionNumbers = (id: string) => {
    const applicationNumbers = methods.getValues('applications');
    return applicationNumbers.find((x) => x.id === id)?.submissions ?? [];
  };

  useEffect(() => {
    if (chipItemsUpdated) {
      const applicationNumbers = methods.getValues('applications');
      const selectedItemIndex = applicationNumbers.findIndex((x) => x.id === selectedApplicationItemId);
      if (applicationNumbers.length > 0 && selectedItemIndex === -1) {
        setSelectedApplicationItemId(applicationNumbers[0].id);
      }
      setChipItemsUpdated(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [chipItemsUpdated]);

  return (
    <ChipListWrapper>
      <CommunicationPhlexChipList
        name="applications"
        heading={t('ManageCommunication.Overview.ApplicationNumberLabel')}
        selectedItemId={selectedApplicationItemId}
        getValues={(data: ChipItemProps[]) => {
          const mappedApplications = data.map((item: ChipItemProps) => ({
            id: item.id,
            text: item.text,
            submissions: getSubmissionNumbers(item.id),
          }));
          methods.setValue('applications', mappedApplications, { shouldDirty: true });
          setChipItemsUpdated(true);
        }}
        clickHandler={(item: ChipItemProps) => {
          selectApplication(item);
        }}
        showAddButton={fields && fields.length < 5}
        addTooltip={t('Common.AddTooltipLabel')}
        deleteTooltip={t('Common.DeleteTooltipLabel')}
        saveTooltip={t('Common.SaveTooltipLabel')}
        undoTooltip={t('Common.UndoTooltipLabel')}
        disabled={isApplicationsDisabled}
        validationMessage={methods.formState.errors.applications?.root?.message?.toString() ?? undefined}
        noDataMsg={t('ManageCommunication.ChipListNoItemsMessage')}
      />
      {fields.map(
        (field, index) =>
          selectedApplicationItemId === field.id && (
            <CommunicationPhlexChipList
              key={`submission-${field.id}`}
              name={`applications.${index}.submissions`}
              heading={t('ManageCommunication.Overview.SubmissionNumberLabel')}
              data={field.submissions ?? []}
              getValues={(data: ChipItemProps[]) => methods.setValue(`applications.${index}.submissions`, data, { shouldDirty: true })}
              showAddButton={true}
              disableClick={true}
              disabled={isSubmissionsDisabled}
              addTooltip={t('Common.AddTooltipLabel')}
              deleteTooltip={t('Common.DeleteTooltipLabel')}
              saveTooltip={t('Common.SaveTooltipLabel')}
              undoTooltip={t('Common.UndoTooltipLabel')}
              validationMessage={methods.formState.errors.applications?.[index]?.submissions?.message?.toString() ?? undefined}
              noDataMsg={t('ManageCommunication.ChipListNoItemsMessage')}
            />
          )
      )}
    </ChipListWrapper>
  );
};
export default CommunicationApplicationsComponent;
