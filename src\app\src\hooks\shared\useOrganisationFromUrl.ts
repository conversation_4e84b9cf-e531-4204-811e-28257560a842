import { useMemo } from 'react';
import { useHref, useLocation } from 'react-router-dom';
import { configurationParams } from 'shared/api/configuration-params';

const useOrganisationFromUrl = (): string => {
  const baseUrl = configurationParams.basePath;
  const path = useHref('/');
  const { pathname } = useLocation();

  return useMemo(() => {
    if (baseUrl.includes('localhost')) {
      return path.substring(1) || 'undefined';
    } else {
      const initialPath = pathname.substring(1);
      return initialPath.substring(0, initialPath.indexOf('/')) || 'undefined';
    }
  }, [path, pathname, baseUrl]);
};

export { useOrganisationFromUrl };
