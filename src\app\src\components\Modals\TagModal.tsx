import { yupResolver } from '@hookform/resolvers/yup';
import { AxiosResponse } from 'axios';
import { CreateTagCommandResponse, TagPagedListModel } from 'axon-hacomms-api-sdk';
import { AxonInput } from 'components/AxonInput';
import { AxonTextArea } from 'components/AxonTextArea';
import { FIELD_LENGTH_LIMITS, REGULAR_EXPRESSIONS } from 'constants/regexp';
import { useOrganisationFromUrl } from 'hooks/shared';
import { PhlexButton, PhlexConfirmationDialog } from 'phlex-core-ui';
import React, { FC, useState } from 'react';
import { FieldValues, FormProvider, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { toast } from 'react-toastify';
import { tagApiService } from 'shared/api';
import * as yup from 'yup';
import components from './styles';

interface TagModalProps extends FieldValues {
  tag?: TagPagedListModel;
  onClose: () => void;
  refreshTable: () => void;
}

interface TagModalFormProps {
  id?: number;
  name: string;
  description?: string;
}

const TagModal: FC<TagModalProps> = ({ tag, onClose, refreshTable }) => {
  const { Modal, ModalHeader, ModalContent, ModalButtons, DeleteButton } = components;
  const { t } = useTranslation();
  const width = 600;
  const tenant = useOrganisationFromUrl();
  const isEdit = !!tag?.id;
  const title = isEdit ? t('TagsTable.UpdateModalHeading') : t('TagsTable.AddModalHeading');
  const [confirmDelete, setConfirmDelete] = useState<boolean>(false);

  const createNewTagSchema = yup.object().shape({
    id: yup.number(),
    name: yup
      .string()
      .trim()
      .required(t('Forms.RequiredField', { field: t('ManageTags.NameLabel') }))
      .matches(new RegExp(REGULAR_EXPRESSIONS.IllegalCharacters), t('Forms.SafeCharacters'))
      .max(FIELD_LENGTH_LIMITS.TAG_NAME, t('Forms.InsertMaxCharacters', { number: FIELD_LENGTH_LIMITS.TAG_NAME.toString() })),
    description: yup
      .string()
      .trim()
      .matches(new RegExp(REGULAR_EXPRESSIONS.IllegalCharacters), t('Forms.SafeCharacters'))
      .max(FIELD_LENGTH_LIMITS.TAG_DESCRIPTION, t('Forms.InsertMaxCharacters', { number: FIELD_LENGTH_LIMITS.TAG_DESCRIPTION.toString() })),
  });

  const methods = useForm<TagModalFormProps>({
    resolver: yupResolver(createNewTagSchema),
  });

  const setValidationError = (fieldName: 'name', message: string) => {
    methods.setError(fieldName, {
      type: 'manual',
      message: message,
    });
  };

  const onSaveSuccess = (r: AxiosResponse<CreateTagCommandResponse>) => {
    if (r.status === 200) {
      onClose();
      refreshTable();
      if (isEdit) {
        toast.success(t('ManageTags.UpdateSucceeded'), {
          toastId: 'tag-updated',
        });
      } else {
        toast.success(t('ManageTags.CreateSucceeded'), {
          toastId: 'tag-created',
        });
      }
    }
  };

  const onSubmit = async (values: TagModalFormProps) => {
    const requestData = {
      id: tag?.id ?? 0,
      name: values.name,
      description: values.description,
    };

    try {
      const response = isEdit ? await tagApiService.updateTag(tenant, requestData) : await tagApiService.createTag(tenant, requestData);
      onSaveSuccess(response);
    } catch (error: any) {
      error.response.data.errors?.Name.some((x: any) => x.includes('exists'))
        ? setValidationError('name', t('Forms.FieldAlreadyInUse', { field: t('ManageTags.NameLabel') }))
        : toast.error(t('ManageTags.CreateFailedErrorMessage'));
    }
  };

  const onDelete: React.MouseEventHandler<HTMLButtonElement> = (e) => {
    e.preventDefault();
    setConfirmDelete(true);
  };

  const remove = async () => {
    const id = tag?.id ?? 0;
    try {
      await tagApiService.deleteTag(id, tenant);
      onClose();
      refreshTable();
      toast.success(t('ManageTags.DeleteSucceeded'));
      setConfirmDelete(false);
    } catch (error: any) {
      toast.error(t('ManageTags.DeleteFailedErrorMessage'));
    }
  };

  return (
    <Modal onClose={onClose} width={width}>
      <ModalHeader>{title}</ModalHeader>
      <FormProvider {...methods}>
        <form onSubmit={methods.handleSubmit(onSubmit)}>
          <ModalContent>
            <AxonInput
              data-testid="axon-input-tag.name"
              value={tag?.name ?? ''}
              name="name"
              width="fullwidth"
              required={true}
              label={t('ManageTags.NameLabel')}
              validationMessage={methods.formState.errors['name']?.message?.toString() ?? undefined}
            />
            <AxonTextArea
              data-testid="axon-input-tag.description"
              value={tag?.description ?? ''}
              name="description"
              width="fullwidth"
              label={t('ManageTags.DescriptionLabel')}
              validationMessage={methods.formState.errors['description']?.message?.toString() ?? undefined}
            />
          </ModalContent>
          <ModalButtons>
            {isEdit && !tag?.isAssociatedToComment && <DeleteButton color="tertiary" label={t('Forms.DeleteButton')} onClick={onDelete} />}
            <PhlexButton color="secondary" type="button" label={t('Forms.CancelButton')} onClick={onClose} />
            <PhlexButton
              data-testid="axon-button-tag.save-button"
              label={isEdit ? t('Forms.UpdateButton') : t('Forms.AddButton')}
              type="submit"
              disabled={methods.formState.isSubmitting}
            />
          </ModalButtons>
          <PhlexConfirmationDialog
            isOpen={confirmDelete}
            heading={t('ManageTags.DeleteHeading')}
            description={t('ManageTags.DeleteDescription', { tagName: tag?.name })}
            onConfirm={() => remove()}
            onCancel={() => setConfirmDelete(false)}
            cancelButtonText={t('Forms.CancelButton')}
            confirmButtonText={t('Forms.DeleteButton')}
          />
        </form>
      </FormProvider>
    </Modal>
  );
};

export default TagModal;
