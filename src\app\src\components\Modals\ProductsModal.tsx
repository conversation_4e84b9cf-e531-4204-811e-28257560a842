import { FilterDescriptor, filterBy } from '@progress/kendo-data-query';
import { DropDownListFilterChangeEvent } from '@progress/kendo-react-dropdowns';
import { ProductModel } from 'axon-hacomms-api-sdk';
import { PhlexButton, PhlexDropdown, PhlexLoader } from 'phlex-core-ui';
import React, { FC, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import components from './styles';

interface ProductsModalProps {
  activeProducts: ProductModel[] | undefined;
  productTabs: string[];
  onProductAdded: (selectedProduct: ProductModel) => void;
  onClose: () => void;
}

const ProductsModal: FC<ProductsModalProps> = ({ activeProducts, productTabs, onProductAdded, onClose }) => {
  const { Modal, ModalHeader, ModalContent, ModalButtons } = components;
  const { t } = useTranslation();
  const width = 600;
  const defaultProduct = { id: undefined, name: t('ProductModals.SelectProduct') };
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [productFilter, setProductFilter] = useState<FilterDescriptor>({
    field: 'name',
    operator: 'contains',
    value: '',
    ignoreCase: true,
  });
  const [selectedProduct, setSelectedProduct] = useState<ProductModel>(defaultProduct);
  const [productTabExists, setProductTabExists] = useState(false);

  const onProductChangeHandler = (product: ProductModel) => {
    setSelectedProduct(product);
    setProductTabExists(false);
  };

  const onAddProductClick = () => {
    if (productTabs.includes(selectedProduct.name)) {
      setProductTabExists(true);
      return;
    }
    onProductAdded(selectedProduct);
  };

  const onProductFilterChange = (e: DropDownListFilterChangeEvent) => {
    setProductFilter(e.filter);
  };

  useEffect(() => {
    if (activeProducts) setIsLoading(false);
  }, [activeProducts]);

  return (
    <Modal onClose={onClose} width={width}>
      <ModalHeader>{t('ProductModals.Heading')}</ModalHeader>
      <ModalContent>
        {isLoading ? (
          <PhlexLoader position="center" />
        ) : (
          <PhlexDropdown
            label="&nbsp;"
            data-testid={`axon-input-communication.product`}
            name="product"
            data={filterBy(activeProducts || [], productFilter)}
            textField="name"
            dataItemKey="id"
            value={selectedProduct}
            width="fullwidth"
            defaultItem={defaultProduct}
            filterable={true}
            onFilterChange={onProductFilterChange}
            onChange={(e) => {
              onProductChangeHandler(e.target.value);
            }}
            validationMessage={
              productTabExists ? t('ProductModals.ProductExistsValidationError', { product: selectedProduct.name }) : undefined
            }
          />
        )}
      </ModalContent>
      <ModalButtons>
        <PhlexButton color="secondary" label={t('Forms.CancelButton')} onClick={onClose} />
        <PhlexButton
          data-testid="axon-button-communication-product.add-button"
          label={t('Forms.AddButton')}
          onClick={onAddProductClick}
          disabled={!selectedProduct.id}
        />
      </ModalButtons>
    </Modal>
  );
};

export default ProductsModal;
