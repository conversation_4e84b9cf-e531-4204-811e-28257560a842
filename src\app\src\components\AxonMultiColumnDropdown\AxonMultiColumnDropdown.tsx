import React, { FC } from 'react';
import { useFormContext, useController } from 'react-hook-form';
import { PhlexMultiColumnDropdown } from 'phlex-core-ui';
import { IPhlexMultiColumnDropdownProps } from 'phlex-core-ui/build/src/controls/PhlexMultiColumnDropdown/PhlexMultiColumnDropdown';
import { ComboBoxBlurEvent, ComboBoxChangeEvent } from '@progress/kendo-react-dropdowns';
import { useTranslation } from 'react-i18next';

const Component: FC<IPhlexMultiColumnDropdownProps> = ({ name, onChange, onBlur, required, label, data, ...rest }) => {
  const { t } = useTranslation();
  const { control, trigger } = useFormContext();

  const { field } = useController({
    name,
    control,
    defaultValue: rest.value,
    rules: {
      required: required ? t('Forms.RequiredField', { field: label }) : '',
    },
  });

  const onChangeHandler = (e: ComboBoxChangeEvent) => {
    field.onChange(e);
    onChange?.(e);
    trigger(name);
  };

  const onBlurHandler = (e: ComboBoxBlurEvent) => {
    field.onBlur();
    onBlur?.(e);
  };

  return (
    <PhlexMultiColumnDropdown
      {...rest}
      data={data}
      value={field.value}
      required={required}
      onChange={onChangeHandler}
      onBlur={onBlurHandler}
      name={name}
      label={label}
    />
  );
};

export default Component;
