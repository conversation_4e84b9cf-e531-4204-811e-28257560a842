import { SearchCommentCommandRequest, SubmissionTypeModel } from 'axon-hacomms-api-sdk';
import { MultiselectData } from 'components/shared/types/types';
import { PhlexMultiSelect } from 'phlex-core-ui';
import React, { FC, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import submissionTypeApiService from 'shared/api/submission-type-api-service';
import { SUBMISSION_TYPE_KEY, SearchFiltersProps } from './search-filter-types';
import { getFiltersFromStorage, updateFiltersInStorage } from './search-filter-service';

const SubmissionTypesFilterComponent: FC<SearchFiltersProps> = (props: SearchFiltersProps) => {
  const { tenant, setSearchModel, clear } = props;
  const { t } = useTranslation();
  const [selectedSubmissionTypes, setSelectedSubmissionTypes] = useState<MultiselectData>([]);

  const fetchActiveSubmissionTypes = async (): Promise<MultiselectData> => {
    const response = await submissionTypeApiService.getSubmissionTypeList(tenant);
    const data =
      response.data.data?.map((item: SubmissionTypeModel) => ({ value: item.id?.toString() ?? '', label: item.name ?? '' })) ?? [];
    return data;
  };

  const updateSearchModel = (data: MultiselectData) => {
    setSearchModel((request: SearchCommentCommandRequest) => ({
      ...request,
      submissionTypes: data.map((x) => ({ id: Number(x.value), name: x.label })),
      skip: 0,
      take: 10,
    }));
  };

  const onSubmissionTypeFilterChange = (data: MultiselectData) => {
    setSelectedSubmissionTypes(data);
    updateFiltersInStorage(data, SUBMISSION_TYPE_KEY);
    updateSearchModel(data);
  };

  useEffect(() => {
    const searchFilters = getFiltersFromStorage();
    if (searchFilters?.filters[SUBMISSION_TYPE_KEY] && searchFilters.filters[SUBMISSION_TYPE_KEY].length > 0) {
      const filterData = searchFilters.filters[SUBMISSION_TYPE_KEY];
      setSelectedSubmissionTypes(filterData);
      updateSearchModel(filterData);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (clear) setSelectedSubmissionTypes([]);
  }, [clear]);

  return (
    <PhlexMultiSelect
      data-testid={'axon-input-search-filter-submission-type'}
      name={`submissionTypeSearchFilter`}
      label={t('SearchFilters.SubmissionType')}
      value={selectedSubmissionTypes}
      defaultValues={selectedSubmissionTypes}
      width="fullwidth"
      compactMode={true}
      fetchData={() => fetchActiveSubmissionTypes()}
      onlyFetchOnOpen={true}
      onChange={onSubmissionTypeFilterChange}
    />
  );
};

export default SubmissionTypesFilterComponent;
