import { SearchCommentCommandRequest } from 'axon-hacomms-api-sdk';
import { MultiselectData } from 'components/shared/types/types';
import { PhlexMultiSelect } from 'phlex-core-ui';
import React, { FC, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { drugSubstanceApiService } from 'shared/api';
import { DRUG_SUBSTANCE_KEY, SearchFiltersProps } from './search-filter-types';
import { generateDrugSubstanceOptions } from 'shared/services/drug-substance-srv';
import { getFiltersFromStorage, updateFiltersInStorage } from './search-filter-service';

const DrugSubstancesFilterComponent: FC<SearchFiltersProps> = (props: SearchFiltersProps) => {
  const { tenant, setSearchModel, disable, clear } = props;
  const { t } = useTranslation();
  const [selectedDrugSubstances, setSelectedDrugSubstances] = useState<MultiselectData>([]);

  const fetchDrugSubstances = async (): Promise<MultiselectData> => {
    const response = await drugSubstanceApiService.getDrugSubstancesList(tenant);
    const data = generateDrugSubstanceOptions(response.data.data ?? [], t('Common.DrugSubstanceCode'), t('Common.DrugSubstanceName'));
    return data;
  };

  const updateSearchModel = (data: MultiselectData) => {
    setSearchModel((request: SearchCommentCommandRequest) => ({
      ...request,
      drugSubstances: data.map((x) => ({ id: Number(x.value), name: x.label })),
      skip: 0,
      take: 10,
    }));
  };

  const onDrugSubstanceFilterChange = (data: MultiselectData) => {
    setSelectedDrugSubstances(data);
    updateFiltersInStorage(data, DRUG_SUBSTANCE_KEY);
    updateSearchModel(data);
  };

  useEffect(() => {
    const searchFilters = getFiltersFromStorage();
    if (searchFilters?.filters[DRUG_SUBSTANCE_KEY] && searchFilters.filters[DRUG_SUBSTANCE_KEY].length > 0) {
      const filterData = searchFilters.filters[DRUG_SUBSTANCE_KEY];
      setSelectedDrugSubstances(filterData);
      updateSearchModel(filterData);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (disable) {
      setSelectedDrugSubstances([]);
      updateFiltersInStorage([], DRUG_SUBSTANCE_KEY);
    }
  }, [disable]);

  useEffect(() => {
    if (clear) setSelectedDrugSubstances([]);
  }, [clear]);

  return (
    <PhlexMultiSelect
      data-testid={'axon-input-search-filter-drug-substance'}
      name={`drugSubstanceSearchFilter`}
      label={t('SearchFilters.DrugSubstance')}
      value={selectedDrugSubstances}
      defaultValues={selectedDrugSubstances}
      width="fullwidth"
      compactMode={true}
      fetchData={() => fetchDrugSubstances()}
      onlyFetchOnOpen={true}
      onChange={onDrugSubstanceFilterChange}
      disabled={disable}
    />
  );
};

export default DrugSubstancesFilterComponent;
