import { CommentDtoModel, ProductDtoModel, ProductModel } from 'axon-hacomms-api-sdk';
import { getGenericProduct } from 'components/Product/genericProduct';
import { PhlexConfirmationDialog, PhlexIconButton, PhlexLoader } from 'phlex-core-ui';
import React, { FC, useCallback, useContext, useEffect, useMemo, useReducer } from 'react';
import { useTranslation } from 'react-i18next';

import ProductsModal from '../../Modals/ProductsModal';
import { DeleteProductDialogSettings, ProductTabsProps, defaultComment } from '../communication-form-types';
import CommunicationComments from './CommunicationComments';
import components from './ProductTabs.styles';
import CommunicationContext from 'context/CommunicationContext';
import { isCommunicationInEditState, mapComment } from '../communication-service';
import { generateDrugSubstanceOptions } from 'shared/services/drug-substance-srv';
import { commentApiService } from 'shared/api';
import { toast } from 'react-toastify';
import { PagedProps } from 'components/SearchViews/search-view-types';
import { defaultProductExtension } from 'components/Product/product-form-types';

type ProductTabsData = {
  productsModalVisible?: boolean;
  deleteProductDialogSettings?: DeleteProductDialogSettings;
  selectedProductIndex?: number;
  productTabs?: ProductDtoModel[];
  productTabAdded?: boolean;
  pagedModel?: PagedProps;
  totalComments?: number;
  comments?: CommentDtoModel[];
  isCommentsLoading?: boolean;
  isInitialCommentsLoading?: boolean;
};

const reducer = (state: ProductTabsData, newState: ProductTabsData) => {
  return { ...state, ...newState };
};

const initialPagedModel: PagedProps = { page: 0, skip: 0, take: 10 };

const ProductTabsComponent: FC<ProductTabsProps> = ({ methods }) => {
  const { t } = useTranslation();
  const {
    communication,
    isNewCommunication,
    products,
    setSelectedProduct,
    selectedProduct,
    generalGuidanceCommentsCount,
    globalEditModeState,
    triggerFormValidation,
    tenant,
  } = useContext(CommunicationContext);
  const [data, setData] = useReducer(reducer, {
    productsModalVisible: false,
    deleteProductDialogSettings: { isOpen: false },
    productTabs: [],
    productTabAdded: false,
    pagedModel: initialPagedModel,
  });
  const activeProducts = useMemo(() => products?.filter((p) => p.isActive), [products]);
  const genericProduct = useMemo(() => getGenericProduct(t('Common.GenericProductName')), [t]);
  const isLoadMoreHidden = useMemo(
    () =>
      !data.pagedModel ||
      !data.totalComments ||
      data.totalComments <= data.pagedModel.take + data.pagedModel.skip ||
      !!data.isCommentsLoading,
    [data.pagedModel, data.totalComments, data.isCommentsLoading]
  );

  const {
    TabContent,
    ProductTabs,
    ProductTab,
    CommentsSection,
    SectionBorder,
    HeadingWrapper,
    Heading,
    HeadingActions,
    SectionWrapper,
    SubHeading,
    ValidationError,
    GeneralGuidance,
  } = components;

  const getComments = useCallback(
    async (id: number, productId?: number | undefined) => {
      if (!data.pagedModel) return;
      const commentsResponse = await commentApiService.getCommentsByCommunicationId(
        +id,
        tenant,
        productId,
        undefined,
        data.pagedModel.skip,
        data.pagedModel.take
      );
      setData({
        comments: commentsResponse.data.data ?? [],
        totalComments: commentsResponse.data.paging?.totalItemCount,
      });
    },
    [tenant, data.pagedModel]
  );

  useEffect(() => {
    if (data.comments) {
      const comments = data.comments.map((c) => mapComment(c, t('Common.DrugSubstanceCode'), t('Common.DrugSubstanceName')));
      const formValues = methods.getValues();
      const loadedComments = data.pagedModel && data.pagedModel.page > 0 ? formValues.comments ?? [] : [];
      methods.reset({
        ...formValues,
        comments: [...loadedComments, ...comments],
      });
      const timeoutId = setTimeout(() => setData({ isCommentsLoading: false, isInitialCommentsLoading: false }), 1000);
      return () => clearTimeout(timeoutId);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data.comments]);

  useEffect(() => {
    if (!isNewCommunication && data.pagedModel && data.selectedProductIndex != undefined) {
      if (data.productTabAdded) {
        setData({ productTabAdded: false });
        return;
      }
      setData({ isCommentsLoading: true });
      getComments(communication?.id as number, selectedProduct === undefined ? undefined : selectedProduct.id);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data.pagedModel, data.selectedProductIndex]);

  useEffect(() => {
    if (communication) {
      const tabIx = communication.products && communication.products.length > 0 ? 0 : genericProduct.id;
      setData({ selectedProductIndex: tabIx, productTabs: communication.products ?? [] });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [communication]);

  const handleLoadMoreComments = () => {
    const currentPagedModel = data.pagedModel ?? initialPagedModel;
    const page = currentPagedModel.page + 1;
    setData({
      pagedModel: {
        ...currentPagedModel,
        page,
        skip: page * currentPagedModel.take,
      },
    });
  };

  const onAddProductClick = async () => {
    if (!(await triggerFormValidation())) {
      return;
    }

    setData({ productsModalVisible: true });
  };

  const onProductsModalClose = () => {
    closeProductsModal();
  };

  const selectProductTab = (newSelectedProductIx: number, productTabs: ProductDtoModel[]) => {
    if (newSelectedProductIx === genericProduct.id) {
      setSelectedProduct?.(undefined);
      const formValues = methods.getValues();
      if (isNewCommunication && formValues.comments?.length === 0) {
        methods.clearErrors('comments');
        methods.reset({ ...formValues, comments: [{ ...defaultComment }] }, { keepDirty: true });
      }
    } else {
      const productId = productTabs[newSelectedProductIx].id;
      const selectedProduct = products?.find((p) => p.id === productId);
      setSelectedProduct?.(selectedProduct);
    }

    setData({
      pagedModel: initialPagedModel,
      selectedProductIndex: newSelectedProductIx,
      isInitialCommentsLoading: !!communication?.id,
    });
  };

  const handleOnProductSelect = async (newSelectedProductIx: number) => {
    if (newSelectedProductIx === data.selectedProductIndex || isCommunicationInEditState(globalEditModeState)) {
      return;
    }

    if (!(await triggerFormValidation())) {
      return;
    }

    selectProductTab(newSelectedProductIx, data.productTabs ?? []);
  };

  const updateProductTabs = (selectedProduct: ProductModel) => {
    const tabs = [...(data.productTabs ?? [])];
    tabs.push({ id: selectedProduct.id, name: selectedProduct.name });
    setData({ productTabs: tabs, selectedProductIndex: tabs.length - 1 });
  };

  const resetComments = (selectedProduct: ProductModel) => {
    const drugSubstanceOptionList = generateDrugSubstanceOptions(
      selectedProduct.drugSubstances ?? [],
      t('Common.DrugSubstanceCode'),
      t('Common.DrugSubstanceName')
    );
    methods.clearErrors('comments');
    const formValues = methods.getValues();
    methods.reset(
      {
        ...formValues,
        comments: [
          {
            ...defaultComment,
            drugSubstances: drugSubstanceOptionList,
            productId: selectedProduct.id,
            productExtensions: [defaultProductExtension],
          },
        ],
      },
      { keepDirty: true }
    );
  };

  const resetSelectedProductAndComments = () => {
    setSelectedProduct?.(undefined);
    setData({ selectedProductIndex: undefined });
    methods.clearErrors('comments');
    const formValues = methods.getValues();
    methods.reset({ ...formValues, comments: [] }, { keepDirty: true });
  };

  const onProductAdded = (selectedProduct: ProductModel) => {
    updateProductTabs(selectedProduct);
    setSelectedProduct?.(selectedProduct);
    resetComments(selectedProduct);
    closeProductsModal();
    setData({ productTabAdded: true });
  };

  const closeProductsModal = () => {
    setData({ productsModalVisible: false });
  };

  const deleteProduct = async () => {
    if (data.deleteProductDialogSettings?.index === undefined) return;
    if (!isNewCommunication) {
      const productId = data.deleteProductDialogSettings?.productId;
      const deleteRequest = { communicationId: communication?.id, productId: productId };
      await commentApiService.deleteCommentsByCommunicationAndProduct(tenant, deleteRequest);
      toast.success(t('ManageCommunication.DeleteProductSucceeded'));
    }
    const tabs = [...(data.productTabs ?? [])];
    tabs.splice(data.deleteProductDialogSettings.index, 1);

    setData({ productTabs: tabs, deleteProductDialogSettings: { isOpen: false }, selectedProductIndex: undefined });
    resetSelectedProductAndComments();
  };

  const isCurrentTab = (ix: number) => data.selectedProductIndex !== undefined && data.selectedProductIndex === ix;

  const handleDeleteIconClick = async (productId: number, index: number) => {
    const isCurrent: boolean = isCurrentTab(index);
    if (communication?.id && !isCurrent && !(await triggerFormValidation())) return;
    setData({ deleteProductDialogSettings: { productId: productId, index: index, isOpen: true } });
  };

  const getProductTabActions = (productId: number, index: number) =>
    communication?.isCompleted ||
    isCommunicationInEditState(globalEditModeState) ||
    (communication?.id && data.productTabs?.length === 1 && generalGuidanceCommentsCount === 0)
      ? []
      : [
          {
            iconName: 'delete',
            iconClick: () => handleDeleteIconClick(productId, index),
            iconTooltip: t('ManageCommunication.DeleteProductTooltip'),
          },
        ];

  const isTabDisabled = () => {
    return isNewCommunication && data.productTabs && data.productTabs?.length > 0;
  };

  return (
    <TabContent>
      <CommentsSection>
        <HeadingWrapper>
          <Heading>{t('ManageCommunication.CommentsHeading')}</Heading>
        </HeadingWrapper>
        <ProductTabs>
          <SectionWrapper>
            <SubHeading>{t('ManageCommunication.GeneralGuidanceSubHeading')}</SubHeading>
          </SectionWrapper>
          <GeneralGuidance>
            <ProductTab
              label={t('ManageCommunication.GeneralGuidance')}
              isCurrent={data.selectedProductIndex === genericProduct.id}
              isDisabled={isTabDisabled()}
              clickHandler={() => {
                if (isTabDisabled()) {
                  return;
                } else {
                  handleOnProductSelect(genericProduct.id);
                }
              }}
            />
          </GeneralGuidance>
          <SectionBorder />
          <SectionWrapper>
            <SubHeading>{t('ManageCommunication.DrugProducts')}</SubHeading>
            <ValidationError
              $showError={!!methods.formState.errors.comments?.message}
              data-title={methods.formState.errors.comments?.message}
            >
              error_outline
            </ValidationError>
            <HeadingActions>
              <PhlexIconButton
                buttonType="add"
                title={t('ManageCommunication.AddProductTitle')}
                onClick={(e: any) => {
                  e.preventDefault();
                  onAddProductClick();
                }}
                disabled={
                  (isNewCommunication && data.selectedProductIndex == genericProduct.id) ||
                  communication?.isCompleted ||
                  isCommunicationInEditState(globalEditModeState) ||
                  (data.productTabs?.length === 1 && isNewCommunication)
                }
              />
            </HeadingActions>
          </SectionWrapper>
          {data.productTabs?.map((product, index) => (
            <ProductTab
              key={`communication-products-row-${product.id}`}
              label={product.name ?? ''}
              isCurrent={isCurrentTab(index)}
              clickHandler={() => handleOnProductSelect(index)}
              actions={getProductTabActions(product.id ?? 0, index)}
            />
          ))}
        </ProductTabs>
      </CommentsSection>
      {data.isInitialCommentsLoading ? (
        <PhlexLoader position="center" />
      ) : (
        <CommunicationComments
          isLoadMoreHidden={isLoadMoreHidden}
          commentsPagedModel={data.pagedModel ?? initialPagedModel}
          handleLoadMoreComments={handleLoadMoreComments}
          onCommentDeleted={resetSelectedProductAndComments}
          isCommentsLoading={data.isCommentsLoading ?? false}
          isGeneralGuidance={data.selectedProductIndex === genericProduct.id}
          totalComments={data.totalComments ?? 0}
          productTabsCount={data.productTabs?.length ?? 0}
          methods={methods}
        />
      )}
      {data.productsModalVisible && (
        <ProductsModal
          activeProducts={activeProducts}
          onProductAdded={onProductAdded}
          onClose={onProductsModalClose}
          productTabs={data.productTabs?.map((p) => p.name ?? '') ?? []}
        ></ProductsModal>
      )}
      <PhlexConfirmationDialog
        isOpen={data.deleteProductDialogSettings?.isOpen ?? false}
        onConfirm={() => deleteProduct()}
        onCancel={() => setData({ deleteProductDialogSettings: { isOpen: false } })}
        heading={t('CommunicationModals.DeleteProductHeading')}
        description={t('CommunicationModals.DeleteProductDescription')}
      />
    </TabContent>
  );
};

export default ProductTabsComponent;
