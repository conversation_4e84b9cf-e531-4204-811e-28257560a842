import { AxonTextArea } from 'components/AxonTextArea';
import styled from 'styled-components';

export default {
  TextAreaContainer: styled.div`
    && .k-textarea {
      margin-bottom: 0.75rem;
      max-height: none;
    }

    && .k-input-inner {
      padding: 0.75rem;
    }
  `,
  LabelWrapper: styled.div`
    line-height: ${(props) => props.theme.lineHeightHeading};
  `,
  QuestionIncludedContainer: styled.div`
    display: flex;
    align-items: center;
    justify-content: end;

    label {
      margin-bottom: 0;
      margin-right: 0.5rem;
    }
  `,
  QuestionIncludedSwitchWrapper: styled.div``,
  StyledTextArea: styled(AxonTextArea)`
    pointer-events: auto;

    && .k-input-inner {
      ${(props) => props.readOnly === true && `background-color: ${props.theme.colors.backgroundSec};`}
    }
  `,
  StyledTextAreaWrapper: styled.div`
    display: flex;
    gap: 0.5rem;
  `,
};
