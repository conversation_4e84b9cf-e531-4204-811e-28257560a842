import { CompositeFilterDescriptor } from '@progress/kendo-data-query';
import { Grid, GridCellProps, GridColumn, GridDetailRowProps, GridExpandChangeEvent, GridRowClickEvent } from '@progress/kendo-react-grid';
import { DosageFormModel, ProductModel, ProductPagedListModel, RouteOfAdministrationModel } from 'axon-hacomms-api-sdk';
import { MainContent, InnerTable, TableIcon, SearchInput, SearchDropDown } from 'components/Content/Content.styles';
import documentsApi from 'components/shared/api/documentsApi';
import useDataGridPagination, { BaseApiPaging, Pagination } from 'components/shared/hooks/useDataGridPagination';
import { routes } from 'pages';
import { TableColumn } from 'phlex-core-ui/build/src/controls/PhlexTable/PhlexTable.types';
import React, { Fragment, FunctionComponent, useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router';
import { PhlexBreadcrumb, PhlexLayout, PhlexButton, PhlexLoader, PhlexMultiSelect } from 'phlex-core-ui';
import { ProductPagedListModelExtended } from 'components/Product/product-form-types';
import HaCommsTable from 'components/Tables/HaCommsTable';
import { tableFilters } from 'shared/consts';
import routeOfAdministrationApiService from 'shared/api/route-of-administration-api-service';
import { useOrganisationFromUrl } from 'hooks/shared';
import { IOption } from 'phlex-core-ui/build/src/controls/PhlexMultiSelect/PhlexMultiSelect';
import { MultiselectData } from 'components/shared/types/types';
import { dosageFormApiService } from 'shared/api';
import { generateDrugSubstanceLabelFromList } from 'shared/services/drug-substance-srv';
import { withRestrictedPermissions } from 'hoc';
import { Permissions } from 'constants/permissions';
import sharedComponents from 'pages/index.styles';

const initialFilter: CompositeFilterDescriptor = {
  logic: 'and',
  filters: [
    {
      field: tableFilters.NAME,
      operator: 'contains',
      value: '',
    },
    {
      field: tableFilters.SUBSTANCES,
      operator: 'contains',
      value: '',
    },
    {
      field: tableFilters.PRODUCT_CODE,
      operator: 'contains',
      value: '',
    },
    {
      field: tableFilters.ROUTE_OF_ADMINISTRATION,
      operator: 'contains',
      value: '',
    },
    {
      field: tableFilters.DOSAGE_FORM,
      operator: 'contains',
      value: '',
    },
    {
      field: tableFilters.IS_ACTIVE,
      operator: 'contains',
      value: '',
    },
  ],
};

const getIsActiveFilter = (val: string) => {
  switch (val) {
    case 'Active':
      return 'true';
    case 'Inactive':
      return 'false';
    default:
      return '';
  }
};

const { PageActions, FilterPanel, ClearFilters } = PhlexLayout;

const { HaCommsPageTopBar } = sharedComponents;

const Component: FunctionComponent<{ basepath: string }> = ({ basepath }) => {
  const tenant = useOrganisationFromUrl();
  const extendModel = (inputData: ProductPagedListModel[]) => {
    return inputData.map((x) => ({ ...x, expanded: true }));
  };

  const navigate = useNavigate();

  const initState = {
    skip: 0,
    data: [] as ProductPagedListModelExtended[],
    total: 0,
    take: 10,
    filter: '',
    orderBy: 'name=>asc',
    refresh: false,
    cleanBasePath: basepath,
  } as Pagination<ProductPagedListModelExtended>;
  const [pagination, onPageChange, changeFilter] = useDataGridPagination<
    ProductPagedListModel,
    BaseApiPaging<ProductPagedListModel>,
    Pagination<ProductPagedListModelExtended>
  >(documentsApi.drugProducts.getList, initState, undefined, extendModel);

  const { t } = useTranslation();

  const breadcrumbs = [
    {
      key: 'manage',
      text: t('Nav.Manage'),
      active: 'false' as const,
    },
    {
      key: 'products',
      text: t('ProductTable.Title'),
      active: 'true' as const,
    },
  ];

  const viewCell = (e: GridCellProps) => {
    return (
      <TableIcon title="View Details" onClick={() => navigate(`${basepath}${routes.updateProduct(e.dataItem.id)}`)}>
        chevron_right
      </TableIcon>
    );
  };

  const drugSubstanceCell = (e: GridCellProps) => {
    return (
      e.dataItem && (
        <td className="wrapText">
          <b>{t('ProductTable.SubstancesField') + ': '}</b>
          {generateDrugSubstanceLabelFromList(e.dataItem.substances ?? [], t('Common.DrugSubstanceCode'), t('Common.DrugSubstanceName'))}
        </td>
      )
    );
  };

  const productNameCell = (e: GridCellProps) => {
    return (
      e.dataItem && (
        <td>
          <b>{t('ProductTable.ProductField') + ': '}</b>
          {e.dataItem.name}
        </td>
      )
    );
  };

  const isActiveCell = (e: GridCellProps) => {
    return (
      e.dataItem && (
        <td>
          <b>{e.dataItem.isActive ? t('Table.Active') : t('Table.Inactive')}</b>
        </td>
      )
    );
  };

  const columns: TableColumn[] = [
    { identifier: tableFilters.NAME, title: t('ProductTable.ProductField'), cell: productNameCell },
    { identifier: tableFilters.SUBSTANCES, title: t('ProductTable.SubstancesField'), cell: drugSubstanceCell },
    { identifier: tableFilters.IS_ACTIVE, title: t('ProductTable.IsActiveField'), cell: isActiveCell },
    { identifier: '', title: '', cell: viewCell, width: '56px' },
  ];

  const DetailComponent = useCallback(
    (props: GridDetailRowProps) => {
      const dataItem = props.dataItem.productExtensions.map((pe: any) => ({
        pcid: pe.pcid,
        dosageFormName: pe.dosageFormName,
        routeOfAdministrationNames: pe.routeOfAdministrations.map((r: any) => r.name).join(', '),
      }));

      return (
        <InnerTable>
          <Grid data={dataItem}>
            <GridColumn field="pcid" title={t('ProductTable.ProductCodeField')} />
            <GridColumn field="routeOfAdministrationNames" title={t('ProductTable.RouteOfAdministrationField')} />
            <GridColumn field="dosageFormName" title={t('ProductTable.DosageFormField')} />
          </Grid>
        </InnerTable>
      );
    },
    [t]
  );

  const [filters, setFilters] = useState(initialFilter);
  const [updateResults, setUpdateResults] = useState(false);
  const [nameFilter, setNameFilter] = useState('');
  const [substancesFilter, setSubstancesFilter] = useState('');
  const [productCodeFilter, setProductCodeFilter] = useState('');
  const [routeOfAdminListFilter, setRouteOfAdminListFilter] = useState<IOption[]>([]);
  const [dosageFormListFilter, setDosageFormListFilter] = useState<IOption[]>([]);
  const [isActiveFilter, setIsActiveFilter] = useState('');
  const [isInitial, setIsInitial] = useState(true);

  const [nameOfRows] = useState(t('ProductTable.PagerLabel'));

  const onRowClick = (e: GridRowClickEvent) => {
    navigate(`${basepath}${routes.updateProduct(e.dataItem.id)}`);
  };

  const [, setProducts] = useState<ProductModel[] | null | undefined>([]);

  const expandChange = (event: GridExpandChangeEvent) => {
    event.dataItem.expanded = event.value;
    const productId = event.dataItem.id;
    const index = pagination.data.findIndex((d: any) => d.id === productId);
    pagination.data[index].expanded = event.dataItem.expanded;

    setProducts(pagination.data ? [...pagination.data] : undefined);
  };

  const productStatusOptions = [t('Table.Active'), t('Table.Inactive')];

  const clearFilters = () => {
    setNameFilter('');
    setSubstancesFilter('');
    setProductCodeFilter('');
    setRouteOfAdminListFilter([]);
    setDosageFormListFilter([]);
    setIsActiveFilter('');
    setUpdateResults(true);
    setFilters(initialFilter);
  };

  useEffect(() => {
    if (updateResults) {
      const timer = setTimeout(() => {
        changeFilter(filters);
        setUpdateResults(false);
      }, 500);
      return () => clearTimeout(timer);
    }
  }, [changeFilter, filters, updateResults]);

  useEffect(() => {
    if (
      nameFilter.length === 0 &&
      substancesFilter.length === 0 &&
      productCodeFilter.length === 0 &&
      routeOfAdminListFilter.length === 0 &&
      dosageFormListFilter.length === 0 &&
      isActiveFilter.length === 0 &&
      isInitial
    ) {
      setIsInitial(false);
      return;
    }

    const filterValues: CompositeFilterDescriptor = {
      logic: 'and',
      filters: [
        {
          field: tableFilters.NAME,
          operator: 'contains',
          value: nameFilter,
        },
        {
          field: tableFilters.SUBSTANCES,
          operator: 'contains',
          value: substancesFilter,
        },
        {
          field: tableFilters.PRODUCT_CODE,
          operator: 'contains',
          value: productCodeFilter,
        },
        {
          field: tableFilters.ROUTE_OF_ADMINISTRATION,
          operator: 'contains',
          value: routeOfAdminListFilter.map((r: IOption) => r.value),
        },
        {
          field: tableFilters.DOSAGE_FORM,
          operator: 'contains',
          value: dosageFormListFilter.map((d: IOption) => d.value),
        },
        {
          field: tableFilters.IS_ACTIVE,
          operator: 'contains',
          value: getIsActiveFilter(isActiveFilter),
        },
      ],
    };

    setUpdateResults(true);
    setFilters(filterValues);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [nameFilter, substancesFilter, productCodeFilter, routeOfAdminListFilter, dosageFormListFilter, isActiveFilter]);

  const fetchRouteOfAdministrationList = async (): Promise<MultiselectData> => {
    const response = await routeOfAdministrationApiService.getRouteOfAdministrationList(tenant);
    return (
      response.data.data?.map((item: RouteOfAdministrationModel) => ({ value: item.id?.toString() ?? '', label: item.name ?? '' })) ?? []
    );
  };

  const fetchDosageFormList = async (): Promise<MultiselectData> => {
    const response = await dosageFormApiService.getDosageFormList(tenant);
    return response.data.data?.map((item: DosageFormModel) => ({ value: item.id?.toString() ?? '', label: item.name ?? '' })) ?? [];
  };

  return (
    <Fragment>
      <HaCommsPageTopBar>
        <PhlexBreadcrumb options={breadcrumbs} />
        <PageActions>
          <PhlexButton
            data-testid="axon-button-product.add-button"
            label={t('ProductTable.AddProductButton')}
            type="button"
            onClick={() => navigate(`${basepath}${routes.createProduct}`)}
          />
        </PageActions>
      </HaCommsPageTopBar>
      <MainContent>
        <FilterPanel>
          <SearchInput
            name="name"
            onChange={(e) => setNameFilter(e.value)}
            value={nameFilter}
            label={t('ProductTable.ProductField')}
            width="fullwidth"
          />
          <SearchInput
            name="substances"
            onChange={(e) => setSubstancesFilter(e.value)}
            value={substancesFilter}
            label={t('ProductTable.SubstancesField')}
            width="fullwidth"
          />
          <SearchInput
            name="productCode"
            onChange={(e) => setProductCodeFilter(e.value)}
            value={productCodeFilter}
            label={t('ProductTable.ProductCodeField')}
            width="fullwidth"
          />
          <PhlexMultiSelect
            data-testid={`axon-product-filter-route-of-admin`}
            name="routeOfAdmin"
            label={t('ProductTable.RouteOfAdministrationField')}
            labelPosition="top"
            width="standard"
            defaultValues={routeOfAdminListFilter}
            value={routeOfAdminListFilter}
            fetchData={() => fetchRouteOfAdministrationList()}
            onlyFetchOnOpen={true}
            onChange={(e) => setRouteOfAdminListFilter(e)}
            compactMode={true}
          />
          <PhlexMultiSelect
            data-testid={`axon-product-filter-dosage-form`}
            name="dosageForm"
            label={t('ProductTable.DosageFormField')}
            labelPosition="top"
            width="fullwidth"
            defaultValues={dosageFormListFilter}
            value={dosageFormListFilter}
            fetchData={() => fetchDosageFormList()}
            onlyFetchOnOpen={true}
            onChange={(e) => setDosageFormListFilter(e)}
            compactMode={true}
          />
          <SearchDropDown
            name="isActive"
            data={productStatusOptions}
            onChange={(e) => setIsActiveFilter(e.value)}
            value={isActiveFilter}
            label={t('ProductTable.IsActiveField')}
            width="fullwidth"
          />
          <ClearFilters name="filter_alt_off" clickHandler={clearFilters} tooltip={t('Common.ClearAllFitlers')} />
        </FilterPanel>
        {pagination.isLoading ? (
          <PhlexLoader position="page" />
        ) : (
          <HaCommsTable
            {...pagination}
            data={pagination.data.map((x) => ({
              ...x,
            }))}
            uniqueIdField={tableFilters.NAME}
            filterable={false}
            pageable={true}
            onPageChange={onPageChange}
            isLoading={pagination.isLoading}
            total={pagination.total}
            columns={columns}
            nameOfRows={nameOfRows}
            filter={initialFilter}
            onRowClick={(e: GridRowClickEvent) => onRowClick(e)}
            detail={DetailComponent}
            expandField="expanded"
            onExpandChange={(e: GridExpandChangeEvent) => expandChange(e)}
          />
        )}
      </MainContent>
    </Fragment>
  );
};

export default withRestrictedPermissions(Component, [Permissions.ViewProduct]);
