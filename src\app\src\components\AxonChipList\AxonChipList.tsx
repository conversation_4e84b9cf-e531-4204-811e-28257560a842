import { REGULAR_EXPRESSIONS } from 'constants/regexp';
import { PhlexChipList } from 'phlex-core-ui';
import { ChipItemProps, PhlexChipListProps } from 'phlex-core-ui/build/src/controls/PhlexChipList/PhlexChipList';
import React, { FC } from 'react';
import { useController, useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

const RHFPhlexChipList: FC<PhlexChipListProps> = ({ data, name, required, getValues, heading, ...rest }) => {
  const { t } = useTranslation();
  const { control, trigger } = useFormContext();

  const {
    field: { ...inputProps },
  } = useController({
    name,
    control,
    defaultValue: data,
    rules: {
      required: required ? t('Forms.RequiredField', { field: heading }) : '',
      validate: (fieldArrayValues: ChipItemProps[]) => {
        const names = fieldArrayValues.map((x) => x.text.trim());
        if (names.findIndex((x) => x.length < 1) > -1) return t('ManageCommunication.NoEmptyValuesValidationMessage');
        if (new Set(names).size != names.length) return t('ManageCommunication.DuplicateValuesValidationMessage');
        if (names.some((name) => !REGULAR_EXPRESSIONS.IllegalCharacters.test(name))) return t('Forms.SafeCharacters');
        return undefined;
      },
    },
  });

  const onGetValues = (data: ChipItemProps[]) => {
    getValues?.(data);
    trigger(name);
  };

  return <PhlexChipList {...rest} required={required} data={inputProps.value} getValues={onGetValues} name={name} heading={heading} />;
};
export default RHFPhlexChipList;
