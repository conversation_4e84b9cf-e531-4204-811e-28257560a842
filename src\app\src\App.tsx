import i18n from 'multilingual/i18n';
import React, { ReactNode } from 'react';
import { I18nextProvider } from 'react-i18next';
import './multilingual/i18n';
import { Provider } from 'react-redux';
import { persistor, store } from 'store';
import HaCommsRouter from 'routers/HaCommsRouter';
import GlobalStyle from 'global-styles';
import { PersistGate } from 'redux-persist/integration/react';
import { PhlexToastContainer } from 'phlex-core-ui';
import { ToastContainer } from 'react-toastify';
import { BasePathType } from 'routers/types';
import { removeTrailingSlashFromPath } from 'shared/services/urlUtils';

const App = ({ basepath }: BasePathType): ReactNode => {
  const cleanBasePath = removeTrailingSlashFromPath(basepath);

  return (
    <Provider store={store}>
      <PersistGate loading={null} persistor={persistor}>
        <I18nextProvider i18n={i18n}>
          <GlobalStyle />
          <HaCommsRouter basepath={cleanBasePath}></HaCommsRouter>
          <PhlexToastContainer />
          <ToastContainer />
        </I18nextProvider>
      </PersistGate>
    </Provider>
  );
};

export default App;
