import React, { FC, useState, useEffect, useRef, useMemo, useContext } from 'react';
import components from './CommunicationComments.styles';
import { useTranslation } from 'react-i18next';
import {
  Comment,
  CommunicationCommentsProps,
  DeleteCommentDialogSettings,
  defaultComment,
  defaultProductExtension,
} from 'components/Communication/communication-form-types';
import { useFieldArray } from 'react-hook-form';
import { PhlexButton, PhlexConfirmationDialog, PhlexIconButton, PhlexLoader } from 'phlex-core-ui';
import { generateDrugSubstanceOptions } from 'shared/services/drug-substance-srv';
import CommentItem from './CommentItem';
import CommunicationContext from 'context/CommunicationContext';
import { isCommunicationInEditState } from '../communication-service';
import { commentApiService } from 'shared/api';
import { toast } from 'react-toastify';
import NonProductCommentItem from './NonProductCommentItem';
import { FeedbackStatusType } from 'phlex-core-ui/build/src/controls/PhlexIconButton/types';

const CommunicationCommentsComponent: FC<CommunicationCommentsProps> = (props: CommunicationCommentsProps) => {
  const myRef = useRef<null | HTMLDivElement>(null);
  const [newCommentAdded, setNewCommentAdded] = useState(false);
  const [commentFormValues, setCommentFormValues] = useState<Comment>();
  const [dialogSettings, setDialogSettings] = useState<DeleteCommentDialogSettings>({ isOpen: false });
  const [commentDeleting, setCommentDeleting] = useState<FeedbackStatusType>('none');
  const { ContentWrapper, Heading, Container, RightContent, HeadingWrapper, LoadMoreButtonWrapper } = components;
  const {
    isGeneralGuidance,
    handleLoadMoreComments,
    onCommentDeleted,
    commentsPagedModel,
    isCommentsLoading,
    isLoadMoreHidden,
    methods,
    totalComments,
    productTabsCount,
  } = props;
  const {
    communication,
    isNewCommunication,
    selectedProduct,
    generalGuidanceCommentsCount,
    setGeneralGuidanceCommentsCount,
    globalEditModeState,
    tenant,
    triggerFormValidation,
  } = useContext(CommunicationContext);
  const { t } = useTranslation();
  const drugSubstanceOptionList = useMemo(
    () => generateDrugSubstanceOptions(selectedProduct?.drugSubstances ?? [], t('Common.DrugSubstanceCode'), t('Common.DrugSubstanceName')),
    [selectedProduct?.drugSubstances, t]
  );

  const { fields, prepend, update, remove } = useFieldArray({
    control: methods.control,
    name: `comments`,
    keyName: 'key',
  });

  useEffect(() => {
    if (newCommentAdded) {
      myRef.current?.scrollIntoView({ behavior: 'smooth' });
      setNewCommentAdded(false);
    }
  }, [newCommentAdded]);

  const addComment = async () => {
    if (!(await triggerFormValidation())) return;
    let newComment = {};
    if (isGeneralGuidance) {
      newComment = { ...defaultComment };
      if (generalGuidanceCommentsCount !== undefined) {
        setGeneralGuidanceCommentsCount?.(generalGuidanceCommentsCount + 1);
      }
    } else {
      newComment = {
        ...defaultComment,
        drugSubstances: drugSubstanceOptionList,
        productId: selectedProduct?.id,
        productExtensions: [defaultProductExtension],
      };
    }
    handleNewCommentAdded(newComment);
  };

  const copyComment = (commentIx: number) => {
    const commentToCopy = methods.getValues(`comments.${commentIx}`);
    handleNewCommentAdded({
      ...commentToCopy,
      productExtensions: commentToCopy.productExtensions,
      id: 0,
      description: '',
      question: '',
      response: '',
    });
  };

  const handleNewCommentAdded = (newComment: Comment) => {
    prepend(newComment);
    setNewCommentAdded(true);
  };

  const deleteComment = async () => {
    setCommentDeleting('queued');
    if (dialogSettings.commentId) {
      await commentApiService.deleteComment(dialogSettings.commentId, tenant);
      toast.success(t('ManageCommunication.DeleteCommentSucceeded'));
    }

    setCommentDeleting('saved');
    remove(dialogSettings.index);
    const values = methods.getValues();
    methods.reset({
      ...values,
      comments: methods.getValues('comments'),
    });
    setDialogSettings({ isOpen: false });

    if (isNewCommunication) {
      onCommentDeleted?.();
    }
  };

  const confirmCommentDelete = (commentId: number, commentIx: number) => {
    setDialogSettings({ commentId: commentId, index: commentIx, isOpen: true });
  };

  const getLoadMoreButtonLabel = () => {
    const remainingCount = totalComments - (commentsPagedModel.page + 1) * commentsPagedModel.take;
    if (remainingCount <= commentsPagedModel.take) {
      return t('Common.LoadRemainingCommentsButtonLabel', {
        count: remainingCount,
      });
    }

    return t('Common.LoadCommentsButtonLabel', {
      count: commentsPagedModel.take,
      remaining: remainingCount,
    });
  };

  const showDeleteCommentButton = (fieldId?: number) => {
    if (fields.length === 1) return false;

    return fields.filter((f) => f.id && f.id > 0).length > 1 || fieldId === 0;
  };

  return (
    <ContentWrapper ref={myRef}>
      <HeadingWrapper>
        <Heading>{isGeneralGuidance ? t('ManageCommunication.GeneralGuidance') : selectedProduct?.name}</Heading>
        {!isNewCommunication && (
          <RightContent>
            <PhlexIconButton
              buttonType="add"
              title={t('ManageCommunication.AddComment')}
              onClick={(e: any) => {
                e.preventDefault();
                addComment();
              }}
              disabled={communication?.isCompleted || isCommunicationInEditState(globalEditModeState)}
            />
          </RightContent>
        )}
      </HeadingWrapper>
      {fields.map((field, index) => (
        <Container key={field.key}>
          {isGeneralGuidance ? (
            <NonProductCommentItem
              newCommentAdded={newCommentAdded}
              commentIx={index}
              methods={methods}
              nonProductCommentField={field}
              confirmCommentDelete={confirmCommentDelete}
              copyComment={copyComment}
              commentFormValues={commentFormValues}
              setCommentFormValues={setCommentFormValues}
              showDeleteComment={fields.filter((f) => f.id).length > 1 || productTabsCount > 0 || field.id === 0}
            />
          ) : (
            <CommentItem
              newCommentAdded={newCommentAdded}
              commentIx={index}
              methods={methods}
              update={update}
              commentField={field}
              showDeleteComment={showDeleteCommentButton(field.id)}
              confirmCommentDelete={confirmCommentDelete}
              copyComment={copyComment}
              drugSubstanceOptionList={drugSubstanceOptionList}
              commentFormValues={commentFormValues}
              setCommentFormValues={setCommentFormValues}
            />
          )}
        </Container>
      ))}
      <LoadMoreButtonWrapper>
        {isCommentsLoading && <PhlexLoader position="center" />}
        <PhlexButton
          title={t('Common.LoadCommentsButtonTitle')}
          label={getLoadMoreButtonLabel()}
          hidden={isLoadMoreHidden}
          disabled={isCommentsLoading}
          type="button"
          onClick={handleLoadMoreComments}
        />
      </LoadMoreButtonWrapper>
      <PhlexConfirmationDialog
        isOpen={dialogSettings.isOpen}
        onConfirm={() => deleteComment()}
        onCancel={() => setDialogSettings({ isOpen: false })}
        heading={t('CommunicationModals.DeleteCommentHeading')}
        description={t('CommunicationModals.DeleteCommentDescription')}
        feedbackStatus={commentDeleting}
      />
    </ContentWrapper>
  );
};

export default CommunicationCommentsComponent;
