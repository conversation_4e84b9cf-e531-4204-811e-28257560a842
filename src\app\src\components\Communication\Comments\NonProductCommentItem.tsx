import React, { FC, Fragment, useCallback, useContext, useEffect, useState } from 'react';
import components from './CommentItem.styles';
import { AxonMultiSelect } from 'components/AxonMultiSelect';
import { PhlexIcon } from 'phlex-core-ui';
import { AxonInput } from 'components/AxonInput';
import { fetchTags, mapCommentToCommentRequestModel } from '../communication-service';
import CommentText from './CommentText';
import { ManageCommunicationFormProps, NonProductCommentItemProps } from '../communication-form-types';
import { useTranslation } from 'react-i18next';
import CommunicationContext, { EditStatus } from 'context/CommunicationContext';
import { toast } from 'react-toastify';
import { commentApiService } from 'shared/api';
import CommentActions from './CommentActions';

const NonProductCommentItemComponent: FC<NonProductCommentItemProps> = (props: NonProductCommentItemProps) => {
  const {
    newCommentAdded,
    commentIx,
    methods,
    nonProductCommentField,
    confirmCommentDelete,
    copyComment,
    commentFormValues,
    setCommentFormValues,
    showDeleteComment,
  } = props;
  const {
    communication,
    tenant,
    globalEditModeState,
    setGlobalEditModeState,
    generalGuidanceCommentsCount,
    setGeneralGuidanceCommentsCount,
  } = useContext(CommunicationContext);
  const { t } = useTranslation();
  const { WideColumn, LinkContainer, CommentWrapper } = components;
  const [isEditMode, setIsEditMode] = useState<boolean>(
    !communication?.id ||
      (globalEditModeState.commentEditState.commentIndex === commentIx &&
        globalEditModeState.commentEditState.editStatus === EditStatus.InEdit)
  );

  const { control } = methods;

  const setQuestionIncludedValue = (index: number, val: boolean) => {
    methods.setValue(`comments.${index}.isQuestionIncluded`, val);
  };

  const handleCopyComment = () => {
    copyComment(commentIx);
    if (generalGuidanceCommentsCount !== undefined) {
      setGeneralGuidanceCommentsCount?.(generalGuidanceCommentsCount + 1);
    }
  };

  const onSubmitHandlerErrors = () => {
    toast.error(t('ManageCommunication.CreateUpdateValidationErrorsMessage'), {
      toastId: 'communication-errors',
    });
  };

  const onSubmit = async (values: ManageCommunicationFormProps) => {
    if (!values.comments) return;
    const communicationId = communication?.id ?? 0;
    const comment = values.comments[commentIx];
    const request = mapCommentToCommentRequestModel(comment, communicationId, true);

    try {
      if (comment?.id && comment.id > 0) {
        await commentApiService.updateComment(tenant, request);
        toast.success(t('ManageCommunication.UpdateCommentSucceeded'), {
          toastId: 'comment-updated',
        });
      } else {
        const response = await commentApiService.createComment(tenant, request);
        methods.setValue(`comments.${commentIx}`, { ...comment, id: response.data.id });
        toast.success(t('ManageCommunication.CreateCommentSucceeded'), {
          toastId: 'comment-created',
        });
      }
      methods.reset(methods.getValues(), { keepDirtyValues: true });
      resetEditMode(false);
    } catch (error: any) {
      toast.error(t('ManageCommunication.CreateFailedErrorMessage'));
    }
  };

  const handleEditCommentItem = useCallback(() => {
    resetEditMode(true);
    const formValues = methods.getValues(`comments.${commentIx}`);
    setCommentFormValues({ ...formValues });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [commentIx]);

  useEffect(() => {
    if (newCommentAdded && commentIx == 0) {
      handleEditCommentItem();
    }
  }, [newCommentAdded, commentIx, handleEditCommentItem]);

  const handleSaveCommentItem = () => {
    methods.handleSubmit(onSubmit, onSubmitHandlerErrors)();
  };

  const handleCancelEditCommentItem = () => {
    resetEditMode(false);
    const formValues = methods.getValues('comments');
    if (!formValues) return;

    formValues[commentIx] = commentFormValues ?? {};
    methods.reset({
      ...methods.getValues(),
      comments: formValues,
    });
  };

  const resetEditMode = (isEdit: boolean) => {
    setIsEditMode(isEdit);
    setGlobalEditModeState?.({
      ...globalEditModeState,
      commentEditState: { editStatus: isEdit ? EditStatus.InEdit : EditStatus.NotStarted, commentIndex: commentIx },
    });
  };

  const handleDeleteCommentItem = () => {
    confirmCommentDelete(nonProductCommentField.id ?? 0, commentIx);
    if (generalGuidanceCommentsCount !== undefined) {
      setGeneralGuidanceCommentsCount?.(generalGuidanceCommentsCount - 1);
    }
  };

  return (
    <Fragment>
      <CommentWrapper>
        <WideColumn>
          <CommentText
            commentIndex={commentIx}
            methods={methods}
            field={nonProductCommentField}
            isGeneralGuidance={true}
            setQuestionIncludedValue={setQuestionIncludedValue}
            isCommentInEditMode={isEditMode}
          />
        </WideColumn>
        <WideColumn>
          <AxonMultiSelect
            data-testid={`axon-input-non-product-comments.tag_${commentIx}`}
            name={`comments.${commentIx}.tags`}
            label={t('Common.TagsLabel') || ''}
            width="fullwidth"
            required={true}
            fetchData={() => fetchTags(tenant)}
            onlyFetchOnOpen={true}
            validationMessage={methods.formState.errors.comments?.[commentIx]?.tags?.message ?? undefined}
            disabled={communication?.isCompleted || !isEditMode}
          />
          <LinkContainer>
            <AxonInput
              urlValidation={true}
              data-testid={`axon-input-non-product-comments.[index]?.birdsLinkToBISAMP_${commentIx}`}
              width="fullwidth"
              label={t('ManageCommunication.BirdsLinkToBISAMPLabel')}
              name={`comments.${commentIx}.birdsLinkToBISAMP`}
              value={nonProductCommentField.birdsLinkToBISAMP ?? undefined}
              validationMessage={methods.formState.errors.comments?.[commentIx]?.birdsLinkToBISAMP?.message ?? undefined}
              disabled={communication?.isCompleted || !isEditMode}
              placeholder={t('ManageCommunication.UrlFormatPlaceholder')}
            />
            {nonProductCommentField.birdsLinkToBISAMP && (
              <a href={nonProductCommentField.birdsLinkToBISAMP} target="_blank" rel="noreferrer">
                <PhlexIcon name="open_in_new" tooltip={t('ManageCommunication.OpenUrlTooltip')} />
              </a>
            )}
          </LinkContainer>
          <LinkContainer>
            <AxonInput
              urlValidation={true}
              data-testid={`axon-input-non-product-comments.[index]?.birdsLinkToBIResponse_${commentIx}`}
              width="fullwidth"
              label={t('ManageCommunication.BirdsLinkToBIResponseLabel')}
              name={`comments.${commentIx}.birdsLinkToBIResponse`}
              value={nonProductCommentField.birdsLinkToBIResponse ?? undefined}
              validationMessage={methods.formState.errors.comments?.[commentIx]?.birdsLinkToBIResponse?.message ?? undefined}
              disabled={communication?.isCompleted || !isEditMode}
              placeholder={t('ManageCommunication.UrlFormatPlaceholder')}
            />
            {nonProductCommentField.birdsLinkToBIResponse && (
              <a href={nonProductCommentField.birdsLinkToBIResponse} target="_blank" rel="noreferrer">
                <PhlexIcon name="open_in_new" tooltip={t('ManageCommunication.OpenUrlTooltip')} />
              </a>
            )}
          </LinkContainer>
        </WideColumn>
      </CommentWrapper>
      <CommentActions
        isEditMode={isEditMode}
        commentIx={commentIx}
        showDeleteCommentIcon={showDeleteComment}
        showCopyCommentIcon={nonProductCommentField && !!nonProductCommentField.id}
        control={control}
        handleCopyComment={handleCopyComment}
        handleSaveCommentItem={handleSaveCommentItem}
        handleCancelEditCommentItem={handleCancelEditCommentItem}
        handleEditCommentItem={handleEditCommentItem}
        handleDeleteCommentItem={handleDeleteCommentItem}
      />
    </Fragment>
  );
};

export default NonProductCommentItemComponent;
