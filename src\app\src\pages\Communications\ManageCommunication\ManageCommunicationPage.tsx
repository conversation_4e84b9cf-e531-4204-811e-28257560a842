import ManageCommunicationPageActions from 'components/Communication/ManageCommunicationPageActions';
import CommunicationOverview from 'components/Communication/Overview/CommunicationOverview';
import { FormPrompt } from 'components/Modals/FormPrompt';
import { useUnsavedChangesPrompt } from 'components/shared/hooks';
import { useOrganisationFromUrl } from 'hooks/shared';
import { routes } from 'pages/routes';
import { PhlexBreadcrumb, PhlexLayout, PhlexLoader } from 'phlex-core-ui';
import React, { FunctionComponent, useCallback, useEffect, useMemo, useReducer } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import { communicationApiService, countryApiService, productApiService, submissionTypeApiService } from 'shared/api';
import components from './ManageCommunication.styles';
import { ManageCommunicationFormProps, ManageCommunicationPageData } from 'components/Communication/communication-form-types';
import { AxiosError } from 'axios';
import {
  mapApplicationNumbers,
  mapApplicationNumbersToChipListProps,
  mapCommentToCommentRequestModel,
} from 'components/Communication/communication-service';
import CommunicationContext, { EditStatus, GlobalEditModeState } from 'context/CommunicationContext';
import ProductTabs from 'components/Communication/Comments/ProductTabs';
import { ProductModel } from 'axon-hacomms-api-sdk';
import { withRestrictedPermissions } from 'hoc';
import { Permissions } from 'constants/permissions';
import { validateForm } from './validate-form-service';

const { PageTopBar, PageActions } = PhlexLayout;

const reducer = (state: ManageCommunicationPageData, newState: ManageCommunicationPageData) => {
  return { ...state, ...newState };
};

const ManageCommunicationPage: FunctionComponent<{ basepath: string }> = ({ basepath }) => {
  const { id } = useParams();
  const { t } = useTranslation();
  const { CommunicationContent } = components;
  const tenant = useOrganisationFromUrl();
  const navigate = useNavigate();

  const [data, setData] = useReducer(reducer, {
    formPromptState: { isVisible: false },
    newCommunicationId: 0,
    communicationStateChanged: false,
  });

  const unsavedChangesMessage = useMemo(() => t('Forms.UnsavedChangesMessage'), [t]);

  const methods = useForm<ManageCommunicationFormProps>({
    defaultValues: {
      applications: [],
      subject: '',
      submissionType: null,
      country: null,
      dateOfCommunication: null,
      comments: [],
    },
  });

  const confirmExit = (confirmNavigation: () => void) => {
    const onConfirm = () => {
      confirmNavigation();
    };

    setData({ formPromptState: { isVisible: true, onConfirm: onConfirm } });
  };

  useUnsavedChangesPrompt(unsavedChangesMessage, methods.formState.isDirty, confirmExit);

  const breadcrumbs = [
    {
      key: 'communications',
      text: t('Nav.Communications'),
      path: `${basepath}${routes.communications}`,
      active: 'false' as const,
    },
    {
      key: 'communication',
      text: id && data.communication?.subject ? data.communication?.subject : t('ManageCommunication.CreateCommunication'),
      active: 'true' as const,
    },
  ];

  const getSubmissionTypes = useCallback(async () => {
    const response = await submissionTypeApiService.getSubmissionTypeList(tenant);
    setData({ submissionTypes: response.data?.data ?? [], isSubmissionTypesLoading: false });
  }, [tenant]);

  const getCountries = useCallback(async () => {
    const response = await countryApiService.getCountryList(tenant);
    setData({ countries: response.data?.data ?? [], isCountriesLoading: false });
  }, [tenant]);

  useEffect(() => {
    setData({ isSubmissionTypesLoading: true, isCountriesLoading: true });
    getSubmissionTypes().catch(() => setData({ isSubmissionTypesLoading: false }));
    getCountries().catch(() => setData({ isCountriesLoading: false }));
  }, [getSubmissionTypes, getCountries]);

  const getProducts = useCallback(async () => {
    const productsResponse = await productApiService.getProductsList(tenant);
    setData({ products: productsResponse.data.data ?? [] });
  }, [tenant]);

  useEffect(() => {
    getProducts();
  }, [getProducts]);

  const getCommunication = useCallback(
    async (id: number) => {
      try {
        const communicationResponse = await communicationApiService.getCommunication(id, tenant);
        setData({
          communication: communicationResponse.data,
          generalGuidanceCommentsCount: communicationResponse.data.generalGuidanceCommentsCount,
        });
      } catch (error: any) {
        if (error.response?.data.Message?.includes('Internal Server Error') || error.response?.status === 400) {
          navigate(`${basepath}${routes.error}`);
        }
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [tenant]
  );

  useEffect(() => {
    if (id) {
      getCommunication(+id);
    }
  }, [id, getCommunication]);

  const setSelectedProduct = (product: ProductModel | undefined) => {
    setData({ selectedProduct: product });
  };

  const setGeneralGuidanceCommentsCount = (count: number) => {
    setData({ generalGuidanceCommentsCount: count });
  };

  useEffect(() => {
    if (id && !!data.communication?.products && data.communication.products.length > 0 && data.products && data.products.length > 0) {
      const productId = data.communication.products[0].id;
      const product = data.products.find((p) => p.id === productId);

      setData({ selectedProduct: product });
    }
  }, [id, data.communication, data.products]);

  useEffect(() => {
    if (data.communication) {
      const formValues = methods.getValues();
      const applications = mapApplicationNumbersToChipListProps(data.communication.applications ?? []);

      methods.reset({
        ...formValues,
        applications: applications,
        subject: data.communication.subject ?? '',
        submissionType: data.communication.submissionType,
        country: data.communication.country,
        dateOfCommunication: !data.communication.dateOfCommunication ? new Date() : new Date(data.communication.dateOfCommunication),
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data.communication]);

  useEffect(() => {
    if (data.newCommunicationId && !methods.formState.isDirty) {
      navigate(`${basepath}${routes.updateCommunication(data.newCommunicationId.toString())}`);
    }
  }, [data.newCommunicationId, basepath, navigate, methods.formState.isDirty]);

  useEffect(() => {
    if (data.communicationStateChanged && !methods.formState.isDirty) {
      navigate(`${basepath}${routes.communications}`);
    }
  }, [data.communicationStateChanged, basepath, navigate, methods.formState.isDirty]);

  const onSaveError = (error: AxiosError) => {
    if (error.response?.status === 401 || error.response?.status === 403) {
      navigate(`${basepath}${routes.forbidden}`);
    }
  };

  const triggerFormValidation = useCallback(async () => {
    if (!(await methods.trigger())) {
      toast.warn(t('ManageCommunication.RequiredFieldsWarning'), {
        toastId: 'required-fields-warning',
      });
      return false;
    }

    return true;
  }, [methods, t]);

  const deleteCommunication = async () => {
    try {
      await communicationApiService.deleteCommunication(data.communication?.id ?? 0, tenant);
      toast.success(t('ManageCommunication.DeleteCommunicationSucceeded'), {
        toastId: 'communication-deleted',
      });
      setData({ communicationStateChanged: true });
    } catch (error: any) {
      onSaveError(error);
      toast.error(t('ManageCommunication.DeleteFailedErrorMessage'));
    }

    setData({ isDeleteCommunicationDialogOpen: false });
  };

  const setValidationError = (fieldName: any, message: string) => {
    methods.setError(fieldName, {
      type: 'manual',
      message: message,
    });
  };

  const createCommunication = async () => {
    try {
      const values = methods.getValues();
      const formErrors = validateForm(values);
      if (formErrors && formErrors.length > 0) {
        formErrors.forEach((error) => {
          setValidationError(error.fieldName, t(error.message, { field: t(error.label) }));
        });
        return;
      }

      const request = {
        applications: mapApplicationNumbers(values.applications ?? []),
        submissionTypeId: values.submissionType?.id ?? 0,
        countryId: values.country?.id ?? 0,
        subject: values.subject ?? '',
        dateOfCommunication: values.dateOfCommunication != null ? values.dateOfCommunication.toDateString() : '',
        comment: values.comments ? mapCommentToCommentRequestModel(values.comments[0], 0, !values.comments[0].productId) : undefined,
      };

      const response = await communicationApiService.createCommunication(tenant, request);

      toast.success(t('ManageCommunication.CreateCommunicationSucceeded'), {
        toastId: 'communication-created',
      });
      methods.reset(methods.getValues(), { keepDirtyValues: true });
      setData({ newCommunicationId: response.data.id });
    } catch (error: any) {
      onSaveError(error);
      toast.error(t('ManageCommunication.CreateFailedErrorMessage'));
    }
  };

  const completeCommunication = async () => {
    try {
      const communicationId = data.communication?.id ?? 0;
      await communicationApiService.completeCommunication(communicationId, tenant);

      toast.success(t('ManageCommunication.CompleteSucceeded'), {
        toastId: 'communication-completed',
      });
      methods.reset();
      setData({ communicationStateChanged: true });
    } catch (error: any) {
      onSaveError(error);
      toast.error(t('ManageCommunication.CompleteFailedErrorMessage'));
    }

    setData({ isCompleteCommunicationDialogOpen: false });
  };

  const reinstateCommunication = async () => {
    try {
      const communicationId = data.communication?.id ?? 0;
      await communicationApiService.reinstateCommunication(communicationId, tenant);
      getCommunication(communicationId);

      toast.success(t('ManageCommunication.ReinstateSucceeded'), {
        toastId: 'communication-reinstated',
      });
    } catch (error: any) {
      onSaveError(error);
      toast.error(t('ManageCommunication.ReinstateFailedErrorMessage'));
    }

    setData({ isReinstateCommunicationDialogOpen: false });
  };

  const setGlobalEditModeState = (value: GlobalEditModeState) => {
    setData({ globalEditModeState: value });
  };

  const communicationContextValue = useMemo(
    () => ({
      communication: data.communication,
      selectedProduct: data.selectedProduct,
      countries: data.countries,
      submissionTypes: data.submissionTypes,
      products: data.products,
      globalEditModeState: data.globalEditModeState ?? {
        overviewEditState: EditStatus.NotStarted,
        commentEditState: { editStatus: EditStatus.NotStarted },
      },
      generalGuidanceCommentsCount: data.generalGuidanceCommentsCount,
      isNewCommunication: !data.communication?.id,
      setSelectedProduct,
      setGeneralGuidanceCommentsCount,
      setGlobalEditModeState,
      triggerFormValidation,
      tenant,
    }),
    [
      data.communication,
      data.selectedProduct,
      data.countries,
      data.submissionTypes,
      data.products,
      tenant,
      data.globalEditModeState,
      data.generalGuidanceCommentsCount,
      triggerFormValidation,
    ]
  );

  if (
    (id && (data.communication === undefined || data.products === undefined)) ||
    data.isCountriesLoading ||
    data.isSubmissionTypesLoading
  ) {
    return <PhlexLoader position="page" />;
  }

  return (
    <CommunicationContext.Provider value={communicationContextValue}>
      <FormProvider {...methods}>
        <form>
          <PageTopBar>
            <PhlexBreadcrumb options={breadcrumbs} />
            <PageActions>
              <ManageCommunicationPageActions
                isFormSubmitting={methods.formState.isSubmitting}
                onCommunicationDelete={deleteCommunication}
                onCommunicationCreate={createCommunication}
                onCommunicationReinstate={reinstateCommunication}
                onCommunicationComplete={completeCommunication}
              />
            </PageActions>
          </PageTopBar>
          <CommunicationContent>
            <CommunicationOverview methods={methods} />
            <ProductTabs methods={methods} />
          </CommunicationContent>
          <FormPrompt
            when={data.formPromptState?.isVisible ?? false}
            message={unsavedChangesMessage}
            onContinue={data.formPromptState?.onConfirm}
            onClose={() => setData({ formPromptState: { isVisible: false } })}
          />
        </form>
      </FormProvider>
    </CommunicationContext.Provider>
  );
};

export default withRestrictedPermissions(ManageCommunicationPage, [
  Permissions.ViewCommunication,
  Permissions.EditCommunication,
  Permissions.CreateCommunication,
  Permissions.DeleteCommunication,
]);
