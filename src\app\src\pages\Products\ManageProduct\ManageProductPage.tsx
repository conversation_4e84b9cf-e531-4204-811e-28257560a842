import { yupResolver } from '@hookform/resolvers/yup';
import {
  DrugSubstanceModel,
  ProductExtensionResponseModel,
  ProductModel,
  ProductTypeModel,
  RouteOfAdministrationModel,
} from 'axon-hacomms-api-sdk';
import ManageProductPageActions from 'components/Product/ManageProductPageActions';
import {
  AddProductExtensionMethod,
  ProductExtension,
  ProductFormProps,
  defaultProductExtension,
} from 'components/Product/product-form-types';
import ProductDetails from 'components/Product/ProductDetails';
import ProductExtensions from 'components/Product/ProductExtensions';
import areAllExtensionsInactive from 'components/Product/productExtensionUtils';
import { FormValidationError } from 'components/shared/types/types';
import { Permissions } from 'constants/permissions';
import { REGULAR_EXPRESSIONS } from 'constants/regexp';
import { withRestrictedPermissions } from 'hoc';
import { useOrganisationFromUrl } from 'hooks/shared';
import { routes } from 'pages';
import { PhlexBreadcrumb, PhlexConfirmationDialog, PhlexLayout, PhlexLoader } from 'phlex-core-ui';
import { IOption } from 'phlex-core-ui/build/src/controls/PhlexMultiSelect/PhlexMultiSelect';
import React, { Fragment, FunctionComponent, useCallback, useEffect, useMemo, useReducer, useRef } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import productApiService from 'shared/api/product-api-service';
import * as yup from 'yup';
import components from './ManageProduct.styles';
import { validateProduct } from './validate-product-service';

const { PageTopBar, PageActions } = PhlexLayout;

interface ManageProductPageData {
  product?: ProductModel;
  newProductId?: number;
  isDeleteProductDialogOpen?: boolean;
  productStateChanged?: boolean;
}

const reducer = (state: ManageProductPageData, newState: ManageProductPageData) => {
  return { ...state, ...newState };
};

const ManageProductPage: FunctionComponent<{ basepath: string }> = ({ basepath }) => {
  const { ProductContent } = components;
  const { id } = useParams();
  const { t } = useTranslation();
  const tenant = useOrganisationFromUrl();
  const navigate = useNavigate();
  const productExtensionsRef = useRef<AddProductExtensionMethod>(null);

  const [data, setData] = useReducer(reducer, {
    product: undefined,
    newProductId: 0,
    isDeleteProductDialogOpen: false,
    productStateChanged: false,
  });

  const isNewProduct = useMemo(() => !data.product || (data.product?.id as number) <= 0, [data.product]);
  const associatedToComments = useMemo(
    () => !data.product || data.product?.productExtensions?.some((p) => p.isAssociatedToComment),
    [data.product]
  );

  const breadcrumbs = [
    {
      key: 'manage',
      text: t('Nav.Manage'),
      path: `${basepath}${routes.products}`,
      active: 'false' as const,
    },
    {
      key: 'products',
      text: t('ProductTable.Title'),
      path: `${basepath}${routes.products}`,
      active: 'false' as const,
    },
    {
      key: 'product',
      text: id && data.product?.name ? data.product?.name : t('ManageProduct.CreateProduct'),
      active: 'true' as const,
    },
  ];

  const CreateNewProductSchema = yup.object().shape({
    id: yup.number(),
    name: yup
      .string()
      .trim()
      .required(t('Forms.RequiredField', { field: t('ManageProduct.ProductNameLabel') }))
      .matches(new RegExp(REGULAR_EXPRESSIONS.IllegalCharacters), t('Forms.SafeCharacters'))
      .max(30, t('Forms.InsertMaxCharacters', { number: '30' })),
    isActive: yup.bool().required(),
    substances: yup
      .array()
      .of(yup.object().shape({ label: yup.string().required(), value: yup.string().required() }))
      .required(t('Forms.RequiredField', { field: t('Common.DrugSubstancesLabel') }))
      .test('substances', t('Forms.RequiredField', { field: t('Common.DrugSubstancesLabel') }), (option) => !!option && option.length > 0),
    productTypes: yup
      .array()
      .of(yup.object().shape({ label: yup.string().required(), value: yup.string().required() }))
      .required(t('Forms.RequiredField', { field: t('Common.ProductTypesLabel') }))
      .test('productTypes', t('Forms.RequiredField', { field: t('Common.ProductTypesLabel') }), (option) => !!option && option.length > 0),
    productExtensions: yup
      .array()
      .of(
        yup.object().shape({
          id: yup.number(),
          pcid: yup
            .string()
            .trim()
            .test(
              'product-code-identifier-validation',
              t('Forms.InsertAlphanumeric'),
              (value) =>
                value === '' || value === t('Common.NotAssigned') || (value !== undefined && REGULAR_EXPRESSIONS.AlphaNumerical.test(value))
            )
            .max(15, t('Forms.InsertMaxCharacters', { number: '15' })),
          dosageForm: yup
            .object()
            .optional()
            .shape({ id: yup.number(), name: yup.string().required() })
            .test('dosage-form', t('Forms.RequiredField', { field: t('ProductExtension.DosageFormLabel') }), (val) => !val || !!val.id),
          routesOfAdministration: yup
            .array()
            .of(yup.object().shape({ value: yup.string().required(), label: yup.string().required() }))
            .test(
              'route-of-administration',
              t('Forms.RequiredField', { field: t('ProductExtension.RouteOfAdminLabel') }),
              (option) => !!option && option.length > 0
            ),
          isActive: yup.bool(),
          isAssociatedToComment: yup.bool(),
        })
      )
      .required(),
  });

  const methods = useForm<ProductFormProps>({
    resolver: yupResolver(CreateNewProductSchema),
    defaultValues: {
      name: '',
      isActive: true,
      productExtensions: [defaultProductExtension],
    },
  });

  const mapSubstanceResponse = (substances: DrugSubstanceModel[] | null | undefined) => {
    return (
      substances?.map((x: DrugSubstanceModel) => ({ label: x.code, value: x.id?.toString() } as { label: string; value: string })) ?? []
    );
  };

  const mapProductTypeResponse = (productTypes: ProductTypeModel[] | null | undefined) => {
    return (
      productTypes?.map((x: ProductTypeModel) => ({ label: x.name, value: x.id?.toString() } as { label: string; value: string })) ?? []
    );
  };

  const mapProductExtensionResponse = (productExtensions: ProductExtensionResponseModel[] | null | undefined) => {
    return (
      productExtensions?.map((x: ProductExtensionResponseModel) => ({
        id: x.id,
        pcid: x?.pcid ?? undefined,
        dosageForm: x.dosageForm,
        routesOfAdministration: x.routesOfAdministration?.map(
          (r: RouteOfAdministrationModel) => ({ value: r.id?.toString() ?? '', label: r.name } as IOption)
        ),
        isActive: x.isActive,
      })) ?? []
    );
  };

  const fetchProduct = useCallback(
    async (id: number) => {
      try {
        const productResponse = await productApiService.getProduct(id, tenant);
        setData({ product: productResponse.data });
        methods.setValue('name', productResponse.data.name);
        methods.setValue('isActive', productResponse.data.isActive ?? false);
        methods.setValue('substances', mapSubstanceResponse(productResponse.data.drugSubstances));
        methods.setValue('productTypes', mapProductTypeResponse(productResponse.data.productTypes));
        methods.setValue('productExtensions', mapProductExtensionResponse(productResponse.data.productExtensions));
      } catch (error: any) {
        if (error?.response?.data.Message?.includes('Internal Server Error') || error.response?.status === 400) {
          navigate(`${basepath}${routes.error}`);
        }
        console.error('Error fetching data:', error);
      }
    },
    [methods, tenant, navigate, basepath]
  );

  useEffect(() => {
    if (id) {
      fetchProduct(+id);
    }
  }, [id, fetchProduct]);

  const setValidationError = (fieldName: any, message: string) => {
    methods.setError(fieldName, {
      type: 'manual',
      message: message,
    });
  };

  const handleServerErrors = (error: any) => {
    const validationErrors = error.response?.data?.errors;
    let handledErrors = false;

    if (validationErrors?.NameAlreadyExists) {
      setValidationError('name', t('Forms.FieldAlreadyInUse', { field: t('ManageProduct.ProductNameLabel') }));
      handledErrors = true;
    }

    if (validationErrors?.PcidAlreadyExists) {
      setValidationError('name', t('Forms.FieldAlreadyInUse', { field: t('ProductExtension.ProductCodeLabel') }));
      handledErrors = true;
    }

    if (!handledErrors) {
      onSaveError();
    }

    if (error.response?.status === 401 || error.response?.status === 403) {
      navigate(`${basepath}${routes.forbidden}`);
    }
  };

  const onSaveError = () => {
    toast.error(t('ManageProduct.CreateFailedErrorMessage'));
  };

  const onSaveSuccess = (message: string, toastId: string) => {
    toast.success(message, { toastId: toastId });
  };

  const handleProductErrors = (errors: FormValidationError[], isProductActiveWithInactiveExtensions: boolean) => {
    errors.forEach((error) => setValidationError(error.fieldName, t(error.message, { field: t(error.label) })));

    if (isProductActiveWithInactiveExtensions) {
      toast.error(t('ProductExtension.AllInactiveError'));
    }
  };

  const onSubmit = async (values: ProductFormProps) => {
    const notAssigned = t('Common.NotAssigned');
    const errors = validateProduct(values);
    const isProductActiveWithInactiveExtensions = areAllExtensionsInactive(values.productExtensions) && values.isActive;
    if (errors.length > 0 || isProductActiveWithInactiveExtensions) {
      handleProductErrors(errors, isProductActiveWithInactiveExtensions);
      return;
    }

    const request = {
      id: data.product?.id ?? 0,
      name: values.name,
      isActive: values.isActive,
      drugSubstanceIds: values.substances.map((x) => +x.value),
      productTypeIds: values.productTypes.map((x) => +x.value),
      productExtensions: values.productExtensions.map((extension: ProductExtension) => ({
        id: extension.id,
        pcid: extension.pcid ? extension.pcid : notAssigned,
        dosageFormId: extension.dosageForm?.id,
        routeOfAdministrationIds: extension.routesOfAdministration?.map((r) => +r.value),
        isActive: extension.isActive,
      })),
      notAssigned: notAssigned,
    };

    try {
      if (id) {
        const updateResponse = await productApiService.updateProduct(tenant, request);
        if (!updateResponse?.data?.id) {
          onSaveError();
          return;
        }
        if (updateResponse?.data?.updatedCommentsCount != undefined && updateResponse?.data?.updatedCommentsCount > 0) {
          onSaveSuccess(t('ManageProduct.NumberOfUpdatedComments') + `${updateResponse?.data?.updatedCommentsCount}`, 'comments-updated');
        }

        onSaveSuccess(t('ManageProduct.UpdateSucceeded'), 'product-updated');
        fetchProduct(updateResponse.data.id);
      } else {
        const createResponse = await productApiService.createProduct(tenant, request);
        if (!createResponse?.data?.id) {
          onSaveError();
          return;
        }
        onSaveSuccess(t('ManageProduct.CreateSucceeded'), 'product-created');
        navigate(`${basepath}${routes.updateProduct(createResponse.data.id.toString())}`);
      }
    } catch (error: any) {
      handleServerErrors(error);
    }
  };

  useEffect(() => {
    if (methods.formState.isSubmitSuccessful) {
      methods.reset({
        name: methods.getValues('name'),
        isActive: methods.getValues('isActive'),
        substances: methods.getValues('substances'),
        productTypes: methods.getValues('productTypes'),
        productExtensions: methods.getValues('productExtensions'),
      });
    }
  }, [methods, methods.formState, methods.formState.isSubmitSuccessful]);

  const onProductStatusChange = (val: boolean) => {
    setProductStatus(val);
  };

  const setProductStatus = (val = false) => {
    if (data.product) {
      const currentProduct: ProductModel = { ...data.product, isActive: val };
      setData({ product: currentProduct });

      if (!val) {
        toast.warn(t('ManageProduct.ProductDisableWarning'));
      }
    }
  };

  useEffect(() => {
    if (data.productStateChanged && !methods.formState.isDirty) {
      navigate(`${basepath}${routes.products}`);
    }
  }, [data.productStateChanged, basepath, navigate, methods.formState.isDirty]);

  const deleteProduct = async () => {
    try {
      await productApiService.deleteProduct(data.product?.id ?? 0, tenant);
      toast.success(t('ManageProduct.DeleteSucceededMessage'), {
        toastId: 'product-deleted',
      });
      methods.reset({});
      setData({ productStateChanged: true });
    } catch (error: any) {
      onSaveError();
    }

    setData({ isDeleteProductDialogOpen: false });
  };

  const handleDeleteProduct = (e: any) => {
    e.preventDefault();
    setData({ isDeleteProductDialogOpen: true });
  };

  const handleAddProductExtension = () => {
    return productExtensionsRef.current?.AddProductExtension();
  };

  return (
    <Fragment>
      <FormProvider {...methods}>
        <form onSubmit={methods.handleSubmit(onSubmit)}>
          <PageTopBar>
            <PhlexBreadcrumb options={breadcrumbs} />
            <PageActions>
              <ManageProductPageActions
                isNewProduct={isNewProduct}
                isaAssociatedToComments={associatedToComments as boolean}
                cleanBasePath={basepath}
                methods={methods}
                handleDeleteProduct={handleDeleteProduct}
                handleAddProductExtension={handleAddProductExtension}
                data={data}
              />
            </PageActions>
          </PageTopBar>
          {id && data.product === undefined ? (
            <PhlexLoader position="page" />
          ) : (
            <ProductContent>
              <ProductDetails product={data.product} methods={methods} tenant={tenant} onProductStatusChange={onProductStatusChange} />
              <ProductExtensions
                ref={productExtensionsRef}
                product={data.product}
                methods={methods}
                tenant={tenant}
                setProductInactive={setProductStatus}
              />
            </ProductContent>
          )}
        </form>
      </FormProvider>
      <PhlexConfirmationDialog
        isOpen={data.isDeleteProductDialogOpen ?? false}
        onConfirm={() => deleteProduct()}
        onCancel={() => setData({ isDeleteProductDialogOpen: false })}
        heading={t('ProductModals.DeleteProductHeading')}
        description={t('ProductModals.DeleteProductDescription')}
      />
    </Fragment>
  );
};

export default withRestrictedPermissions(ManageProductPage, [
  Permissions.ViewProduct,
  Permissions.EditProduct,
  Permissions.CreateProduct,
  Permissions.DeleteProduct,
]);
