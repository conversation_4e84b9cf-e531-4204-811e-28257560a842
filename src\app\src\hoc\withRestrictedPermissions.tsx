import { routes } from 'pages';
import React, { useEffect } from 'react';
import { Navigate } from 'react-router-dom';
import { BasePathType } from 'routers/types';
import { authHelper } from 'shared/services';
import { useHacommsPermissionsStore } from 'store/permissions/permissionsStore';

type AccessType = 'all' | 'any';

type WithRestrictedPermissionsProps = {
  permissions: string[];
  accessType: AccessType;
  redirect: boolean;
};

function withRestrictedPermissions<T extends BasePathType>(WrappedComponent: React.ComponentType<T>, permissions: string[]) {
  const displayName = WrappedComponent.displayName ?? WrappedComponent.name ?? 'Component';

  const ComponentWithRestrictedPermissions = (props: Omit<T, keyof WithRestrictedPermissionsProps>) => {
    const {
      permissionsStatus,
      hacommsPermissions: userPermissions,
      actions: { getAllPermissions },
    } = useHacommsPermissionsStore();

    useEffect(() => {
      if (permissionsStatus === 'idle') {
        getAllPermissions();
      }
    }, [permissionsStatus, getAllPermissions]);

    if (permissionsStatus !== 'completed' || !userPermissions) return null;

    if (!authHelper.hasAccess(userPermissions, permissions)) {
      return <Navigate to={`${props.basepath}${routes.forbidden}`} />;
    }

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return <WrappedComponent {...(props as any)} />;
  };

  ComponentWithRestrictedPermissions.displayName = `withTheme(${displayName})`;

  return ComponentWithRestrictedPermissions;
}

export default withRestrictedPermissions;
