import { DrugSubstanceCodeAndName, DrugSubstanceModel } from 'axon-hacomms-api-sdk';

export const generateDrugSubstanceLabel = (
  name: string | undefined | null,
  code: string | undefined | null,
  codeLabel: string,
  nameLabel: string
) => {
  return `${codeLabel}: ${code} | ${nameLabel}: ${name}`;
};

export const generateDrugSubstanceOptions = (substances: DrugSubstanceModel[], codeLabel: string, nameLabel: string) =>
  substances.map((item) => ({
    value: item.id?.toString() ?? '',
    label: generateDrugSubstanceLabel(item.name, item.code, codeLabel, nameLabel),
  }));

export const generateDrugSubstanceLabelFromList = (
  drugSubstancesList: DrugSubstanceCodeAndName[],
  codeLabel: string,
  nameLabel: string
) => {
  const adjustedDrugSubstanceLabels: string[] = [];
  drugSubstancesList.forEach((item) => {
    adjustedDrugSubstanceLabels.push(generateDrugSubstanceLabel(item.name, item.code, codeLabel, nameLabel));
  });
  return adjustedDrugSubstanceLabels.join(', ');
};
