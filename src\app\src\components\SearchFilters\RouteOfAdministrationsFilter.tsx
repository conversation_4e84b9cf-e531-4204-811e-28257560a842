import { RouteOfAdministrationModel, SearchCommentCommandRequest } from 'axon-hacomms-api-sdk';
import { MultiselectData } from 'components/shared/types/types';
import { PhlexMultiSelect } from 'phlex-core-ui';
import React, { FC, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import routeOfAdministrationApiService from 'shared/api/route-of-administration-api-service';
import { ROUTE_OF_ADMINISTRATION_KEY, SearchFiltersProps } from './search-filter-types';
import { getFiltersFromStorage, updateFiltersInStorage } from './search-filter-service';

const RouteOfAdministrationsFilterComponent: FC<SearchFiltersProps> = (props: SearchFiltersProps) => {
  const { tenant, setSearchModel, disable, clear } = props;
  const { t } = useTranslation();
  const [selectedRouteOfAdministrations, setSelectedRouteOfAdministrations] = useState<MultiselectData>([]);

  const fetchRouteOfAdministrations = async (): Promise<MultiselectData> => {
    const response = await routeOfAdministrationApiService.getRouteOfAdministrationList(tenant);
    const data =
      response.data.data?.map((item: RouteOfAdministrationModel) => ({ value: item.id?.toString() ?? '', label: item.name ?? '' })) ?? [];
    return data;
  };

  const updateSearchModel = (data: MultiselectData) => {
    setSearchModel((request: SearchCommentCommandRequest) => ({
      ...request,
      routesOfAdministration: data.map((x) => ({ id: Number(x.value), name: x.label })),
      skip: 0,
      take: 10,
    }));
  };

  const onRouteOfAdministrationFilterChange = (data: MultiselectData) => {
    setSelectedRouteOfAdministrations(data);
    updateFiltersInStorage(data, ROUTE_OF_ADMINISTRATION_KEY);
    updateSearchModel(data);
  };

  useEffect(() => {
    const searchFilters = getFiltersFromStorage();
    if (searchFilters?.filters[ROUTE_OF_ADMINISTRATION_KEY] && searchFilters.filters[ROUTE_OF_ADMINISTRATION_KEY].length > 0) {
      const filterData = searchFilters.filters[ROUTE_OF_ADMINISTRATION_KEY];
      setSelectedRouteOfAdministrations(filterData);
      updateSearchModel(filterData);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (disable) {
      setSelectedRouteOfAdministrations([]);
      updateFiltersInStorage([], ROUTE_OF_ADMINISTRATION_KEY);
    }
  }, [disable]);

  useEffect(() => {
    if (clear) setSelectedRouteOfAdministrations([]);
  }, [clear]);

  return (
    <PhlexMultiSelect
      data-testid={'axon-input-search-filter-route-of-administration'}
      name={`routeOfAdministrationSearchFilter`}
      label={t('SearchFilters.RouteOfAdministration')}
      value={selectedRouteOfAdministrations}
      defaultValues={selectedRouteOfAdministrations}
      width="fullwidth"
      compactMode={true}
      fetchData={() => fetchRouteOfAdministrations()}
      onlyFetchOnOpen={true}
      onChange={onRouteOfAdministrationFilterChange}
      disabled={disable}
    />
  );
};

export default RouteOfAdministrationsFilterComponent;
