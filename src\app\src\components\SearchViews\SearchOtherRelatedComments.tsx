import React, { FC, useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import components from './SearchOtherRelatedComments.styles';
import { PagedProps, SearchOtherRelatedCommentsProps } from './search-view-types';
import { CommentDtoModel } from 'axon-hacomms-api-sdk';
import SearchCommentDetails from './SearchCommentDetails';
import { commentApiService } from 'shared/api';
import { PhlexButton, PhlexLoader } from 'phlex-core-ui';

const initialPagedModel: PagedProps = { page: 0, skip: 0, take: 10 };

const SearchOtherRelatedCommentsComponent: FC<SearchOtherRelatedCommentsProps> = (props: SearchOtherRelatedCommentsProps) => {
  const { Wrapper, Heading, HeadingWrapper, BoldProduct, OtherCommentWrapper, LoadMoreButtonWrapper } = components;
  const { communicationId, productId, excludedCommentId, tenant } = props;
  const { t } = useTranslation();
  const [comments, setComments] = useState<CommentDtoModel[]>([]);
  const [total, setTotal] = useState<number | null | undefined>();
  const [page, setPage] = useState<number>(0);
  const [loading, setLoading] = useState(false);
  const [pagedModel, setPagedModel] = useState<PagedProps>(initialPagedModel);

  const getComments = useCallback(
    async (communicationId: number, productId: number | undefined, excludedCommentId: number | undefined, pagedModel: PagedProps) => {
      setLoading(true);
      const response = await commentApiService.getCommentsByCommunicationId(
        communicationId,
        tenant,
        productId,
        excludedCommentId,
        pagedModel.skip,
        pagedModel.take
      );
      setComments((prevState) => [...prevState, ...(response.data.data ?? [])]);
      setTotal(response.data.paging?.totalItemCount);
      setPage((page) => page + 1);
      setLoading(false);
    },
    [tenant]
  );

  const loadMore = () => {
    setPagedModel((request: PagedProps) => ({
      ...request,
      page: page,
      skip: page * pagedModel.take,
    }));
  };

  useEffect(() => {
    if (communicationId) {
      getComments(communicationId, productId, excludedCommentId, pagedModel);
    }
  }, [communicationId, productId, excludedCommentId, getComments, pagedModel]);

  return (
    <>
      {total != null && total > 0 && (
        <Wrapper>
          <HeadingWrapper>
            <Heading>
              {t('SearchOtherRelatedComments.CommentLabel')}
              <BoldProduct>
                {comments != undefined && comments.length > 0 && !comments[0].isGeneralGuidance
                  ? comments[0].productName
                  : t('Common.GeneralGuidance')}
              </BoldProduct>
            </Heading>
          </HeadingWrapper>
          {comments?.map((comment: CommentDtoModel, index: number) => {
            return (
              <OtherCommentWrapper key={index + 1}>
                <SearchCommentDetails key={index + 1} comment={comment} number={index + 1} />
              </OtherCommentWrapper>
            );
          })}
          <LoadMoreButtonWrapper>
            {loading ? (
              <PhlexLoader position="center" />
            ) : (
              <PhlexButton
                title={t('Common.LoadMoreButtonLabel')}
                label={t('Common.LoadMoreButtonLabel')}
                hidden={total != null && total <= pagedModel.take + pagedModel.skip}
                onClick={() => {
                  loadMore();
                }}
              />
            )}
          </LoadMoreButtonWrapper>
        </Wrapper>
      )}
    </>
  );
};

export default SearchOtherRelatedCommentsComponent;
