import { AxonChipList } from 'components/AxonChipList';
import styled from 'styled-components';

export default {
  CommunicationPhlexChipList: styled(AxonChipList)`
    width: calc(50% - 0.375rem);
  `,
  ChipListWrapper: styled.div`
    display: flex;
    gap: 0.75rem;
    height: 15rem;
    width: calc(50% - 1.5rem);
    border-right: ${(props) => props.theme.border} ${(props) => props.theme.colors.backgroundSec};
    padding-right: 1rem;
  `,
};
