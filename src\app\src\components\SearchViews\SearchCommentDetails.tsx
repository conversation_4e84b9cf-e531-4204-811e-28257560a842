import React, { FC, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import components from './SearchCommentDetails.styles';
import { PhlexBadge, PhlexIcon } from 'phlex-core-ui';
import { SearchCommentDetailsProps } from './search-view-types';
import { generateDrugSubstanceLabel } from 'shared/services/drug-substance-srv';
import { DrugSubstanceCodeAndName, TagModel } from 'axon-hacomms-api-sdk';
import { getFiltersFromStorage } from 'components/SearchFilters/search-filter-service';
import { useHighlightText } from 'components/shared/hooks/useHighlightText';
import { toDateTimeFormat } from 'shared/services/date-time-format';

const SearchCommentDetailsComponent: FC<SearchCommentDetailsProps> = (props: SearchCommentDetailsProps) => {
  const {
    WideColumn,
    TextAreaContainer,
    Number<PERSON><PERSON>,
    Container,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON>s<PERSON><PERSON><PERSON>,
    SectionBorder,
    InfoSection,
    MainContainer,
  } = components;
  const { comment, number } = props;
  const { t } = useTranslation();
  const searchText = getFiltersFromStorage()?.searchText as string;
  const highlightedDescription = useHighlightText(comment?.description as string, searchText);
  const highlightedQuestion = useHighlightText(comment?.question as string, searchText);
  const highlightedResponse = useHighlightText(comment?.response as string, searchText);
  const [productCodes, setProductCodes] = useState<(string | null | undefined)[]>([]);
  const [dosageFormNames, setDosageFormNames] = useState<(string | null | undefined)[]>([]);
  const [productTypes, setProductTypes] = useState<(string | null | undefined)[]>([]);
  const [routesOfAdministrations, setRoutesOfAdministrations] = useState<(string | null | undefined)[]>([]);

  useEffect(() => {
    if (comment?.productExtensions) {
      const uniqueProductCodes = Array.from(new Set(comment.productExtensions.map((pe) => pe.pcid)));
      setProductCodes(uniqueProductCodes);

      const uniqueDosageFormNames = Array.from(new Set(comment.productExtensions.map((pe) => pe.dosageFormName)));
      setDosageFormNames(uniqueDosageFormNames);

      const uniqueProductTypes = Array.from(new Set(comment.productTypes?.map((pt) => pt)));
      setProductTypes(uniqueProductTypes);

      const uniqueRoutesOfAdministration = Array.from(
        new Set(comment.productExtensions?.flatMap((pe) => pe.routeOfAdministrations?.flatMap((roa) => roa.name)))
      );
      setRoutesOfAdministrations(uniqueRoutesOfAdministration);
    }
  }, [comment?.productExtensions, comment?.productTypes]);

  return (
    <MainContainer>
      <Container>
        {number > 0 && <NumberBox>#{number}</NumberBox>}
        <WideColumn>
          {comment?.isQuestionIncluded && (
            <QuestionIncludedWrapper>
              <QuestionWrapper>{t('SearchCommentDetails.QuestionLabel')}</QuestionWrapper>
              <TextAreaContainer>{highlightedQuestion ?? undefined} </TextAreaContainer>
              <AnswerWrapper>{t('SearchCommentDetails.AnswerLabel')}</AnswerWrapper>
              <TextAreaContainer>{highlightedResponse ?? undefined}</TextAreaContainer>
            </QuestionIncludedWrapper>
          )}
          {!comment?.isQuestionIncluded && <TextAreaContainer>{highlightedDescription ?? undefined}</TextAreaContainer>}
        </WideColumn>
        <WideColumn>
          {!comment?.isGeneralGuidance && (
            <>
              <ElementContainer>
                <LabelWrapper>{t('SearchCommentDetails.ProductExtensionLabel')}</LabelWrapper>
                <ChipsContainer>
                  {productCodes?.map((productCode) => {
                    return <PhlexBadge key={productCode} label={productCode ?? ''} size="small" statusColor="client" />;
                  })}
                </ChipsContainer>
              </ElementContainer>
              <ElementContainer>
                <LabelWrapper>{t('SearchCommentDetails.DosageFormLabel')}</LabelWrapper>
                <ChipsContainer>
                  {dosageFormNames?.map((dosageFormName) => {
                    return <PhlexBadge key={dosageFormName} label={dosageFormName ?? ''} size="small" statusColor="client" />;
                  })}
                </ChipsContainer>
              </ElementContainer>
              <ElementContainer>
                <LabelWrapper>{t('SearchCommentDetails.RouteOfAdministrationLabel')}</LabelWrapper>
                <ChipsContainer>
                  {routesOfAdministrations.map((name) => {
                    return <PhlexBadge key={name} label={name ?? ''} size="small" statusColor="client" />;
                  })}
                </ChipsContainer>
              </ElementContainer>
              <ElementContainer>
                <LabelWrapper>{t('SearchCommentDetails.DrugSubstancesLabel')}</LabelWrapper>
                <ChipsContainer>
                  {comment?.drugSubstances?.map((drugSubstance: DrugSubstanceCodeAndName) => {
                    return (
                      drugSubstance && (
                        <PhlexBadge
                          key={drugSubstance.code}
                          label={generateDrugSubstanceLabel(
                            drugSubstance.name,
                            drugSubstance.code,
                            t('Common.DrugSubstanceCode'),
                            t('Common.DrugSubstanceName')
                          )}
                          size="small"
                          statusColor="client"
                        />
                      )
                    );
                  })}
                </ChipsContainer>
              </ElementContainer>
              <ElementContainer>
                <LabelWrapper>{t('Common.ProductTypesLabel')}</LabelWrapper>
                <ChipsContainer>
                  {productTypes?.map((name) => {
                    return <PhlexBadge key={name} label={name ?? ''} size="small" statusColor="client" />;
                  })}
                </ChipsContainer>
              </ElementContainer>
            </>
          )}
          <ElementContainer>
            <LabelWrapper>{t('SearchCommentDetails.TagsLabel')}</LabelWrapper>
            <ChipsContainer>
              {comment?.tags?.map((t: TagModel) => {
                return t.name && <PhlexBadge key={t.name} label={t.name} size="small" statusColor="client" />;
              })}
            </ChipsContainer>
          </ElementContainer>
          {comment?.birdsLinkToBISAMP && (
            <ElementContainer>
              <LabelWrapper>{t('SearchCommentDetails.BirdsLinkToBISAMPLabel')}</LabelWrapper>
              <a href={comment?.birdsLinkToBISAMP} target="_blank" rel="noreferrer">
                <PhlexIcon name="open_in_new" tooltip={comment?.birdsLinkToBISAMP} />
              </a>
            </ElementContainer>
          )}
          {comment?.birdsLinkToBIResponse && (
            <ElementContainer>
              <LabelWrapper>{t('SearchCommentDetails.BirdsLinkToBIResponseLabel')}</LabelWrapper>
              <a href={comment?.birdsLinkToBIResponse} target="_blank" rel="noreferrer">
                <PhlexIcon name="open_in_new" tooltip={comment?.birdsLinkToBIResponse} />
              </a>
            </ElementContainer>
          )}
        </WideColumn>
      </Container>
      <SectionBorder />
      <InfoSection>
        {t('ManageCommunication.CommentCreatedOnField', {
          createdOn: toDateTimeFormat(comment?.createdDate, 'DD MMM yyyy [at] HH:mma'),
          createdBy: comment?.createdBy,
        })}
        {t('ManageCommunication.CommentLastUpdatedOnField', {
          lastUpdatedOn: toDateTimeFormat(comment?.lastUpdatedDate, 'DD MMM yyyy [at] HH:mma'),
          lastUpdatedBy: comment?.lastUpdatedBy,
        })}
      </InfoSection>
    </MainContainer>
  );
};

export default SearchCommentDetailsComponent;
