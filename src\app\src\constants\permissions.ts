export const Permissions = {
  CanSearchComments: 'CanSearchComments',

  ViewCommunication: 'ViewCommunication',
  EditCommunication: 'EditCommunication',
  CreateCommunication: 'CreateCommunication',
  DeleteCommunication: 'DeleteCommunication',

  ViewCountry: 'ViewCountry',

  ViewProduct: 'ViewProduct',
  ViewProductList: 'ViewProductList',
  EditProduct: 'EditProduct',
  CreateProduct: 'CreateProduct',
  DeleteProduct: 'DeleteProduct',
  ViewProductCode: 'ViewProductCode',

  ViewTag: 'ViewTag',
  CreateTag: 'CreateTag',
  EditTag: 'EditTag',
  DeleteTag: 'DeleteTag',

  ViewDosageForm: 'ViewDosageForm',
  CreateDosageForm: 'CreateDosageForm',
  EditDosageForm: 'EditDosageForm',
  DeleteDosageForm: 'DeleteDosageForm',

  ViewRouteOfAdministration: 'ViewRouteOfAdministration',
  CreateRouteOfAdministration: 'CreateRouteOfAdministration',
  EditRouteOfAdministration: 'EditRouteOfAdministration',
  DeleteRouteOfAdministration: 'DeleteRouteOfAdministration',

  ViewSubstance: 'ViewSubstance',
  ViewSubstanceList: 'ViewSubstanceList',
  EditSubstance: 'EditSubstance',
  CreateSubstance: 'CreateSubstance',
  DeleteSubstance: 'DeleteSubstance',

  ViewDrugType: 'ViewDrugType',

  ViewApplicationNumber: 'ViewApplicationNumber',
  ViewSubmissionNumber: 'ViewSubmissionNumber',

  ViewSubmissionType: 'ViewSubmissionType',

  ViewProductType: 'ViewProductType',
};

export const manageDataPermissionsArray = [
  Permissions.ViewCommunication,
  Permissions.ViewProduct,
  Permissions.ViewSubstance,
  Permissions.ViewRouteOfAdministration,
  Permissions.ViewTag,
  Permissions.ViewDosageForm,
];
