import { PayloadAction, createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { permissionApi } from 'components/shared/api/permissionApi';
import { Permissions } from 'constants/permissions';
import { arrayOfPermissions } from 'shared/services/permissionsUtils';
import appConfig from '../../shared/config';

interface IPermissionsInitialState {
  hacommsPermissions: Array<string>;
  permissionsStatus: PermissionsStatus;
}

const initialState: IPermissionsInitialState = {
  hacommsPermissions: [],
  permissionsStatus: 'idle',
};

export type PermissionsStatus = 'idle' | 'loading' | 'completed';

export const getPermissionsAsync = createAsyncThunk<string[], { tenant: string }>(`hacomms/permissions`, async ({ tenant }) => {
  const hacommsPermissions = Object.keys(Permissions);

  const permissions = await permissionApi
    .getUserPermissions(hacommsPermissions, tenant)
    .then((resp) => {
      return arrayOfPermissions(resp.data?.data?.permissions) || ([] as string[]);
    })
    .catch(() => {
      return arrayOfPermissions(appConfig.LOCAL_PERMISSIONS as any) ?? ([] as string[]);
    });

  return permissions;
});

export const permissionsSlice = createSlice({
  name: 'hacomms_permissions',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder.addCase(getPermissionsAsync.pending, (state) => {
      state.permissionsStatus = 'loading';
    });
    builder.addCase(getPermissionsAsync.fulfilled, (state, action: PayloadAction<string[]>) => {
      state.permissionsStatus = 'completed';
      state.hacommsPermissions = action.payload;
    });
  },
});

const permissionsActions = permissionsSlice.actions;

export { permissionsActions };

export default permissionsSlice.reducer;
