import styled from 'styled-components';

export default {
  DosageFormContainer: styled.div`
    display: flex;
    flex-direction: column;
    padding-top: ${(props) => props.theme.spaceM};
    position: relative;
  `,
  LabelWrapper: styled.div``,
  TextWrapper: styled.div`
    margin-bottom: 1rem;
  `,
  SectionWrapper: styled.div`
    margin-bottom: 1rem;
  `,
  ProductExtensionWrapper: styled.div`
    background-color: ${(props) => props.theme.colors.backgroundSec};
    border-radius: ${(props) => props.theme.radius};
    position: relative;
    flex: 1;
  `,
  ProductExtensionContainer: styled.div`
    padding: 2rem 4rem;
  `,
  ScrollViewWrapper: styled.div`
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    position: relative;
  `,
  PagingInfoWrapper: styled.div`
    position: absolute;
    bottom: 0;
    right: 0;
    left: 0;
    display: flex;
    justify-content: space-between;
    text-align: center;
    width: auto;
    height: 30px;
  `,
  PagingInfo: styled.div`    
    position: absolute;
    right: 50%;
}`,
  ActionsWrapper: styled.div`
    display: flex;
    justify-content: end;
    gap: 0.5rem;
    margin-bottom: 1rem;
  `,
  LeftArrowContainer: styled.div`
    position: absolute;
    left: 0;
  `,
  RightArrowContainer: styled.div`
    position: absolute;
    right: 0;
  `,
  ErrorWrapper: styled.div`
    padding: ${({ theme }) => theme.spaceM};
    background-color: ${({ theme }) => theme.colors.feedbackRedLight};
    border: ${({ theme }) => `${theme.border} ${theme.colors.feedbackRed}`};
    border-radius: ${({ theme }) => theme.radius};
    display: flex;
    flex-direction: column;
    gap: ${({ theme }) => theme.spaceM};
    justify-content: center;
    align-items: center;
    color: ${({ theme }) => theme.colors.feedbackRed};
    user-select: none;
  `,
};
