import { DosageFormModel, ProductPagedListModel } from 'axon-hacomms-api-sdk';
import { MultiselectData } from 'components/shared/types/types';
import { IOption } from 'phlex-core-ui/build/src/controls/PhlexMultiSelect/PhlexMultiSelect';
import { UseFormReturn } from 'react-hook-form';

export interface ProductPagedListModelExtended extends ProductPagedListModel {
  expanded: boolean;
}

export interface ProductExtension {
  id?: number;
  pcid?: string;
  dosageForm?: DosageFormModel;
  routesOfAdministration?: IOption[];
  isActive?: boolean;
  isAssociatedToComment?: boolean;
}

export const defaultProductExtension: ProductExtension = {
  id: 0,
  pcid: undefined,
  dosageForm: undefined,
  isActive: true,
  isAssociatedToComment: false,
};

export interface ProductFormProps {
  id?: number;
  name: string;
  isActive: boolean;
  substances: MultiselectData;
  productTypes: MultiselectData;
  productExtensions: ProductExtension[];
}

export interface AddProductExtensionMethod {
  AddProductExtension(): void;
}

export interface ManageProductPageActionsProps {
  isNewProduct: boolean;
  isaAssociatedToComments: boolean;
  cleanBasePath: string;
  methods: UseFormReturn<ProductFormProps, any>;
  handleDeleteProduct: (e: any) => void;
  handleAddProductExtension: () => void;
  data: any;
}
