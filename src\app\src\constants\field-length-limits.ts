// Field length constants organized by character limit
export const FIELD_LENGTH_15 = 15;
export const FIELD_LENGTH_30 = 30;
export const FIELD_LENGTH_50 = 50;
export const FIELD_LENGTH_100 = 100;
export const FIELD_LENGTH_500 = 500;

export const FIELD_LENGTH_LIMITS = {
  // 15 character fields
  PRODUCT_CODE_IDENTIFIER: FIELD_LENGTH_15,

  // 30 character fields
  PRODUCT_NAME: FIELD_LENGTH_30,

  // 50 character fields
  SUBSTANCE_CODE: FIELD_LENGTH_50,
  TAG_NAME: FIELD_LENGTH_50,
  ROUTE_OF_ADMINISTRATION_NAME: FIELD_LENGTH_50,
  DOSAGE_FORM_NAME: FIELD_LENGTH_50,

  // 100 character fields
  SUBSTANCE_NAME: FIELD_LENGTH_100,

  // 500 character fields
  SUBSTANCE_DESCRIPTION: FIELD_LENGTH_500,
  TAG_DESCRIPTION: FIELD_LENGTH_500,
};
