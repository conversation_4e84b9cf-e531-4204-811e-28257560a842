import { AxonDatePicker } from 'components/AxonDatePicker';
import { PhlexIcon, PhlexLoader } from 'phlex-core-ui';
import styled from 'styled-components';

export default {
  CommunicationOverview: styled.div`
    display: flex;
  `,
  CommunicationOverviewContainer: styled.div`
    display: flex;
    gap: 1.5rem;
    flex: 1 0 0;
    background-color: ${(props) => props.theme.colors.backgroundPri};
    border-radius: ${(props) => props.theme.radius};
    box-shadow: ${(props) => props.theme.shadow};
    margin-top: 1rem;
    margin-bottom: 1.25rem;
    padding: 1rem;
  `,
  CommunicationDatePicker: styled(AxonDatePicker)``,
  SubjectInputWrapper: styled.div``,
  OverviewRightContent: styled.div`
    display: flex;
    flex-direction: column;
    gap: 10px;
    flex: 1 0 0;
    position: relative;
  `,
  ContentWrapper: styled.div`
    display: flex;
    gap: 1rem;
    padding-top: 10px;
  `,
  ContentItem: styled.div`
    flex: 1 0 0;
  `,
  VerticalOuterSeparator: styled.div`
    margin-top: -1rem;
    margin-bottom: -1rem;
  `,
  ActionColumn: styled.div`
    display: flex;
    align-items: center;
    flex-direction: column;
    padding-left: 0.75rem;
    padding-top: 1rem;
    min-width: 30px;
  `,
  ActionButton: styled.div``,
  ActionIcon: styled(PhlexIcon)``,
  ActionLoader: styled(PhlexLoader)`
    border-top-color: #000;
    width: 1.25rem;
    height: 1.25rem;
  `,
  InfoSection: styled.div`
    position: absolute;
    bottom: 0;
    right: 0;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    justify-content: end;
    text-align: end;
    font-size: ${(props) => props.theme.textXS};
  `,
};
