import React, { FC, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { SearchFiltersProps } from './search-filter-types';
import { SearchCommentCommandRequest } from 'axon-hacomms-api-sdk';
import { getFiltersFromStorage, updateIsQuestionIncludedInStorage } from './search-filter-service';
import { DropDownListChangeEvent } from '@progress/kendo-react-dropdowns';
import { PhlexDropdown } from 'phlex-core-ui';

const IsQuestionIncludedFilterComponent: FC<SearchFiltersProps> = (props: SearchFiltersProps) => {
  const { setSearchModel, clear } = props;
  const { t } = useTranslation();
  const [selectedQuestionOption, setSelectedQuestionOption] = useState<boolean | undefined | null>(undefined);

  const questionOptions = [
    { id: 1, value: null, label: t('SearchFilters.QuestionFilterOptions.All') },
    { id: 2, value: false, label: t('SearchFilters.QuestionFilterOptions.CommentsOnly') },
    { id: 3, value: true, label: t('SearchFilters.QuestionFilterOptions.QuestionIncluded') },
  ];

  const updateSearchModel = (data: boolean | null) => {
    setSearchModel((request: SearchCommentCommandRequest) => ({
      ...request,
      isQuestionIncluded: data ?? undefined,
      skip: 0,
      take: 10,
    }));
  };

  const onIsQuestionIncludedFilterChange = (data: DropDownListChangeEvent) => {
    const selectedOption = data.value.value;
    setSelectedQuestionOption(selectedOption);
    updateIsQuestionIncludedInStorage(selectedOption);
    updateSearchModel(selectedOption);
  };

  useEffect(() => {
    const searchFilters = getFiltersFromStorage();
    if (searchFilters?.isQuestionIncluded != undefined) {
      setSelectedQuestionOption(searchFilters.isQuestionIncluded);
      updateSearchModel(searchFilters.isQuestionIncluded);
    } else {
      setSelectedQuestionOption(undefined);
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (clear) setSelectedQuestionOption(undefined);
  }, [clear]);

  return (
    <PhlexDropdown
      data-testid={'axon-input-search-filter-isQuestionIncluded'}
      name={`isQuestionIncludedSearchFilter`}
      label={t('SearchFilters.QuestionFilterOptions.Label')}
      value={questionOptions.filter((x) => x.value == selectedQuestionOption)[0]}
      width="fullwidth"
      data={questionOptions}
      textField="label"
      dataItemKey="id"
      onChange={onIsQuestionIncludedFilterChange}
    />
  );
};

export default IsQuestionIncludedFilterComponent;
