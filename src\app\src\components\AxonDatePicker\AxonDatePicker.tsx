import React, { FC } from 'react';
import { useForm<PERSON>ontext, useController } from 'react-hook-form';
import { PhlexDatePicker } from 'phlex-core-ui';
import { DatePickerChangeEvent } from '@progress/kendo-react-dateinputs';
import { DatePickerProps } from 'phlex-core-ui/build/src/controls/PhlexDatePicker/PhlexDatePicker';
import { useTranslation } from 'react-i18next';

interface IRHFDatePickerProps extends DatePickerProps {
  name: string;
}

const Component: FC<IRHFDatePickerProps> = ({ name, onChange, label, required, ...rest }) => {
  const { t } = useTranslation();
  const { control, trigger } = useFormContext();

  const {
    field: { ...inputProps },
  } = useController({
    name,
    control,
    rules: { required: required ? t('Forms.RequiredField', { field: label }) : '' },
  });

  const onChangeHandler = (e: DatePickerChangeEvent) => {
    inputProps.onChange(e);
    onChange?.(e);
    trigger().catch((e) => console.log(`Error in datepicker onchangehandler ${e}`));
  };

  return <PhlexDatePicker {...rest} value={inputProps.value} onChange={onChangeHandler} name={name} label={label} required={required} />;
};
export default Component;
