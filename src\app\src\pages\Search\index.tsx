import { SortDescriptor } from '@progress/kendo-data-query';
import { GridPageChangeEvent, GridSortChangeEvent } from '@progress/kendo-react-grid';
import { SearchComment, SearchCommentCommandRequest } from 'axon-hacomms-api-sdk';
import { MainContent } from 'components/Content/Content.styles';
import SearchColumnsModal from 'components/Modals/SearchColumnsModal';
import SearchFilters from 'components/SearchFilters/SearchFilters';
import SearchTable from 'components/SearchViews/SearchTable';
import SearchExcelExport from 'components/SearchViews/SearchExcelExport';
import { useOrganisationFromUrl } from 'hooks/shared';
import { PhlexBreadcrumb, PhlexButton, PhlexLayout, PhlexLoader } from 'phlex-core-ui';
import React, { Fragment, FunctionComponent, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import commentApiService from 'shared/api/comment-api-service';
import { TableColumn } from 'phlex-core-ui/build/src/controls/PhlexTable/PhlexTable.types';
import Axios, { CancelTokenSource } from 'axios';
import { getSearchColumnsFromStorage, updateSearchColumnsFromStorage } from 'components/SearchFilters/search-filter-service';
import { MultiselectData } from 'components/shared/types/types';
import { withRestrictedPermissions } from 'hoc';
import { Permissions } from 'constants/permissions';
import sharedComponents from 'pages/index.styles';
import { auditEventSource } from 'shared/consts';

const initialSort: Array<SortDescriptor> = [];

const { HaCommsPageTopBar } = sharedComponents;

const SearchPage: FunctionComponent<{ basepath: string }> = ({ basepath }) => {
  const tenant = useOrganisationFromUrl();
  const { PageActions } = PhlexLayout;
  const { t } = useTranslation();
  const [searchModel, setSearchModel] = useState<SearchCommentCommandRequest>({
    skip: 0,
    take: 10,
  } as SearchCommentCommandRequest);
  const [total, setTotal] = useState<number | null | undefined>();
  const [data, setData] = useState<SearchComment[] | null | undefined>([]);
  const [sort, setSort] = useState(initialSort);
  const [loading, setLoading] = useState(false);
  const [tableHeight, setTableHeight] = useState<number | undefined>(undefined);
  const [columnSelectionVisible, setColumnSelectionVisible] = useState(false);
  const [cancelToken, setCancelToken] = useState<CancelTokenSource>();

  const allColumns: TableColumn[] = [
    { identifier: 'applicationNumberNames', title: t('SearchTable.ApplicationNumber'), showTooltip: true, width: '200' },
    { identifier: 'countryName', title: t('SearchTable.Country'), showTooltip: true, width: '200' },
    { identifier: 'dateOfCommunication', title: t('SearchTable.DateOfCommunication'), showTooltip: true, width: '200' },
    { identifier: 'dosageFormNames', title: t('SearchTable.DosageForm'), showTooltip: true, width: '200' },
    { identifier: 'drugSubstanceNames', title: t('SearchTable.DrugSubstances'), showTooltip: true, width: '200' },
    { identifier: 'isGeneralGuidance', title: t('SearchTable.GeneralGuidance'), showTooltip: true, width: '200' },
    { identifier: 'productCodeNames', title: t('SearchTable.ProductCode'), showTooltip: true, width: '200' },
    { identifier: 'productName', title: t('SearchTable.ProductName'), showTooltip: true, width: '200' },
    { identifier: 'isQuestionIncluded', title: t('SearchTable.QuestionIncluded'), showTooltip: true, width: '200' },
    { identifier: 'routeOfAdministrationNames', title: t('SearchTable.RoutesOfAdministration'), showTooltip: true, width: '200' },
    { identifier: 'submissionNumberNames', title: t('SearchTable.SubmissionNumber'), showTooltip: true, width: '200' },
    { identifier: 'submissionTypeName', title: t('SearchTable.SubmissionType'), showTooltip: true, width: '200' },
    { identifier: 'tagNames', title: t('SearchTable.Tags'), showTooltip: true, width: '200' },
    { identifier: 'productTypeNames', title: t('SearchTable.ProductTypes'), showTooltip: true, width: '200' },
    { identifier: 'birdsLinkToBISAMP', title: t('SearchTable.BirdsLinkToBISAMP'), showTooltip: true, width: '200' },
    { identifier: 'question', title: t('SearchTable.Question'), showTooltip: true, width: '200' },
    { identifier: 'response', title: t('SearchTable.Response'), showTooltip: true, width: '200' },
    {
      identifier: 'birdsLinkToBIResponse',
      title: t('SearchTable.BirdsLinkToBIResponse'),
      showTooltip: true,
      width: '200',
    },
  ].sort((column1, column2) => column1.title.localeCompare(column2.title));

  const requiredColumns: TableColumn[] = [
    { identifier: 'description', title: t('SearchTable.Comments'), showTooltip: true, lock: true, width: '300' },
  ];

  const predefinedColumnNames = ['productName', 'countryName', 'tagNames'];

  const [columns, setColumns] = useState(allColumns.filter((x) => predefinedColumnNames.includes(x.identifier)));

  const breadcrumbs = [
    {
      key: 'search',
      text: t('Nav.Search'),
      active: 'false' as const,
    },
    {
      key: 'comments',
      text: t('Nav.Comments'),
      active: 'true' as const,
    },
  ];

  useEffect(() => {
    setTableHeight(undefined);
    const cachedSearchColumns = getSearchColumnsFromStorage();
    if (cachedSearchColumns) {
      setColumns(cachedSearchColumns);
    }
    const fetchData = async () => {
      setLoading(true);
      const options = { cancelToken: newCancelToken.token };
      const response = await commentApiService.searchComments(
        tenant,
        { ...searchModel, requestType: auditEventSource.SEARCH_LIST },
        options
      );
      setData(response.data.comments);
      setTotal(response.data.total);
      setTableHeight(response.data.comments && response.data.comments?.length > 0 ? 600 : undefined);
      setLoading(false);
    };

    if (cancelToken) {
      cancelToken.cancel('Search request cancelled.');
    }

    const newCancelToken = Axios.CancelToken.source();
    setCancelToken(newCancelToken);
    fetchData().catch((error: any) => {
      setLoading(false);
      console.error(`Error getting search results: ${error}`);
    });

    return () => {
      if (newCancelToken) {
        newCancelToken.cancel();
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [tenant, searchModel]);

  const onPageChange = (event: GridPageChangeEvent) => {
    setSearchModel((request: SearchCommentCommandRequest) => ({
      ...request,
      take: event.page.take,
      skip: event.page.skip,
    }));
  };

  const onSortChange = (event: GridSortChangeEvent) => {
    if (event.sort.length > 0) {
      setSort(event.sort);
      setSearchModel((request: SearchCommentCommandRequest) => ({
        ...request,
        sort: event.sort[0]?.field + ' ' + event.sort[0]?.dir,
      }));
    } else {
      setSort(initialSort);
      setSearchModel((request: SearchCommentCommandRequest) => ({
        ...request,
        sort: '',
      }));
    }
  };

  const onAddColumnClick = () => {
    setColumnSelectionVisible(true);
  };

  const onSelectedColumns = (selectedColumnNames: string[]) => {
    const selectedColumns = allColumns.filter((x) => selectedColumnNames.find((sc) => sc === x.identifier));
    setColumns(selectedColumns);
    updateSearchColumnsFromStorage(selectedColumns);
    closeColumnsModal();
  };

  const closeColumnsModal = () => {
    setColumnSelectionVisible(false);
  };

  const onColumnsModalClose = () => {
    closeColumnsModal();
  };

  const mapSelectedColumns = () => {
    if (columns.length === allColumns.length) {
      return [{ value: '-1', label: 'All' }] as MultiselectData;
    }
    return columns.map((column) => ({ value: column.identifier, label: column.title }));
  };

  return (
    <Fragment>
      <HaCommsPageTopBar>
        <PhlexBreadcrumb options={breadcrumbs} />
        <PageActions>
          <PhlexButton type="button" onClick={onAddColumnClick} title={t('Common.Columns')} label={t('Common.Columns')}></PhlexButton>
          <SearchExcelExport tenant={tenant} searchModel={searchModel} disabled={loading || !total || total <= 0}></SearchExcelExport>
        </PageActions>
      </HaCommsPageTopBar>
      <MainContent>
        <SearchFilters tenant={tenant} setSearchModel={setSearchModel}></SearchFilters>
        {loading ? (
          <PhlexLoader position="center" />
        ) : (
          <SearchTable
            data={data as SearchComment[]}
            onPageChange={onPageChange}
            onSortChange={onSortChange}
            sort={sort}
            total={total as number}
            skip={searchModel.skip as number}
            take={searchModel.take as number}
            cleanBasePath={basepath}
            columns={[...requiredColumns, ...columns]}
            height={tableHeight}
          ></SearchTable>
        )}
        {columnSelectionVisible && (
          <SearchColumnsModal
            columns={allColumns.map((column) => ({ value: column.identifier, label: column.title }))}
            selectedColumns={mapSelectedColumns()}
            onSelectedColumns={onSelectedColumns}
            onClose={onColumnsModalClose}
          ></SearchColumnsModal>
        )}
      </MainContent>
    </Fragment>
  );
};

export default withRestrictedPermissions(SearchPage, [Permissions.CanSearchComments]);
