import React, { FC, useEffect, useState } from 'react';
import components from './CommunicationDatesFilter.styles';
import { useTranslation } from 'react-i18next';
import { DateFormats } from 'shared/consts';
import { DateRangePickerChangeEvent, SelectionRange } from '@progress/kendo-react-dateinputs';
import { DateRangePickerGlobalStyle } from 'phlex-core-ui';
import { SearchFiltersProps } from './search-filter-types';
import { SearchCommentCommandRequest } from 'axon-hacomms-api-sdk';
import { getFiltersFromStorage, updateCommunicationDatesInStorage } from './search-filter-service';

const CommunicationDatesFilterComponent: FC<SearchFiltersProps> = (props: SearchFiltersProps) => {
  const { setSearchModel, clear } = props;
  const { t } = useTranslation();
  const { DateRangeWrapper, DatesFilter } = components;
  const [selectedCommunicationDates, setSelectedCommunicationDates] = useState<SelectionRange | null>();

  const updateSearchModel = (selectedRange: SelectionRange) => {
    setSearchModel((request: SearchCommentCommandRequest) => ({
      ...request,
      startDate: selectedRange.start?.toDateString(),
      endDate: selectedRange.end?.toDateString(),
      skip: 0,
      take: 10,
    }));
  };

  const onDateOfCommunicationChange = (event: DateRangePickerChangeEvent) => {
    setSelectedCommunicationDates(event.value);
    const communicationDateRange = {
      start: event.value.start,
      end: event.value.end,
    };
    updateCommunicationDatesInStorage(communicationDateRange);
    updateSearchModel(communicationDateRange);
  };

  useEffect(() => {
    const searchFilters = getFiltersFromStorage();
    if (searchFilters?.communicationDateRange) {
      const selectedRange = {
        start: searchFilters.communicationDateRange.start ? new Date(searchFilters.communicationDateRange.start) : null,
        end: searchFilters.communicationDateRange.end ? new Date(searchFilters.communicationDateRange.end) : null,
      };
      setSelectedCommunicationDates(selectedRange);
      updateSearchModel(selectedRange);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (clear) setSelectedCommunicationDates(null);
  }, [clear]);

  return (
    <DateRangeWrapper>
      <DateRangePickerGlobalStyle />
      <DatesFilter
        data-testid={'axon-input-search-filter-communication-date'}
        name="dateOfCommunicationSearchFilter"
        label={t('ManageCommunication.DateOfCommunicationLabel')}
        format={DateFormats.ddMMMyyyy}
        onChange={onDateOfCommunicationChange}
        value={selectedCommunicationDates as SelectionRange}
      ></DatesFilter>
    </DateRangeWrapper>
  );
};

export default CommunicationDatesFilterComponent;
