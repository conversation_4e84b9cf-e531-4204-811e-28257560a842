import styled from 'styled-components';

export default {
  CommunicationContent: styled.div`
    display: flex;
  `,
  CommentsWrapper: styled.div`
    display: flex;
    flex-direction: column;
    flex: 2;
    padding: 1rem;
    margin-bottom: 1rem;
    margin-top: 1rem;
  `,
  Wrapper: styled.div`
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    padding: 1rem;
  `,
  HeadingWrapper: styled.div`
    display: flex;
    justify-content: space-between;
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  `,
  Heading: styled.h2`
    font-size: ${(props) => props.theme.headingM};
    font-weight: ${(props) => props.theme.bold};
    margin-top: 0;
    margin-bottom: 1rem;
  `,
  ProductInfo: styled.div`
    display: inline-flex;
    align-items: flex-end;
    gap: 0.375rem;
    height: 1.25rem;
  `,
  BoldProduct: styled.span`
    font-weight: ${(props) => props.theme.bold};
  `,
};
