import { PhlexTable } from 'phlex-core-ui';
import styled from 'styled-components';

const SearchTable = styled(PhlexTable)`
  margin-top: 1.5rem;
  background-color: ${(props) => props.theme.colors.backgroundSec};
  color: ${(props) => props.theme.colors.textPri};
  --kendo-scrollbar-width: 0;
  .k-table-tbody {
    color: ${(props) => props.theme.colors.textPri};
  }
  .k-grid-norecords {
    background-color: ${(props) => props.theme.colors.backgroundSec};
    color: ${(props) => props.theme.colors.textPri};
    max-width: 2000px;
  }
  .k-table-thead {
    background-color: ${(props) => props.theme.colors.backgroundSec};
    color: ${(props) => props.theme.colors.textPri};
  }
  .k-grid-content tr.k-master-row. {
    cursor: pointer;
  }
  .k-grid-content tr.k-master-row td.wrapText {
    text-wrap: wrap;
  }
  .k-grid-content .k-detail-cell tr.k-master-row {
    cursor: default;
  }
  &.k-grid td {
    white-space: normal;
    vertical-align: top;
  }
  .k-master-row.k-table-alt-row .k-grid-content-sticky,
  .k-master-row.k-table-alt-row .k-grid-row-sticky {
    background-color: ${(props) => props.theme.colors.backgroundPri};
  }
  &.k-grid td:first-child,
  .k-grid-header th:first-child {
    background-color: ${(props) => props.theme.colors.backgroundPri};
    box-shadow: 5px 1px 5px ${(props) => props.theme.colors.backgroundSec};
  }
`;

export { SearchTable };
