import React, { FC, Fragment, useCallback, useContext, useEffect, useMemo, useState } from 'react';
import components from './CommentItem.styles';
import { AxonMultiSelect } from 'components/AxonMultiSelect';
import { PhlexIcon, PhlexMultiSelect } from 'phlex-core-ui';
import { AxonInput } from 'components/AxonInput';
import CommunicationCommentProductExtensionsComponent from './CommunicationCommentProductExtensions';
import { fetchTags, mapCommentToCommentRequestModel } from '../communication-service';
import CommentText from './CommentText';
import { CommentItemProps, IProductExtension, ManageCommunicationFormProps } from '../communication-form-types';
import { useTranslation } from 'react-i18next';
import { toast } from 'react-toastify';
import { commentApiService } from 'shared/api';
import CommunicationContext, { EditStatus } from 'context/CommunicationContext';
import CommentActions from './CommentActions';
import { IOption } from 'phlex-core-ui/build/src/controls/PhlexLazyMultiSelect/PhlexLazyMultiSelect';
import { MultiselectData } from 'components/shared/types/types';
import { ProductTypeModel } from 'axon-hacomms-api-sdk';
import { checkForDuplicateProductExtensions } from 'pages/Communications/ManageCommunication/validate-form-service';
import { toDateTimeFormat } from 'shared/services/date-time-format';

const CommentItemComponent: FC<CommentItemProps> = (props: CommentItemProps) => {
  const {
    newCommentAdded,
    commentIx,
    methods,
    update,
    commentField,
    showDeleteComment,
    confirmCommentDelete,
    copyComment,
    drugSubstanceOptionList,
    commentFormValues,
    setCommentFormValues,
  } = props;

  const { control } = methods;
  const { t } = useTranslation();
  const { CommentContainer, WideColumn, LinkContainer, CommentWrapper, SectionBorder, InfoSection } = components;
  const { communication, isNewCommunication, selectedProduct, globalEditModeState, setGlobalEditModeState, tenant } =
    useContext(CommunicationContext);
  const [isEditMode, setIsEditMode] = useState<boolean>(
    isNewCommunication ||
      (globalEditModeState.commentEditState.commentIndex === commentIx &&
        globalEditModeState.commentEditState.editStatus === EditStatus.InEdit)
  );
  const [productExtensionTableValues, setProductExtensionTableValues] = useState<IProductExtension[]>([]);

  const productTypes = useMemo(
    () =>
      selectedProduct?.productTypes?.map((item: ProductTypeModel) => ({ value: item.id?.toString() ?? '', label: item.name ?? '' })) ?? [],
    [selectedProduct]
  );
  useEffect(() => {
    if (selectedProduct?.productExtensions && selectedProduct.productExtensions?.length > 0) {
      const data = selectedProduct.productExtensions
        .filter((pe) => pe.isActive)
        .map((pe) => ({
          id: pe.id,
          pcid: pe.pcid,
          dosageFormName: pe.dosageForm?.name,
          routeOfAdministrationList: pe.routesOfAdministration?.map((r) => r.name).toString(),
          routeOfAdministrations: pe.routesOfAdministration?.map((r) => ({ value: r.id?.toString() ?? '', label: r.name ?? '' })),
        }));

      setProductExtensionTableValues(data);
    }
  }, [selectedProduct, selectedProduct?.productExtensions]);

  const setQuestionIncludedValue = (index: number, val: boolean) => {
    methods.setValue(`comments.${index}.isQuestionIncluded`, val);
  };

  const handleCopyComment = () => {
    copyComment(commentIx);
  };

  const onSubmitHandlerErrors = () => {
    toast.error(t('ManageCommunication.CreateUpdateValidationErrorsMessage'), {
      toastId: 'communication-errors',
    });
  };

  const onSubmit = async (values: ManageCommunicationFormProps) => {
    if (!values.comments) return;
    const communicationId = communication?.id ?? 0;
    const comment = values.comments[commentIx];
    const request = mapCommentToCommentRequestModel(comment, communicationId);

    try {
      if (comment?.id && comment.id > 0) {
        const response = await commentApiService.updateComment(tenant, request);
        methods.setValue(`comments.${commentIx}`, {
          ...comment,
          id: response.data.id,
          lastUpdatedBy: response.data.lastUpdatedBy,
          lastUpdatedDate: response.data.lastUpdatedDate,
        });
        toast.success(t('ManageCommunication.UpdateCommentSucceeded'), {
          toastId: 'comment-updated',
        });
      } else {
        const response = await commentApiService.createComment(tenant, request);
        methods.setValue(`comments.${commentIx}`, {
          ...comment,
          id: response.data.id,
          createdBy: response.data.createdBy,
          createdDate: response.data.createdDate,
          lastUpdatedBy: response.data.lastUpdatedBy,
          lastUpdatedDate: response.data.lastUpdatedDate,
        });
        toast.success(t('ManageCommunication.CreateCommentSucceeded'), {
          toastId: 'comment-created',
        });
      }
      methods.reset(methods.getValues());
      resetEditMode(false);
    } catch (error: any) {
      toast.error(t('ManageCommunication.CreateFailedErrorMessage'));
    }
  };

  const handleEditCommentItem = useCallback(() => {
    resetEditMode(true);
    const formValues = methods.getValues(`comments.${commentIx}`);
    setCommentFormValues({
      ...formValues,
      productExtensions: formValues.productExtensions,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [commentIx]);

  useEffect(() => {
    if (newCommentAdded && commentIx == 0) {
      handleEditCommentItem();
    }
  }, [newCommentAdded, commentIx, handleEditCommentItem]);

  const setValidationError = (fieldName: any, message: string) => {
    methods.setError(fieldName, {
      type: 'manual',
      message: message,
    });
  };

  const handleSaveCommentItem = () => {
    const comment = methods.getValues(`comments.${commentIx}`);

    const formErrors = checkForDuplicateProductExtensions(comment, commentIx);
    if (formErrors && formErrors.length > 0) {
      formErrors.forEach((error) => {
        setValidationError(error.fieldName, t(error.message, { field: t(error.label) }));
      });
      return;
    } else {
      methods.clearErrors(`comments.${commentIx}.productExtensions`);
    }

    methods.handleSubmit(onSubmit, onSubmitHandlerErrors)();
  };

  const handleCancelEditCommentItem = () => {
    resetEditMode(false);
    update(commentIx, {
      ...commentFormValues,
      productExtensions: commentFormValues?.productExtensions,
    });
    methods.resetField(`comments.${commentIx}.productExtensions`, { keepDirty: false });
  };

  const resetEditMode = (isEdit: boolean) => {
    setIsEditMode(isEdit);
    setGlobalEditModeState?.({
      ...globalEditModeState,
      commentEditState: { editStatus: isEdit ? EditStatus.InEdit : EditStatus.NotStarted, commentIndex: commentIx },
    });
  };

  const handleDeleteCommentItem = () => {
    confirmCommentDelete(commentField.id ?? 0, commentIx);
  };

  const fetchDrugSubstances = (): Promise<IOption[]> => Promise.resolve(drugSubstanceOptionList);
  const fetchProductTypes = (): Promise<MultiselectData> => Promise.resolve(productTypes);

  return (
    <Fragment>
      <CommentWrapper>
        <CommentText
          commentIndex={commentIx}
          methods={methods}
          field={commentField}
          isGeneralGuidance={false}
          setQuestionIncludedValue={setQuestionIncludedValue}
          isCommentInEditMode={isEditMode}
        />
        <CommentContainer>
          <CommunicationCommentProductExtensionsComponent
            productExtensionTableValues={productExtensionTableValues}
            commentFieldIndex={commentIx}
            isCommentInEditMode={isEditMode}
            isCommunicationCompleted={communication?.isCompleted ?? false}
            methods={methods}
          />
          <WideColumn>
            <AxonMultiSelect
              data-testid={`axon-input-product-with-comments.comments.drug-substance_${commentIx}`}
              name={`comments[${commentIx}].drugSubstances`}
              label={t('ManageCommunication.DrugSubstancesLabel') || ''}
              defaultValues={drugSubstanceOptionList}
              value={drugSubstanceOptionList}
              width="fullwidth"
              required={true}
              fetchData={() => fetchDrugSubstances()}
              onlyFetchOnOpen={true}
              validationMessage={methods.formState.errors.comments?.[commentIx]?.drugSubstances?.message ?? undefined}
              disabled={drugSubstanceOptionList.length === 1 || communication?.isCompleted || !isEditMode}
            />
            <PhlexMultiSelect
              data-testid={`axon-input-product-with-comments.comments.product-type_${commentIx}`}
              name={`comments[${commentIx}].productTypes`}
              label={t('Common.ProductTypesLabel') || ''}
              defaultValues={productTypes}
              value={productTypes}
              width="fullwidth"
              fetchData={() => fetchProductTypes()}
              onlyFetchOnOpen={true}
              disabled={true}
            />
            <AxonMultiSelect
              data-testid={`axon-input-product-with-comments.comments.tag_${commentIx}`}
              name={`comments[${commentIx}].tags`}
              label={t('Common.TagsLabel') || ''}
              required={true}
              width="fullwidth"
              fetchData={() => fetchTags(tenant)}
              onlyFetchOnOpen={true}
              validationMessage={methods.formState.errors.comments?.[commentIx]?.tags?.message ?? undefined}
              disabled={communication?.isCompleted || !isEditMode}
            />
            <LinkContainer>
              <AxonInput
                urlValidation={true}
                data-testid={`axon-input-product-with-comments.comments.[index]?.birdsLinkToBISAMP_${commentIx}`}
                width="fullwidth"
                label={t('ManageCommunication.BirdsLinkToBISAMPLabel')}
                name={`comments[${commentIx}].birdsLinkToBISAMP`}
                value={commentField.birdsLinkToBISAMP ?? undefined}
                validationMessage={methods.formState.errors.comments?.[commentIx]?.birdsLinkToBISAMP?.message ?? undefined}
                disabled={communication?.isCompleted || !isEditMode}
                placeholder={t('ManageCommunication.UrlFormatPlaceholder')}
              />
              {commentField.birdsLinkToBISAMP && (
                <a href={commentField.birdsLinkToBISAMP} target="_blank" rel="noreferrer">
                  <PhlexIcon name="open_in_new" tooltip={t('ManageCommunication.OpenUrlTooltip')} />
                </a>
              )}
            </LinkContainer>
            <LinkContainer>
              <AxonInput
                urlValidation={true}
                data-testid={`axon-input-product-with-comments.comments.[index]?.birdsLinkToBIResponse_${commentIx}`}
                width="fullwidth"
                label={t('ManageCommunication.BirdsLinkToBIResponseLabel')}
                name={`comments[${commentIx}].birdsLinkToBIResponse`}
                value={commentField.birdsLinkToBIResponse ?? undefined}
                validationMessage={methods.formState.errors.comments?.[commentIx]?.birdsLinkToBIResponse?.message ?? undefined}
                disabled={communication?.isCompleted || !isEditMode}
                placeholder={t('ManageCommunication.UrlFormatPlaceholder')}
              />
              {commentField.birdsLinkToBIResponse && (
                <a href={commentField.birdsLinkToBIResponse} target="_blank" rel="noreferrer">
                  <PhlexIcon name="open_in_new" tooltip={t('ManageCommunication.OpenUrlTooltip')} />
                </a>
              )}
            </LinkContainer>
          </WideColumn>
        </CommentContainer>
        <SectionBorder />
        {commentField?.createdDate && (
          <InfoSection>
            {t('ManageCommunication.CommentCreatedOnField', {
              createdOn: toDateTimeFormat(commentField?.createdDate, 'DD MMM yyyy [at] HH:mma'),
              createdBy: commentField?.createdBy,
            })}
            {commentField?.lastUpdatedDate &&
              t('ManageCommunication.CommentLastUpdatedOnField', {
                lastUpdatedOn: toDateTimeFormat(commentField?.lastUpdatedDate, 'DD MMM yyyy [at] HH:mma'),
                lastUpdatedBy: commentField?.lastUpdatedBy,
              })}
          </InfoSection>
        )}
      </CommentWrapper>
      {!isNewCommunication && (
        <CommentActions
          isEditMode={isEditMode}
          commentIx={commentIx}
          showDeleteCommentIcon={showDeleteComment}
          showCopyCommentIcon={commentField && !!commentField.id}
          control={control}
          handleCopyComment={handleCopyComment}
          handleSaveCommentItem={handleSaveCommentItem}
          handleCancelEditCommentItem={handleCancelEditCommentItem}
          handleEditCommentItem={handleEditCommentItem}
          handleDeleteCommentItem={handleDeleteCommentItem}
        />
      )}
    </Fragment>
  );
};

export default CommentItemComponent;
