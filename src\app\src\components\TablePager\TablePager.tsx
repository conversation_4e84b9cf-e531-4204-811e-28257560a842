import { PagerProps } from '@progress/kendo-react-data-tools';
import React, { useMemo, FC } from 'react';
import { PhlexIcon } from 'phlex-core-ui';
import components from './TablePager.styles';
import numberService from 'shared/services/number-service';
import { useTranslation } from 'react-i18next';

interface AdminTablePagerProps extends PagerProps {
  nameOfRows: string;
}
const TablePager: FC<AdminTablePagerProps> = (props: AdminTablePagerProps) => {
  const { t } = useTranslation();
  const { skip, take, total, onPageChange, nameOfRows } = props;
  const currentPage = Math.floor(skip / take) + 1;
  const lastPage = Math.ceil(total / take);
  const totalPages = Math.ceil((total || 0) / take);

  const handleChange = (event: any, skipValue: number) => {
    if (onPageChange) {
      onPageChange({
        target: event.target,
        skip: skipValue,
        take,
        syntheticEvent: event,
        nativeEvent: event.nativeEvent,
        targetEvent: event.targetEvent,
      });
    }
  };

  const resultsLabel = useMemo(() => {
    const all = numberService.useThousandsSeparators(skip + take > total ? total : skip + take);
    const showing = numberService.useThousandsSeparators(skip + 1);
    const of = numberService.useThousandsSeparators(total);
    if (total > 0) {
      return `Showing ${showing} to ${all} of ${of} ${nameOfRows}`;
    }
    return '';
  }, [skip, take, total, nameOfRows]);

  const handlePageChange = (event: any) => {
    let pageValue = parseInt(event.target.value);
    if (pageValue === currentPage || isNaN(pageValue) || pageValue > lastPage) {
      return;
    }
    if (pageValue < 1) {
      if (currentPage === 1) {
        return;
      }
      pageValue = 1;
    }

    handleChange(event, (pageValue - 1) * take);
  };

  const onInputKeyDown = (event: any) => {
    if (event.key === 'Enter') {
      handlePageChange(event);
    }
  };

  const { ActionsBox, Row } = components;

  return (
    <Row>
      <div>{resultsLabel}</div>
      {totalPages > 1 && (
        <ActionsBox>
          <button disabled={currentPage === 1} onClick={(e) => handleChange(e, 0)}>
            <PhlexIcon name="first_page" size="large" tooltip={t('Common.FirstPage')} />
          </button>
          <button disabled={currentPage === 1} onClick={(e) => handleChange(e, (currentPage - 2) * take)}>
            <PhlexIcon name="navigate_before" size="large" tooltip={t('Common.PreviousPage')} />
          </button>
          <span>
            Page
            <input type="number" defaultValue={currentPage} onBlur={handlePageChange} onKeyDown={onInputKeyDown} />
            of {totalPages}
          </span>
          <button disabled={currentPage === lastPage} onClick={(e) => handleChange(e, currentPage * take)}>
            <PhlexIcon name="navigate_next" size="large" tooltip={t('Common.NextPage')} />
          </button>
          <button disabled={currentPage === lastPage} onClick={(e) => handleChange(e, (totalPages - 1) * take)}>
            <PhlexIcon name="last_page" size="large" tooltip={t('Common.LastPage')} />
          </button>
        </ActionsBox>
      )}
    </Row>
  );
};

export default TablePager;
