import { PhlexIcon, PhlexLoader } from 'phlex-core-ui';
import styled from 'styled-components';

export default {
  ActionColumn: styled.div`
    display: flex;
    align-items: center;
    flex-direction: column;
    gap: 1rem;
    padding-left: 0.75rem;
    padding-top: 0.5rem;
    min-width: 30px;
  `,
  ActionButton: styled.div``,
  ActionIcon: styled(PhlexIcon)``,
  ActionDeleteIcon: styled(PhlexIcon)`
    color: ${(props) => props.theme.colors.textInvalid};
  `,
  ActionLoader: styled(PhlexLoader)`
    border-top-color: #000;
    width: 1.25rem;
    height: 1.25rem;
  `,
};
