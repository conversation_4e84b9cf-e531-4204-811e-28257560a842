{"parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 2018, "sourceType": "module"}, "plugins": ["prettier", "@typescript-eslint", "react-hooks"], "extends": ["prettier", "plugin:react/recommended", "plugin:@typescript-eslint/recommended"], "rules": {"prettier/prettier": "error", "react-hooks/rules-of-hooks": "error", "react-hooks/exhaustive-deps": "warn", "react/prop-types": "off", "@typescript-eslint/no-explicit-any": ["off"]}, "settings": {"react": {"pragma": "React", "version": "detect"}}}