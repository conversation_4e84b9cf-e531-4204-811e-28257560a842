import { CompositeFilterDescriptor, SortDescriptor } from '@progress/kendo-data-query';
import { GridCellProps, GridRowClickEvent, GridSortChangeEvent } from '@progress/kendo-react-grid';
import { DrugSubstanceModel, DrugSubstancePagedListModel } from 'axon-hacomms-api-sdk';
import { MainContent, ClearFilters, TableIcon, SearchInput } from 'components/Content/Content.styles';
import documentsApi from 'components/shared/api/documentsApi';
import useDataGridPagination, { Pagination } from 'components/shared/hooks/useDataGridPagination';
import SubstanceModal from 'components/Modals/SubstanceModal';
import HaCommsTable from 'components/Tables/HaCommsTable';
import { TableColumn } from 'phlex-core-ui/build/src/controls/PhlexTable/PhlexTable.types';
import React, { Fragment, FunctionComponent, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { toFilterDateFormat, toDateTimeFormat } from 'shared/services/date-time-format';
import { PhlexBreadcrumb, PhlexButton, PhlexDatePicker, PhlexLayout, PhlexLoader } from 'phlex-core-ui';
import { DateFormats, tableFilters } from 'shared/consts';
import { withRestrictedPermissions } from 'hoc';
import { Permissions } from 'constants/permissions';
import sharedComponents from 'pages/index.styles';

const initialSort: Array<SortDescriptor> = [{ field: tableFilters.CODE, dir: 'asc' }];

const { HaCommsPageTopBar } = sharedComponents;

const initialFilter: CompositeFilterDescriptor = {
  logic: 'and',
  filters: [
    {
      field: tableFilters.NAME,
      operator: 'contains',
      value: '',
    },
    {
      field: tableFilters.CODE,
      operator: 'contains',
      value: '',
    },
    {
      field: tableFilters.CREATED_DATE,
      operator: 'contains',
      value: '',
    },
    {
      field: tableFilters.LAST_UPDATED_DATE,
      operator: 'contains',
      value: '',
    },
    {
      field: tableFilters.LAST_UPDATED_BY,
      operator: 'contains',
      value: '',
    },
  ],
};

const { PageActions, FilterPanel } = PhlexLayout;

const Component: FunctionComponent<{ basepath: string }> = ({ basepath }) => {
  const initState = {
    skip: 0,
    data: [] as DrugSubstancePagedListModel[],
    total: 0,
    take: 20,
    filter: '',
    orderBy: 'code=>asc',
    refresh: false,
    cleanBasePath: basepath,
  } as Pagination<DrugSubstancePagedListModel>;

  const [pagination, onPageChange, changeFilter, sortChange] = useDataGridPagination<DrugSubstancePagedListModel>(
    documentsApi.drubSubstances.getList,
    initState
  );
  const { t } = useTranslation();

  const breadcrumbs = [
    {
      key: 'manage',
      text: t('Nav.Manage'),
      active: 'false' as const,
    },
    {
      key: 'substances',
      text: t('SubstanceTable.Title'),
      active: 'true' as const,
    },
  ];

  const cellClick = (e: GridCellProps) => {
    setVisible(true);
    setSubstance(pagination.data.find((x: DrugSubstancePagedListModel) => x.id === e.dataItem.id) as DrugSubstanceModel);
  };

  const viewCell = (e: GridCellProps) => {
    return (
      <TableIcon title="View Details" onClick={() => cellClick(e)}>
        chevron_right
      </TableIcon>
    );
  };

  const columns: TableColumn[] = [
    { identifier: tableFilters.CODE, title: t('SubstanceTable.CodeField'), showTooltip: true },
    { identifier: tableFilters.NAME, title: t('SubstanceTable.NameField'), showTooltip: true },
    { identifier: tableFilters.CREATED_DATE, title: t('Common.CreatedDateField'), showTooltip: true },
    { identifier: tableFilters.LAST_UPDATED_DATE, title: t('Common.LastUpdatedDateField'), showTooltip: true },
    { identifier: tableFilters.LAST_UPDATED_BY, title: t('Common.LastUpdatedByField'), showTooltip: true },
    { identifier: '', title: '', cell: viewCell, width: '56px' },
  ];

  const [filters, setFilters] = useState(initialFilter);
  const [updateResults, setUpdateResults] = useState(false);
  const [codeFilter, setCodeFilter] = useState('');
  const [nameFilter, setNameFilter] = useState('');
  const [createdDateFilter, setCreatedDateFilter] = useState<Date | null>(null);
  const [lastModifiedDateFilter, setLastModifiedDateFilter] = useState<Date | null>(null);
  const [lastModifiedByFilter, setLastModifiedByFilter] = useState('');
  const [isInitial, setIsInitial] = useState(true);

  const [nameOfRows] = useState(t('SubstanceTable.PagerLabel'));
  const [sort, setSort] = useState(initialSort);
  const onSortChange = (e: GridSortChangeEvent) => {
    setSort(e.sort);
    sortChange(e.sort);
  };

  const [visible, setVisible] = useState(false);
  const [substance, setSubstance] = useState<DrugSubstanceModel | undefined>();

  const onClose = () => {
    setVisible(false);
    setSubstance(undefined);
  };

  const onRowClick = (e: GridRowClickEvent) => {
    setVisible(true);
    setSubstance(pagination.data.find((x: DrugSubstancePagedListModel) => x.id === e.dataItem.id) as DrugSubstanceModel);
  };

  const clearFilters = () => {
    setCodeFilter('');
    setNameFilter('');
    setCreatedDateFilter(null);
    setLastModifiedDateFilter(null);
    setLastModifiedByFilter('');
    setUpdateResults(true);
    setFilters(initialFilter);
  };

  useEffect(() => {
    if (updateResults) {
      const timer = setTimeout(() => {
        changeFilter(filters);
        setUpdateResults(false);
      }, 500);
      return () => clearTimeout(timer);
    }
  }, [changeFilter, filters, updateResults]);

  useEffect(() => {
    if (
      nameFilter.length === 0 &&
      codeFilter.length === 0 &&
      createdDateFilter === null &&
      lastModifiedDateFilter === null &&
      lastModifiedByFilter.length === 0 &&
      isInitial
    ) {
      setIsInitial(false);
      return;
    }

    const filterValues: CompositeFilterDescriptor = {
      logic: 'and',
      filters: [
        {
          field: tableFilters.NAME,
          operator: 'contains',
          value: nameFilter,
        },
        {
          field: tableFilters.CODE,
          operator: 'contains',
          value: codeFilter,
        },
        {
          field: tableFilters.CREATED_DATE,
          operator: 'contains',
          value: toFilterDateFormat(createdDateFilter),
        },
        {
          field: tableFilters.LAST_UPDATED_DATE,
          operator: 'contains',
          value: toFilterDateFormat(lastModifiedDateFilter),
        },
        {
          field: tableFilters.LAST_UPDATED_BY,
          operator: 'contains',
          value: lastModifiedByFilter,
        },
      ],
    };

    setUpdateResults(true);
    setFilters(filterValues);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [codeFilter, nameFilter, createdDateFilter, lastModifiedDateFilter, lastModifiedByFilter]);

  return (
    <Fragment>
      <HaCommsPageTopBar>
        <PhlexBreadcrumb options={breadcrumbs} />
        <PageActions>
          <PhlexButton
            data-testid="axon-button-substance.add-button"
            label={t('SubstanceTable.AddSubstanceButton')}
            onClick={() => setVisible(true)}
          />
        </PageActions>
      </HaCommsPageTopBar>
      <MainContent>
        <FilterPanel>
          <SearchInput name="code" onChange={(e) => setCodeFilter(e.value)} value={codeFilter} label={t('SubstanceTable.CodeField')} />
          <SearchInput name="name" onChange={(e) => setNameFilter(e.value)} value={nameFilter} label={t('SubstanceTable.NameField')} />
          <PhlexDatePicker
            name="createdDate"
            onChange={(e) => setCreatedDateFilter(e.value)}
            value={createdDateFilter}
            placeholder={t('Common.DatePlaceholder')}
            format={DateFormats.ddMMMyyyy}
            label={t('Common.CreatedDateField')}
          />
          <PhlexDatePicker
            name="lastModifiedDate"
            onChange={(e) => setLastModifiedDateFilter(e.value)}
            value={lastModifiedDateFilter}
            placeholder={t('Common.DatePlaceholder')}
            format={DateFormats.ddMMMyyyy}
            label={t('Common.LastUpdatedDateField')}
          />
          <SearchInput
            name="lastModifiedBy"
            onChange={(e) => setLastModifiedByFilter(e.value)}
            value={lastModifiedByFilter}
            label={t('Common.LastUpdatedByField')}
          />
          <ClearFilters name="filter_alt_off" clickHandler={clearFilters} tooltip={t('Common.ClearAllFitlers')} />
        </FilterPanel>
        {pagination.isLoading ? (
          <PhlexLoader position="page" />
        ) : (
          <HaCommsTable
            {...pagination}
            data={pagination.data.map((x) => ({
              ...x,
              name: x.name ? x.name : `${t('Common.NotAssigned')}`,
              lastUpdatedDate: toDateTimeFormat(x.lastUpdatedDate),
              createdDate: toDateTimeFormat(x.createdDate),
            }))}
            uniqueIdField={tableFilters.CODE}
            filterable={false}
            sortable={true}
            pageable={true}
            onPageChange={onPageChange}
            onSortChange={onSortChange}
            isLoading={pagination.isLoading}
            total={pagination.total}
            columns={columns}
            nameOfRows={nameOfRows}
            sort={sort}
            filter={initialFilter}
            onRowClick={(e: GridRowClickEvent) => onRowClick(e)}
          />
        )}
        {visible && (
          <SubstanceModal onClose={onClose} substance={substance} refreshTable={() => (pagination.refresh = !pagination.refresh)} />
        )}
      </MainContent>
    </Fragment>
  );
};

export default withRestrictedPermissions(Component, [
  Permissions.ViewSubstance,
  Permissions.EditSubstance,
  Permissions.CreateSubstance,
  Permissions.DeleteSubstance,
]);
