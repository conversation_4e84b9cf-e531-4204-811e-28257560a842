import React, { <PERSON> } from 'react';
import { IPhlexSwitchProps } from 'phlex-core-ui/build/src/controls/PhlexSwitch/PhlexSwitch';
import { PhlexSwitch } from 'phlex-core-ui';
import { SwitchChangeEvent } from '@progress/kendo-react-inputs';
import { useController, useFormContext } from 'react-hook-form';

interface IRHFPhlexSwitch extends IPhlexSwitchProps {
  name: string;
}

const Component: FC<IRHFPhlexSwitch> = ({ name, onChange, ...rest }) => {
  const { control } = useFormContext();

  const { field } = useController({
    name,
    control,
  });

  const onChangeHandler = (e: SwitchChangeEvent) => {
    field.onChange(e);
    onChange?.(e);
  };

  return <PhlexSwitch {...rest} checked={field.value} onChange={onChangeHandler} name={name} />;
};

export default Component;
