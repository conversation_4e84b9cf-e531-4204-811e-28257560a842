import { MultiselectData } from 'components/shared/types/types';
import { PhlexMultiSelect } from 'phlex-core-ui';
import React, { FC, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { applicationApiService } from 'shared/api';
import { APPLICATION_NUMBER_KEY, SearchFiltersProps } from './search-filter-types';
import { SearchCommentCommandRequest } from 'axon-hacomms-api-sdk';
import { getFiltersFromStorage, updateFiltersInStorage } from './search-filter-service';

const ApplicationNumbersFilterComponent: FC<SearchFiltersProps> = (props: SearchFiltersProps) => {
  const { tenant, setSearchModel, clear } = props;
  const { t } = useTranslation();
  const [selectedApplicationNumbers, setSelectedApplicationNumbers] = useState<MultiselectData>([]);

  const fetchApplicationNumbers = async (): Promise<MultiselectData> => {
    const response = await applicationApiService.getAllApplicationNumbers(tenant);
    const data = response.data.data?.map((number: string) => ({ value: number ?? '', label: number ?? '' })) ?? [];
    return data;
  };

  const updateSearchModel = (data: MultiselectData) => {
    setSearchModel((request: SearchCommentCommandRequest) => ({
      ...request,
      applicationNumbers: data.map((x) => x.label),
      skip: 0,
      take: 10,
    }));
  };

  const onApplicationNumberFilterChange = (data: MultiselectData) => {
    setSelectedApplicationNumbers(data);
    updateFiltersInStorage(data, APPLICATION_NUMBER_KEY);
    updateSearchModel(data);
  };

  useEffect(() => {
    const searchFilters = getFiltersFromStorage();
    if (searchFilters?.filters[APPLICATION_NUMBER_KEY] && searchFilters.filters[APPLICATION_NUMBER_KEY].length > 0) {
      const filterData = searchFilters.filters[APPLICATION_NUMBER_KEY];
      setSelectedApplicationNumbers(filterData);
      updateSearchModel(filterData);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (clear) setSelectedApplicationNumbers([]);
  }, [clear]);

  return (
    <PhlexMultiSelect
      data-testid={'axon-input-search-filter-application-number'}
      name={`applicationNumberSearchFilter`}
      label={t('SearchFilters.ApplicationNumber')}
      value={selectedApplicationNumbers}
      defaultValues={selectedApplicationNumbers}
      width="fullwidth"
      compactMode={true}
      fetchData={() => fetchApplicationNumbers()}
      onlyFetchOnOpen={true}
      onChange={onApplicationNumberFilterChange}
    />
  );
};

export default ApplicationNumbersFilterComponent;
