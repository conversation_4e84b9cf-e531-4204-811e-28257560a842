import { yupResolver } from '@hookform/resolvers/yup';
import { AxiosResponse } from 'axios';
import { CreateRouteOfAdministrationCommandResponse, RouteOfAdministrationPagedListModel } from 'axon-hacomms-api-sdk';
import { AxonInput } from 'components/AxonInput';
import { FIELD_LENGTH_LIMITS, REGULAR_EXPRESSIONS } from 'constants/regexp';
import { useOrganisationFromUrl } from 'hooks/shared';
import { PhlexButton, PhlexConfirmationDialog } from 'phlex-core-ui';
import React, { FC, useState } from 'react';
import { FieldValues, FormProvider, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { toast } from 'react-toastify';
import { routeOfAdministrationApiService } from 'shared/api';
import * as yup from 'yup';
import components from './styles';

interface RouteOfAdministrationModalProps extends FieldValues {
  routeOfAdministration?: RouteOfAdministrationPagedListModel;
  onClose: () => void;
  refreshTable: () => void;
}

interface RouteOfAdministrationModalFormProps {
  id?: number;
  name: string;
}

const RouteOfAdministrationModal: FC<RouteOfAdministrationModalProps> = ({ routeOfAdministration, onClose, refreshTable }) => {
  const { Modal, ModalHeader, ModalContent, ModalButtons, DeleteButton } = components;
  const { t } = useTranslation();
  const width = 600;
  const tenant = useOrganisationFromUrl();
  const isEdit = !!routeOfAdministration?.id;
  const title = isEdit ? t('RoutesOfAdministrationTable.UpdateModalHeading') : t('RoutesOfAdministrationTable.AddModalHeading');
  const [confirmDelete, setConfirmDelete] = useState<boolean>(false);

  const createNewRouteOfAdministrationSchema = yup.object().shape({
    id: yup.number(),
    name: yup
      .string()
      .trim()
      .required(t('Forms.RequiredField', { field: t('ManageRouteOfAdministration.NameLabel') }))
      .matches(new RegExp(REGULAR_EXPRESSIONS.IllegalCharacters), t('Forms.SafeCharacters'))
      .max(
        FIELD_LENGTH_LIMITS.ROUTE_OF_ADMINISTRATION_NAME,
        t('Forms.InsertMaxCharacters', { number: FIELD_LENGTH_LIMITS.ROUTE_OF_ADMINISTRATION_NAME.toString() })
      ),
  });

  const methods = useForm<RouteOfAdministrationModalFormProps>({
    resolver: yupResolver(createNewRouteOfAdministrationSchema),
  });

  const setValidationError = (fieldName: 'name', message: string) => {
    methods.setError(fieldName, {
      type: 'manual',
      message: message,
    });
  };

  const onSaveSuccess = (r: AxiosResponse<CreateRouteOfAdministrationCommandResponse>) => {
    if (r.status === 200) {
      onClose();
      refreshTable();
      if (isEdit) {
        toast.success(t('ManageRouteOfAdministration.UpdateSucceeded'), {
          toastId: 'routeOfAdministration-updated',
        });
      } else {
        toast.success(t('ManageRouteOfAdministration.CreateSucceeded'), {
          toastId: 'routeOfAdministration-created',
        });
      }
    }
  };

  const onSubmit = async (values: RouteOfAdministrationModalFormProps) => {
    const requestData = {
      id: routeOfAdministration?.id ?? 0,
      name: values.name,
    };

    try {
      const response = isEdit
        ? await routeOfAdministrationApiService.updateRouteOfAdministration(tenant, requestData)
        : await routeOfAdministrationApiService.createRouteOfAdministration(tenant, requestData);
      onSaveSuccess(response);
    } catch (error: any) {
      error.response.data.errors?.Name.some((x: any) => x.includes('exists'))
        ? setValidationError('name', t('Forms.FieldAlreadyInUse', { field: t('ManageRouteOfAdministration.NameLabel') }))
        : toast.error(t('ManageRouteOfAdministration.CreateFailedErrorMessage'));
    }
  };

  const onDelete: React.MouseEventHandler<HTMLButtonElement> = (e) => {
    e.preventDefault();
    setConfirmDelete(true);
  };

  const remove = async () => {
    const id = routeOfAdministration?.id ?? 0;
    try {
      await routeOfAdministrationApiService.deleteRouteOfAdministration(id, tenant);
      onClose();
      refreshTable();
      toast.success(t('ManageRouteOfAdministration.DeleteSucceeded'));
      setConfirmDelete(false);
    } catch (error: any) {
      toast.error(t('ManageRouteOfAdministration.DeleteFailedErrorMessage'));
    }
  };

  return (
    <Modal onClose={onClose} width={width}>
      <ModalHeader>{title}</ModalHeader>
      <FormProvider {...methods}>
        <form onSubmit={methods.handleSubmit(onSubmit)}>
          <ModalContent>
            <AxonInput
              data-testid="axon-input-routeOfAdministration.name"
              value={routeOfAdministration?.name ?? ''}
              name="name"
              width="fullwidth"
              required={true}
              label={t('ManageRouteOfAdministration.NameLabel')}
              validationMessage={methods.formState.errors['name']?.message?.toString() ?? undefined}
            />
          </ModalContent>
          <ModalButtons>
            {isEdit && !routeOfAdministration?.isAssociatedToProduct && (
              <DeleteButton color="tertiary" type="button" label={t('Forms.DeleteButton')} onClick={onDelete} />
            )}
            <PhlexButton color="secondary" type="button" label={t('Forms.CancelButton')} onClick={onClose} />
            <PhlexButton
              data-testid="axon-button-routeOfAdministration.save-button"
              label={isEdit ? t('Forms.UpdateButton') : t('Forms.AddButton')}
              type="submit"
              disabled={methods.formState.isSubmitting}
            />
          </ModalButtons>
          <PhlexConfirmationDialog
            isOpen={confirmDelete}
            heading={t('ManageRouteOfAdministration.DeleteHeading')}
            description={t('ManageRouteOfAdministration.DeleteDescription', { routeOfAdministrationName: routeOfAdministration?.name })}
            onConfirm={() => remove()}
            onCancel={() => setConfirmDelete(false)}
            cancelButtonText={t('Forms.CancelButton')}
            confirmButtonText={t('Forms.DeleteButton')}
          />
        </form>
      </FormProvider>
    </Modal>
  );
};

export default RouteOfAdministrationModal;
