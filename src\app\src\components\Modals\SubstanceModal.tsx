import { yupResolver } from '@hookform/resolvers/yup';
import { AxiosResponse } from 'axios';
import { CreateDrugSubstanceCommandResponse, DrugSubstanceModel } from 'axon-hacomms-api-sdk';
import { AxonInput } from 'components/AxonInput';
import { AxonTextArea } from 'components/AxonTextArea';
import { FIELD_LENGTH_LIMITS, REGULAR_EXPRESSIONS } from 'constants/regexp';
import { useOrganisationFromUrl } from 'hooks/shared';
import { PhlexButton, PhlexConfirmationDialog } from 'phlex-core-ui';
import React, { FC, useState } from 'react';
import { FieldValues, FormProvider, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { toast } from 'react-toastify';
import { drugSubstanceApiService } from 'shared/api';
import * as yup from 'yup';
import components from './styles';

interface SubstanceModalProps extends FieldValues {
  substance?: DrugSubstanceModel;
  onClose: () => void;
  refreshTable: () => void;
}

interface SubstanceModalFormProps {
  id?: number;
  code: string;
  name?: string;
  description?: string;
}

const SubstanceModal: FC<SubstanceModalProps> = ({ substance, onClose, refreshTable }) => {
  const { Modal, ModalHeader, ModalContent, ModalButtons, DeleteButton } = components;
  const { t } = useTranslation();
  const width = 600;
  const tenant = useOrganisationFromUrl();
  const isEdit = !!substance?.id;
  const title = isEdit ? t('SubstanceTable.UpdateModalHeading') : t('SubstanceTable.AddModalHeading');
  const [confirmDelete, setConfirmDelete] = useState<boolean>(false);

  const createNewSubstanceSchema = yup.object().shape({
    id: yup.number(),
    code: yup
      .string()
      .trim()
      .required(t('Forms.RequiredField', { field: t('ManageSubstance.CodeLabel') }))
      .matches(new RegExp(REGULAR_EXPRESSIONS.IllegalCharacters), t('Forms.SafeCharacters'))
      .max(FIELD_LENGTH_LIMITS.SUBSTANCE_CODE, t('Forms.InsertMaxCharacters', { number: FIELD_LENGTH_LIMITS.SUBSTANCE_CODE.toString() })),
    name: yup
      .string()
      .trim()
      .matches(new RegExp(REGULAR_EXPRESSIONS.IllegalCharacters), t('Forms.SafeCharacters'))
      .max(FIELD_LENGTH_LIMITS.SUBSTANCE_NAME, t('Forms.InsertMaxCharacters', { number: FIELD_LENGTH_LIMITS.SUBSTANCE_NAME.toString() })),
    description: yup
      .string()
      .matches(new RegExp(REGULAR_EXPRESSIONS.IllegalCharacters), t('Forms.SafeCharacters'))
      .max(
        FIELD_LENGTH_LIMITS.SUBSTANCE_DESCRIPTION,
        t('Forms.InsertMaxCharacters', { number: FIELD_LENGTH_LIMITS.SUBSTANCE_DESCRIPTION.toString() })
      ),
  });

  const methods = useForm<SubstanceModalFormProps>({
    resolver: yupResolver(createNewSubstanceSchema),
  });

  const setValidationError = (fieldName: 'code' | 'name', message: string) => {
    methods.setError(fieldName, {
      type: 'manual',
      message: message,
    });
  };

  const onSaveSuccess = (r: AxiosResponse<CreateDrugSubstanceCommandResponse>) => {
    if (r.status === 200) {
      onClose();
      refreshTable();
      if (isEdit) {
        toast.success(t('ManageSubstance.UpdateSucceeded'), {
          toastId: 'substance-updated',
        });
      } else {
        toast.success(t('ManageSubstance.CreateSucceeded'), {
          toastId: 'substance-created',
        });
      }
    }
  };

  const onSaveError = () => {
    toast.error(t('ManageSubstance.CreateFailedErrorMessage'));
  };

  const handleServerErrors = (error: any) => {
    const validationErrors = error.response?.data?.errors;
    let handledErrors = false;

    if (validationErrors?.CodeAlreadyExists) {
      setValidationError('code', t('Forms.FieldAlreadyInUse', { field: t('ManageSubstance.CodeLabel') }));
      handledErrors = true;
    }

    if (validationErrors?.NameAlreadyExists) {
      setValidationError('name', t('Forms.FieldAlreadyInUse', { field: t('ManageSubstance.NameLabel') }));
      handledErrors = true;
    }

    if (!handledErrors) {
      onSaveError();
    }
  };

  const saveSubstance = async (values: SubstanceModalFormProps) => {
    const requestData = {
      id: substance?.id ?? 0,
      name: values.name && values.name?.length > 0 ? values.name : t('Common.NotAssigned'),
      code: values.code ?? '',
      description: values.description,
    };

    try {
      const substanceResponse = isEdit
        ? await drugSubstanceApiService.updateDrugSubstance(tenant, requestData)
        : await drugSubstanceApiService.createDrugSubstance(tenant, requestData);
      onSaveSuccess(substanceResponse);
    } catch (error: any) {
      handleServerErrors(error);
    }
  };

  const onSubmit = async (values: SubstanceModalFormProps) => {
    return await saveSubstance(values);
  };

  const onDelete: React.MouseEventHandler<HTMLButtonElement> = (e) => {
    e.preventDefault();
    setConfirmDelete(true);
  };

  const remove = async () => {
    const id = substance?.id ?? 0;
    try {
      await drugSubstanceApiService.deleteDrugSubstance(id, tenant);
      onClose();
      refreshTable();
      toast.success(t('ManageSubstance.DeleteSucceeded'));
      setConfirmDelete(false);
    } catch (error: any) {
      toast.error(t('ManageSubstance.DeleteFailedErrorMessage'));
    }
  };

  return (
    <Modal onClose={onClose} width={width}>
      <ModalHeader>{title}</ModalHeader>
      <FormProvider {...methods}>
        <form onSubmit={methods.handleSubmit(onSubmit)}>
          <ModalContent>
            <AxonInput
              data-testid="axon-input-substance.code"
              value={substance?.code ?? ''}
              name="code"
              width="fullwidth"
              required={true}
              label={t('ManageSubstance.CodeLabel')}
              validationMessage={methods.formState.errors['code']?.message?.toString() ?? undefined}
            />
            <AxonInput
              data-testid="axon-input-substance.name"
              value={isEdit ? substance?.name ?? t('Common.NotAssigned') : ''}
              placeholder={isEdit ? t('Common.NotAssigned') : ''}
              name="name"
              width="fullwidth"
              label={t('ManageSubstance.NameLabel')}
              validationMessage={methods.formState.errors['name']?.message?.toString() ?? undefined}
            />
            <AxonTextArea
              data-testid="axon-input-substance.description"
              value={substance?.description ?? ''}
              name="description"
              width="fullwidth"
              label={t('ManageSubstance.DescriptionLabel')}
              rows={5}
            />
          </ModalContent>
          <ModalButtons>
            {isEdit && !substance?.isAssociatedToComment && (
              <DeleteButton color="tertiary" type="button" label={t('Forms.DeleteButton')} onClick={onDelete} />
            )}
            <PhlexButton color="secondary" type="button" label={t('Forms.CancelButton')} onClick={onClose} />
            <PhlexButton
              data-testid="axon-button-substance.save-button"
              label={isEdit ? t('Forms.UpdateButton') : t('Forms.AddButton')}
              type="submit"
              disabled={methods.formState.isSubmitting}
            />
          </ModalButtons>
          <PhlexConfirmationDialog
            isOpen={confirmDelete}
            heading={t('ManageSubstance.DeleteHeading')}
            description={t('ManageSubstance.DeleteDescription', { substanceCode: substance?.code })}
            onConfirm={() => remove()}
            onCancel={() => setConfirmDelete(false)}
            cancelButtonText={t('Forms.CancelButton')}
            confirmButtonText={t('Forms.DeleteButton')}
          />
        </form>
      </FormProvider>
    </Modal>
  );
};

export default SubstanceModal;
