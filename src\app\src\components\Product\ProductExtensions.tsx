import React, { forwardRef, useCallback, useEffect, useImperative<PERSON>andle, useRef, useState } from 'react';
import { UseFormReturn, useFieldArray } from 'react-hook-form';
import { AddProductExtensionMethod, ProductFormProps, defaultProductExtension } from './product-form-types';
import { AxonInput } from 'components/AxonInput';
import { AxonSelect } from 'components/AxonSelect';
import { DropDownListFilterChangeEvent } from '@progress/kendo-react-dropdowns';
import { FilterDescriptor, filterBy } from '@progress/kendo-data-query';
import { DosageFormModel, ProductModel, RouteOfAdministrationModel } from 'axon-hacomms-api-sdk';
import { useTranslation } from 'react-i18next';
import { AxonMultiSelect } from 'components/AxonMultiSelect';
import { MultiselectData } from 'components/shared/types/types';
import routeOfAdministrationApiService from 'shared/api/route-of-administration-api-service';
import { IOption } from 'phlex-core-ui/build/src/controls/PhlexMultiSelect/PhlexMultiSelect';
import { PhlexButton, PhlexIcon, PhlexLayout } from 'phlex-core-ui';
import { AxonSwitch } from 'components/AxonSwitch';
import { SwitchChangeEvent } from '@progress/kendo-react-inputs';
import { dosageFormApiService } from 'shared/api';
import areAllExtensionsInactive from './productExtensionUtils';
import components from './ProductExtensions.styles';
import sharedComponents from './ProductDetails.styles';

interface ProductExtensionsProps {
  product?: ProductModel;
  methods: UseFormReturn<ProductFormProps, any>;
  tenant: string;
  setProductInactive: () => void;
}

const ProductExtensionsComponent = forwardRef<AddProductExtensionMethod, ProductExtensionsProps>((props: ProductExtensionsProps, ref) => {
  const { product, methods, tenant, setProductInactive } = props;
  const { Wrapper, Heading, Container, ProductExtensionWrapper, CodeColumn, WideColumn, SwitchColumn, DeleteColumn, CenteredContent } =
    components;
  const { AssociateCommentWarning } = sharedComponents;
  const { InputLabel } = PhlexLayout;
  const { t } = useTranslation();
  const [dosageForms, setDosageForms] = useState<DosageFormModel[]>([]);
  const [dosageFormsDataSources, setDosageFormsDataSources] = useState<[DosageFormModel[]]>([[]]);
  const [dosageFormFilterApplied, setDosageFormFilterApplied] = useState<boolean>(false);
  const [newExtensionAdded, setNewExtensionAdded] = useState(false);
  const productExtensionsRef = useRef<null | HTMLDivElement>(null);
  const productExtensions = product?.productExtensions;

  const { fields, append, remove } = useFieldArray({
    control: methods.control,
    name: 'productExtensions',
  });

  const onDosageFormFilterChange = (ix: number, filter: FilterDescriptor) => {
    const dataSources = dosageFormsDataSources;
    dataSources[ix] = filterBy(dosageForms, filter);
    setDosageFormsDataSources(dataSources);
    setDosageFormFilterApplied(true);
  };

  useEffect(() => {
    if (dosageFormFilterApplied) {
      setDosageFormFilterApplied(false);
    }
  }, [dosageFormFilterApplied]);

  const fetchRoutesOfAdministration = async (): Promise<MultiselectData> => {
    const response = await routeOfAdministrationApiService.getRouteOfAdministrationList(tenant);
    return (
      response.data.data?.map((item: RouteOfAdministrationModel) => ({ value: item.id?.toString() ?? '', label: item.name ?? '' })) ?? []
    );
  };

  const addProductExtension = () => {
    append(defaultProductExtension);
    const data = dosageFormsDataSources ?? [[]];
    data.push([...(dosageForms ?? [])]);
    setDosageFormsDataSources(data);
    setNewExtensionAdded(true);
  };

  const removeProductExtension = (index: number) => {
    remove(index);
    methods.reset({
      name: methods.getValues('name'),
      isActive: methods.getValues('isActive'),
      substances: methods.getValues('substances'),
      productExtensions: methods.getValues('productExtensions'),
    });
  };

  useImperativeHandle(ref, () => ({
    AddProductExtension: addProductExtension,
  }));

  useEffect(() => {
    if (newExtensionAdded) {
      productExtensionsRef.current?.scrollIntoView({ behavior: 'smooth' });
      setNewExtensionAdded(false);
    }
  }, [newExtensionAdded]);

  const getDosageForms = useCallback(async () => {
    const dosageFormsResponse = await dosageFormApiService.getDosageFormList(tenant);
    setDosageForms(dosageFormsResponse.data?.data ?? []);
  }, [tenant]);

  useEffect(() => {
    getDosageForms();
  }, [getDosageForms]);

  useEffect(() => {
    if (dosageForms && dosageForms.length > 0) {
      if (productExtensions && productExtensions.length > 0) {
        const data: [Array<DosageFormModel>] = new Array(productExtensions.length).fill([...dosageForms]) as [Array<DosageFormModel>];
        setDosageFormsDataSources(data);
      } else {
        setDosageFormsDataSources([[...dosageForms]]);
      }
    }
  }, [dosageForms, productExtensions]);

  const getProductExtensionValues = () => methods.getValues().productExtensions;

  const changeExtensionStatus = (index: number, val: boolean) => {
    methods.setValue(`productExtensions.${index}.isActive`, val);
    const productExtensions = getProductExtensionValues();
    if (areAllExtensionsInactive(productExtensions)) {
      setProductInactive();
    }
  };

  const isExtensionDisabled = (index: number) => {
    return (product && !product.isActive) || !methods.watch(`productExtensions.${index}.isActive`);
  };

  return (
    <Wrapper>
      <Heading>{t('ManageProduct.ProductExtensions')}</Heading>
      {fields.map((field, index) => (
        <Container key={field.id} ref={productExtensionsRef}>
          <ProductExtensionWrapper>
            <CodeColumn>
              <AxonInput
                data-testid={`axon-input-product-extensions.pcid_${index}`}
                name={`productExtensions.${index}.pcid`}
                label={t('ProductExtension.ProductCodeLabel')}
                width="fullwidth"
                value={field.pcid}
                validationMessage={methods.formState.errors.productExtensions?.[index]?.pcid?.message ?? undefined}
                disabled={isExtensionDisabled(index)}
              />
              <AssociateCommentWarning hidden={(productExtensions && !productExtensions[index]?.isAssociatedToComment) ?? true}>
                {t('ManageProduct.AssociateExtensionWithCommentWarningMessage')}
              </AssociateCommentWarning>
            </CodeColumn>
            <WideColumn>
              <AxonSelect
                data-testid={`axon-input-product-extensions.dosage-form_${index}`}
                name={`productExtensions.${index}.dosageForm`}
                label={t('ProductExtension.DosageFormLabel')}
                defaultValue={field.dosageForm}
                value={field.dosageForm}
                width="fullwidth"
                required={true}
                data={dosageFormsDataSources?.[index]}
                textField="name"
                dataItemKey="id"
                filterable={true}
                onFilterChange={(e: DropDownListFilterChangeEvent) => onDosageFormFilterChange(index, e.filter)}
                defaultItem={{ id: null, name: t('ProductExtension.SelectDosageForm') }}
                validationMessage={methods.formState.errors.productExtensions?.[index]?.dosageForm?.message ?? undefined}
                disabled={isExtensionDisabled(index) || (productExtensions ? productExtensions[index]?.isAssociatedToComment : false)}
              />
              <AxonMultiSelect
                data-testid={`axon-input-product-extensions.route-admin_${index}`}
                name={`productExtensions.${index}.routesOfAdministration`}
                label={t('ProductExtension.RouteOfAdminLabel')}
                defaultValues={field.routesOfAdministration}
                value={field.routesOfAdministration}
                width="fullwidth"
                required={true}
                fetchData={() => fetchRoutesOfAdministration()}
                onlyFetchOnOpen={true}
                validationMessage={methods.formState.errors.productExtensions?.[index]?.routesOfAdministration?.message ?? undefined}
                onChange={(data: IOption[]) => methods.setValue(`productExtensions.${index}.routesOfAdministration`, data)}
                disabled={isExtensionDisabled(index) || (productExtensions ? productExtensions[index]?.isAssociatedToComment : false)}
              />
            </WideColumn>
            <SwitchColumn>
              <InputLabel>
                {methods.getValues(`productExtensions.${index}.isActive`) ? t('Forms.ActiveLabel') : t('Forms.InActiveLabel')}
              </InputLabel>
              <AxonSwitch
                data-testid={`axon-input-product-extensions.isActive_${index}`}
                name={`productExtensions.${index}.isActive`}
                checked={field.isActive}
                onChange={(e: SwitchChangeEvent) => changeExtensionStatus(index, e.value)}
                disabled={!product || !product?.isActive}
              />
            </SwitchColumn>
          </ProductExtensionWrapper>
          <DeleteColumn>
            {fields.length > 1 &&
              (!product || product.isActive) &&
              (productExtensions ? !productExtensions[index]?.isAssociatedToComment : true) && (
                <PhlexIcon
                  name="delete"
                  size="large"
                  tooltip={t('Common.DeleteText')}
                  clickHandler={() => removeProductExtension(index)}
                  fill={false}
                />
              )}
          </DeleteColumn>
        </Container>
      ))}
      <CenteredContent>
        <PhlexButton
          icon="add"
          label={t('Forms.AddProductExtensionButton')}
          type="button"
          onClick={addProductExtension}
          disabled={product && !product.isActive}
        />
      </CenteredContent>
    </Wrapper>
  );
});
ProductExtensionsComponent.displayName = 'ProductExtensions';
export default ProductExtensionsComponent;
