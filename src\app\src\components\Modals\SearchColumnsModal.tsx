import React, { FC } from 'react';
import { useTranslation } from 'react-i18next';
import components from './styles';
import { PhlexButton, PhlexMultiSelect } from 'phlex-core-ui';
import { MultiselectData } from 'components/shared/types/types';
import { IOption } from 'phlex-core-ui/build/src/controls/PhlexMultiSelect/PhlexMultiSelect';

interface ColumnsModalProps {
  columns: MultiselectData;
  selectedColumns: MultiselectData;
  onSelectedColumns: (selectedColumns: string[]) => void;
  onClose: () => void;
}

const SearchColumnsModal: FC<ColumnsModalProps> = ({ columns, selectedColumns, onSelectedColumns, onClose }) => {
  const { Modal, ModalHeader, ModalContent, ModalButtons } = components;
  const { t } = useTranslation();
  const width = 600;

  const onColumnsChange = (data: MultiselectData) => {
    if (data && data.length === 1 && data[0].value === '-1') {
      selectedColumns = columns;
      return;
    }
    selectedColumns = data;
  };
  return (
    <Modal onClose={onClose} width={width}>
      <ModalHeader>{t('SearchModal.Heading')}</ModalHeader>
      <ModalContent>
        <PhlexMultiSelect
          data-testid={`axon-input-search-columns`}
          name="selectedColumns"
          label={t('Common.Columns')}
          labelPosition="top"
          width="fullwidth"
          defaultValue={selectedColumns as IOption[]}
          defaultValues={selectedColumns}
          includeAlloption={true}
          fetchData={() => Promise.resolve(columns)}
          onChange={onColumnsChange}
        />
      </ModalContent>
      <ModalButtons>
        <PhlexButton color="secondary" label={t('Forms.CancelButton')} onClick={onClose} />
        <PhlexButton
          data-testid="axon-button-communication-product.add-button"
          label={t('Common.ModifyButton')}
          onClick={() => onSelectedColumns(selectedColumns.map((x) => x.value))}
        />
      </ModalButtons>
    </Modal>
  );
};

export default SearchColumnsModal;
