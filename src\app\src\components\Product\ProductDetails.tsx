import React, { FC, useEffect, useState } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { SwitchChangeEvent } from '@progress/kendo-react-inputs';
import { ProductExtensionResponseModel, ProductModel, ProductTypeModel } from 'axon-hacomms-api-sdk';
import { drugSubstanceApiService, productTypeApiService } from 'shared/api';
import { PhlexLayout } from 'phlex-core-ui';
import { AxonInput } from 'components/AxonInput';
import { AxonMultiSelect } from 'components/AxonMultiSelect';
import components from './ProductDetails.styles';
import { MultiselectData } from 'components/shared/types/types';
import { ProductFormProps } from './product-form-types';
import { generateDrugSubstanceOptions } from 'shared/services/drug-substance-srv';
import { AxonSwitch } from 'components/AxonSwitch';

interface ProductDetailsProps {
  product?: ProductModel;
  tenant: string;
  methods: UseFormReturn<ProductFormProps, any>;
  onProductStatusChange: (val: boolean) => void;
}

const ProductDetailsComponent: FC<ProductDetailsProps> = (props: ProductDetailsProps) => {
  const { product, methods, tenant, onProductStatusChange } = props;
  const { Wrapper, Sticky, Heading, Form, AssociateCommentWarning } = components;
  const { InputLabel } = PhlexLayout;
  const { t } = useTranslation();
  const [isProductAssociatedWithComment, setIsProductAssociatedWithComment] = useState<boolean | undefined>(false);

  const fetchDrugSubstances = async (): Promise<MultiselectData> => {
    const response = await drugSubstanceApiService.getDrugSubstancesList(tenant);
    return generateDrugSubstanceOptions(response.data.data ?? [], t('Common.DrugSubstanceCode'), t('Common.DrugSubstanceName'));
  };

  const fetchProductTypes = async (): Promise<MultiselectData> => {
    const response = await productTypeApiService.getProductTypesList(tenant);
    return response.data.data?.map((item: ProductTypeModel) => ({ value: item.id?.toString() ?? '', label: item.name ?? '' })) ?? [];
  };

  const onSubstancesChange = (data: MultiselectData) => {
    methods.setValue('substances', data);
  };

  const onProductTypesChange = (data: MultiselectData) => {
    methods.setValue('productTypes', data);
  };

  const changeProductStatus = (val: boolean) => {
    methods.setValue('isActive', val);
    onProductStatusChange(val);
  };

  useEffect(() => {
    if (product) {
      const isAssociate = product?.productExtensions?.some((x: ProductExtensionResponseModel) => x.isAssociatedToComment);
      setIsProductAssociatedWithComment(isAssociate);
      methods.setValue('isActive', product?.isActive ?? false);
      methods.setValue(
        'substances',
        generateDrugSubstanceOptions(product?.drugSubstances ?? [], t('Common.DrugSubstanceCode'), t('Common.DrugSubstanceName'))
      );
      methods.setValue(
        'productTypes',
        product.productTypes?.map((item: ProductTypeModel) => ({ value: item.id?.toString() ?? '', label: item.name ?? '' })) ?? []
      );
    }
  }, [product, methods, t]);

  const isProductActive = methods.watch('isActive');

  return (
    <Wrapper>
      <Sticky>
        <Heading>{t('ManageProduct.ProductDetails')}</Heading>
        <Form>
          <AxonInput
            data-testid="axon-input-product.name"
            label={t('ManageProduct.ProductNameLabel')}
            width="standard"
            name="name"
            required={true}
            value={product?.name}
            validationMessage={methods.formState.errors['name']?.message?.toString() ?? undefined}
            disabled={!isProductActive}
          />
          <div>
            <InputLabel>{!product || product.isActive ? t('Forms.ActiveLabel') : t('Forms.InActiveLabel')}</InputLabel>
            <AxonSwitch
              data-testid="axon-switch-product.active"
              name="isActive"
              checked={product?.isActive ?? true}
              onChange={(e: SwitchChangeEvent) => changeProductStatus(e.value)}
              disabled={!product}
            />
          </div>
          <AxonMultiSelect
            data-testid="axon-multiselect-product.substances"
            label={t('ManageProduct.DrugSubstancesLabel')}
            name="substances"
            required={true}
            fetchData={() => fetchDrugSubstances()}
            onlyFetchOnOpen={true}
            onChange={onSubstancesChange}
            validationMessage={
              methods.formState.errors['substances']?.message?.toString()
                ? t('Forms.RequiredField', { field: t('ManageProduct.DrugSubstancesField') })
                : undefined
            }
            disabled={!isProductActive || isProductAssociatedWithComment}
          />
          <AxonMultiSelect
            data-testid="axon-multiselect-product.productTypes"
            label={t('Common.ProductTypesLabel')}
            name="productTypes"
            required={true}
            fetchData={() => fetchProductTypes()}
            onlyFetchOnOpen={true}
            onChange={onProductTypesChange}
            validationMessage={methods.formState.errors['productTypes']?.message?.toString() ?? undefined}
            disabled={!isProductActive}
          />
          {isProductAssociatedWithComment && (
            <div>
              <AssociateCommentWarning>{t('ManageProduct.AssociateProductWithCommentWarningMessage')}</AssociateCommentWarning>
            </div>
          )}
        </Form>
      </Sticky>
    </Wrapper>
  );
};

export default ProductDetailsComponent;
