import { CommunicationModel, CountryModel, ProductModel, SubmissionTypeModel } from 'axon-hacomms-api-sdk';
import React from 'react';

export enum EditStatus {
  NotStarted = 'NotStarted',
  InEdit = 'InEdit',
}

type CommentEditState = {
  editStatus: EditStatus;
  commentIndex?: number;
};

export type GlobalEditModeState = {
  overviewEditState: EditStatus;
  commentEditState: CommentEditState;
};

const CommunicationContext = React.createContext<{
  communication?: CommunicationModel;
  selectedProduct?: ProductModel;
  setSelectedProduct?: (product: ProductModel | undefined) => void;
  countries?: CountryModel[];
  submissionTypes?: SubmissionTypeModel[];
  products?: ProductModel[];
  tenant: string;
  globalEditModeState: GlobalEditModeState;
  isNewCommunication?: boolean;
  setGlobalEditModeState?: (value: GlobalEditModeState) => void;
  triggerFormValidation: () => Promise<boolean>;
  generalGuidanceCommentsCount?: number;
  setGeneralGuidanceCommentsCount?: (value: number) => void;
}>({
  tenant: '',
  globalEditModeState: { overviewEditState: EditStatus.NotStarted, commentEditState: { editStatus: EditStatus.NotStarted } },
  triggerFormValidation: () => Promise.resolve(true),
});

export default CommunicationContext;
