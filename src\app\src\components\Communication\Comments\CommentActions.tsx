import React, { FC, Fragment, useContext } from 'react';
import components from './CommentActions.styles';
import CommunicationContext, { EditStatus } from 'context/CommunicationContext';
import { useTranslation } from 'react-i18next';
import { useFormState } from 'react-hook-form';
import { CommentActionsProps } from '../communication-form-types';

const CommentActionsComponent: FC<CommentActionsProps> = (props: CommentActionsProps) => {
  const {
    isEditMode,
    commentIx,
    showDeleteCommentIcon,
    showCopyCommentIcon,
    control,
    handleCopyComment,
    handleSaveCommentItem,
    handleCancelEditCommentItem,
    handleEditCommentItem,
    handleDeleteCommentItem,
  } = props;
  const { isSubmitting } = useFormState({ control });
  const { communication, isNewCommunication, globalEditModeState } = useContext(CommunicationContext);
  const { ActionColumn, ActionButton, ActionIcon, ActionDeleteIcon, ActionLoader } = components;
  const { t } = useTranslation();

  return (
    <ActionColumn>
      {!communication?.isCompleted && globalEditModeState.overviewEditState !== EditStatus.InEdit && (
        <Fragment>
          {(globalEditModeState.commentEditState.editStatus !== EditStatus.InEdit ||
            globalEditModeState.commentEditState.commentIndex === commentIx) && (
            <Fragment>
              {!isNewCommunication && isEditMode && (
                <Fragment>
                  {isSubmitting ? (
                    <ActionButton>
                      <ActionLoader position="center" />
                    </ActionButton>
                  ) : (
                    <>
                      <ActionButton>
                        <ActionIcon
                          name="save"
                          size="large"
                          tooltip={t('ManageCommunication.Save')}
                          fill={false}
                          clickHandler={() => handleSaveCommentItem()}
                        />
                      </ActionButton>
                      <ActionButton>
                        <ActionIcon
                          name="cancel"
                          size="large"
                          tooltip={t('ManageCommunication.Cancel')}
                          fill={false}
                          clickHandler={() => handleCancelEditCommentItem()}
                        />
                      </ActionButton>
                    </>
                  )}
                </Fragment>
              )}
              {!isEditMode && (
                <ActionIcon
                  name="edit"
                  size="large"
                  tooltip={t('ManageCommunication.Edit')}
                  fill={false}
                  clickHandler={() => handleEditCommentItem()}
                />
              )}
            </Fragment>
          )}
          {globalEditModeState.commentEditState.editStatus !== EditStatus.InEdit && (
            <Fragment>
              {showDeleteCommentIcon && (
                <ActionButton>
                  <ActionDeleteIcon
                    name="delete"
                    size="large"
                    tooltip={t('Common.DeleteText')}
                    clickHandler={() => handleDeleteCommentItem()}
                    fill={false}
                  />
                </ActionButton>
              )}
              {showCopyCommentIcon && (
                <ActionButton>
                  <ActionIcon
                    name="content_copy"
                    size="large"
                    tooltip={t('ManageCommunication.CopyComment')}
                    clickHandler={() => handleCopyComment()}
                    fill={false}
                  />
                </ActionButton>
              )}
            </Fragment>
          )}
        </Fragment>
      )}
    </ActionColumn>
  );
};

export default CommentActionsComponent;
