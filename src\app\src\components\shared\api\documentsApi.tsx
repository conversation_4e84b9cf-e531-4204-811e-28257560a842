import Axios, { AxiosPromise } from 'axios';
import {
  ProductModel,
  DrugSubstanceModel,
  RouteOfAdministrationModel,
  DosageFormModel,
  TagModel,
  CommunicationPagedListModel,
} from 'axon-hacomms-api-sdk';
import { PaginationResponse } from 'components/TablePager/pagination-response';
import { configurationParams } from 'shared/api/configuration-params';

const baseUrl = configurationParams.basePath;

const getDrugSubstances = (
  skip: number,
  take: number,
  filters: string,
  order: string,
  tenant: string
): AxiosPromise<PaginationResponse<DrugSubstanceModel>> => {
  const baseUri = `${baseUrl}/${tenant}/v1/DrugSubstances?skip=${skip}&take=${take}&order=${order}&${filters}`;

  return Axios.get<PaginationResponse<DrugSubstanceModel>>(baseUri);
};

const getRoutesOfAdministration = (
  skip: number,
  take: number,
  filters: string,
  order: string,
  tenant: string
): AxiosPromise<PaginationResponse<RouteOfAdministrationModel>> => {
  const baseUri = `${baseUrl}/${tenant}/v1/RoutesOfAdministration?skip=${skip}&take=${take}&order=${order}&${filters}`;

  return Axios.get<PaginationResponse<RouteOfAdministrationModel>>(baseUri);
};

const getDosageForms = (
  skip: number,
  take: number,
  filters: string,
  order: string,
  tenant: string
): AxiosPromise<PaginationResponse<DosageFormModel>> => {
  const baseUri = `${baseUrl}/${tenant}/v1/DosageForms?skip=${skip}&take=${take}&order=${order}&${filters}`;

  return Axios.get<PaginationResponse<DosageFormModel>>(baseUri);
};

const getTags = (
  skip: number,
  take: number,
  filters: string,
  order: string,
  tenant: string
): AxiosPromise<PaginationResponse<TagModel>> => {
  const baseUri = `${baseUrl}/${tenant}/v1/Tags?skip=${skip}&take=${take}&order=${order}&${filters}`;

  return Axios.get<PaginationResponse<TagModel>>(baseUri);
};

const getDrugProducts = (
  skip: number,
  take: number,
  filters: string,
  order: string,
  tenant: string
): AxiosPromise<PaginationResponse<ProductModel>> => {
  const baseUri = `${baseUrl}/${tenant}/v1/Products?skip=${skip}&take=${take}&order=${order}&${filters}`;

  return Axios.get<PaginationResponse<ProductModel>>(baseUri);
};

const getCommunicationsByStatus = (
  skip: number,
  take: number,
  filter: string,
  order: string,
  tenant: string,
  isCompleted?: boolean
): AxiosPromise<PaginationResponse<CommunicationPagedListModel>> => {
  const userFilter = filter ? `&${filter}` : '';
  const baseUri = `${baseUrl}/${tenant}/v1/Communications?skip=${skip}&take=${take}&order=${order}&filters=iscompleted=>${isCompleted}${userFilter}`;

  return Axios.get<PaginationResponse<CommunicationPagedListModel>>(baseUri);
};

const getOpenCommunications = (
  skip: number,
  take: number,
  filter: string,
  order: string,
  tenant: string
): AxiosPromise<PaginationResponse<CommunicationPagedListModel>> => {
  return getCommunicationsByStatus(skip, take, filter, order, tenant, false);
};

const getCompletedCommunications = (
  skip: number,
  take: number,
  filter: string,
  order: string,
  tenant: string
): AxiosPromise<PaginationResponse<CommunicationPagedListModel>> => {
  return getCommunicationsByStatus(skip, take, filter, order, tenant, true);
};

export default {
  drubSubstances: {
    getList: getDrugSubstances,
  },
  routesOfAdministration: {
    getList: getRoutesOfAdministration,
  },
  dosageForms: {
    getList: getDosageForms,
  },
  tags: {
    getList: getTags,
  },
  drugProducts: {
    getList: getDrugProducts,
  },
  communications: {
    getOpenList: getOpenCommunications,
    getCompletedList: getCompletedCommunications,
  },
};
