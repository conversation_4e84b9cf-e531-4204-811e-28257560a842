import moment from 'moment';

export const toDateTimeFormat = (date: string | null | undefined, format = 'DD/MMM/yyyy') => {
  return moment(date, 'MM-DD-YYYY HH:mm:ss').format(format);
};

export const toFilterDateFormat = (date: Date | null) => {
  if (date === null) return '';

  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const formattedMonth = month < 10 ? `0${month}` : month;
  const formattedDay = day < 10 ? `0${day}` : day;
  return `${year}-${formattedMonth}-${formattedDay}`;
};
