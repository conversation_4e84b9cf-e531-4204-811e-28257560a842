import { ProductModel, SearchCommentCommandRequest } from 'axon-hacomms-api-sdk';
import { MultiselectData } from 'components/shared/types/types';
import { PhlexMultiSelect } from 'phlex-core-ui';
import React, { FC, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { productApiService } from 'shared/api';
import { PRODUCT_KEY, SearchFiltersProps } from './search-filter-types';
import { getFiltersFromStorage, updateFiltersInStorage } from './search-filter-service';

const DrugProductsFilterComponent: FC<SearchFiltersProps> = (props: SearchFiltersProps) => {
  const { tenant, setSearchModel, disable, clear } = props;
  const { t } = useTranslation();
  const [selectedProducts, setSelectedProducts] = useState<MultiselectData>([]);

  const fetchDrugProducts = async (): Promise<MultiselectData> => {
    const response = await productApiService.getProductsList(tenant);
    const drugProducts = response.data.data ?? [];
    const result = drugProducts.map((item: ProductModel) => ({ value: item.id?.toString() ?? '', label: item.name ?? '' })) ?? [];
    return result;
  };

  const updateSearchModel = (data: MultiselectData) => {
    setSearchModel((request: SearchCommentCommandRequest) => ({
      ...request,
      products: data.map((x) => ({ id: Number(x.value), name: x.label })),
      skip: 0,
      take: 10,
    }));
  };

  const onProductFilterChange = (data: MultiselectData) => {
    setSelectedProducts(data);
    updateFiltersInStorage(data, PRODUCT_KEY);
    updateSearchModel(data);
  };

  useEffect(() => {
    const searchFilters = getFiltersFromStorage();
    if (searchFilters?.filters[PRODUCT_KEY] && searchFilters.filters[PRODUCT_KEY].length > 0) {
      const filterData = searchFilters.filters[PRODUCT_KEY];
      setSelectedProducts(filterData);
      updateSearchModel(filterData);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (disable) {
      setSelectedProducts([]);
      updateFiltersInStorage([], PRODUCT_KEY);
    }
  }, [disable]);

  useEffect(() => {
    if (clear) setSelectedProducts([]);
  }, [clear]);

  return (
    <PhlexMultiSelect
      data-testid={'axon-input-search-filter-product'}
      name={`drugProductSearchFilter`}
      label={t('SearchFilters.DrugProduct')}
      value={selectedProducts}
      defaultValues={selectedProducts}
      width="fullwidth"
      compactMode={true}
      fetchData={() => fetchDrugProducts()}
      onlyFetchOnOpen={true}
      onChange={onProductFilterChange}
      disabled={disable}
    />
  );
};

export default DrugProductsFilterComponent;
