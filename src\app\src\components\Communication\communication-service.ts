import { ApplicationModel, CommentDtoModel, RouteOfAdministrationModel, SubmissionModel, TagModel } from 'axon-hacomms-api-sdk';
import { MultiselectData } from 'components/shared/types/types';
import { IOption } from 'phlex-core-ui/build/src/controls/PhlexMultiSelect/PhlexMultiSelect';
import tagApiService from 'shared/api/tag-api-service';
import { generateDrugSubstanceOptions } from 'shared/services/drug-substance-srv';
import { ApplicationChipItemProps, Comment, ManageCommunicationFormProps } from './communication-form-types';
import { EditStatus, GlobalEditModeState } from 'context/CommunicationContext';
import { ChipItemProps } from 'phlex-core-ui/build/src/controls/PhlexChipList/PhlexChipList';

export const mapComment = (comment: CommentDtoModel, codeLabel: string, nameLabel: string) => ({
  ...comment,
  productExtensions: comment.productExtensions?.map((cpe) => ({
    id: cpe.id,
    pcid: cpe.pcid,
    dosageFormName: cpe.dosageFormName,
    routeOfAdministrations: cpe.routeOfAdministrations?.map(
      (r: RouteOfAdministrationModel) => ({ value: r.id?.toString() ?? '', label: r.name } as IOption)
    ),
  })),
  drugSubstances: generateDrugSubstanceOptions(comment.drugSubstances ?? [], codeLabel, nameLabel),
  tags: comment.tags?.map((r: TagModel) => ({ value: r.id?.toString() ?? '', label: r.name } as IOption)),
  isQuestionIncluded: comment.isQuestionIncluded,
  birdsLinkToBIResponse: comment.birdsLinkToBIResponse ?? '',
  birdsLinkToBISAMP: comment.birdsLinkToBISAMP ?? '',
});

export const fetchTags = async (tenant: string): Promise<MultiselectData> => {
  const response = await tagApiService.getTagsList(tenant);
  return response.data.data?.map((item: TagModel) => ({ value: item.id?.toString() ?? '', label: item.name ?? '' })) ?? [];
};

export const mapCommentToCommentRequestModel = (comment: Comment, communicationId: number, isGeneralGuidance = false) => ({
  id: comment.id,
  communicationId: communicationId,
  description: comment.description,
  question: comment.question,
  response: comment.response,
  birdsLinkToBIResponse: comment.birdsLinkToBIResponse,
  birdsLinkToBISAMP: comment.birdsLinkToBISAMP,
  productExtensions: comment.productExtensions?.map((cpe) => ({
    productExtensionId: cpe.id,
    routeOfAdministrationIds: cpe.routeOfAdministrations?.map((r) => Number(r.value)),
  })),
  drugSubstanceIds: comment.drugSubstances?.map((r) => +r.value),
  tagIds: comment.tags?.map((r) => +r.value),
  isGeneralGuidance: isGeneralGuidance,
  isQuestionIncluded: comment.isQuestionIncluded,
});

export const mapApplicationNumbersToChipListProps = (applications: ApplicationModel[]) => {
  return applications?.map(
    (appl: ApplicationModel, index: number) =>
      ({
        id: appl.id?.toString(),
        text: appl.number,
        submissions: appl.submissions?.map((sub: SubmissionModel, innerIndex: number) => ({
          text: sub.number,
          id: `${innerIndex.toString()}_${index.toString()}`,
        })),
      } as ApplicationChipItemProps)
  );
};

export const mapApplicationNumbers = (applications: ApplicationChipItemProps[]) => {
  return applications.map((item: ApplicationChipItemProps) => ({
    id: !Number.isInteger(Number(item.id)) ? 0 : Number(item.id),
    number: item.text,
    submissions: item.submissions?.map((sub: ChipItemProps) => ({
      number: sub.text,
    })),
  })) as ApplicationModel[];
};

export const isCommunicationInEditState = (globalEditModeState: GlobalEditModeState) =>
  globalEditModeState.commentEditState.editStatus === EditStatus.InEdit || globalEditModeState.overviewEditState === EditStatus.InEdit;

export const getCommunicationFormState = (formValues: ManageCommunicationFormProps) => {
  return {
    ...formValues,
    applications: formValues.applications ?? [],
    subject: formValues.subject ?? '',
    submissionType: formValues.submissionType,
    country: formValues.country,
    dateOfCommunication: !formValues.dateOfCommunication ? new Date() : new Date(formValues.dateOfCommunication),
  };
};
