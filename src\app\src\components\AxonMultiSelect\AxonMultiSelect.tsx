import React, { FC } from 'react';
import { useFormContext, useController } from 'react-hook-form';
import { IOption, IPhlexMultiSelectProps } from 'phlex-core-ui/build/src/controls/PhlexMultiSelect/PhlexMultiSelect';
import { MultiSelectBlurEvent, MultiSelectChangeEvent } from '@progress/kendo-react-dropdowns';
import { PhlexMultiSelect } from 'phlex-core-ui';
import { useTranslation } from 'react-i18next';

const Component: FC<IPhlexMultiSelectProps> = ({ name, onChange, required, onBlur, label, ...rest }) => {
  const { t } = useTranslation();
  const { control, trigger } = useFormContext();
  const { field } = useController({
    name,
    control,
    defaultValue: rest.value,
    rules: { required: required ? t('Forms.RequiredField', { field: label }) : '' },
  });

  const onChangeHandler = (x: IOption[], e: MultiSelectChangeEvent) => {
    field.onChange(e);
    onChange?.(x, e);
    trigger(name);
  };

  const onBlurHandler = (e: MultiSelectBlurEvent) => {
    field.onBlur();
    onBlur?.(e);
  };

  return (
    <PhlexMultiSelect
      {...rest}
      value={field.value}
      required={required}
      defaultValues={field.value}
      onChange={onChangeHandler}
      onBlur={onBlurHandler}
      name={name}
      label={label}
    />
  );
};

export default Component;
