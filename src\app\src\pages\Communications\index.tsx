import React, { Fragment, FunctionComponent, useCallback, useEffect, useReducer, useState } from 'react';
import { useTranslation } from 'react-i18next';
import CommunicationsTable from 'pages/Communications/CommunicationsTable';
import { MainContent } from 'components/Content/Content.styles';
import {
  PhlexBreadcrumb,
  PhlexButton,
  PhlexDatePicker,
  PhlexDropdown,
  PhlexInput,
  PhlexLayout,
  PhlexTabStrip,
  PhlexTabStripSkeleton,
} from 'phlex-core-ui';
import { useNavigate } from 'react-router';
import { routes } from 'pages';
import countryApiService from 'shared/api/country-api-service';
import { CountryModel } from 'axon-hacomms-api-sdk';
import { useOrganisationFromUrl } from 'hooks/shared';
import { CompositeFilterDescriptor } from '@progress/kendo-data-query';
import components from './CommunicationsTable.styles';
import { toFilterDateFormat } from 'shared/services/date-time-format';
import { DateFormats, tableFilters } from 'shared/consts';
import { withRestrictedPermissions } from 'hoc';
import { Permissions } from 'constants/permissions';
import sharedComponents from 'pages/index.styles';

const { PageActions, FilterPanel, ClearFilters } = PhlexLayout;

const { HaCommsPageTopBar } = sharedComponents;

const initialFilter: CompositeFilterDescriptor = {
  logic: 'and',
  filters: [
    {
      field: tableFilters.SUBJECT,
      operator: 'contains',
      value: '',
    },
    {
      field: tableFilters.COUNTRY_ID,
      operator: 'contains',
      value: 0,
    },
    {
      field: tableFilters.PRODUCT_NAMES,
      operator: 'contains',
      value: '',
    },
    {
      field: tableFilters.COMMUNICATION_DATE,
      operator: 'contains',
      value: '',
    },
    {
      field: tableFilters.CREATED_DATE,
      operator: 'contains',
      value: '',
    },
    {
      field: tableFilters.CREATED_BY,
      operator: 'contains',
      value: '',
    },
  ],
};

interface CommunicationFilterData {
  isTabChanged: boolean;
  subjectFilter: string;
  countryFilter: CountryModel | null;
  productFilter: string;
  dateOfCommunicationFilter: Date | null;
  createdDateFilter: Date | null;
  createdByFilter: string;
}

const reducer = (state: CommunicationFilterData, newState: CommunicationFilterData) => {
  return { ...state, ...newState };
};

const Component: FunctionComponent<{ basepath: string }> = ({ basepath }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const tenant = useOrganisationFromUrl();
  const { TableWrapper } = components;

  const breadcrumbs = [
    {
      key: 'substances',
      text: t('CommunicationTable.Title'),
      active: 'true' as const,
    },
  ];
  const [selectedTab, setSelectedTab] = useState(0);

  const [communicationFilters, setCommunicationFilters] = useReducer(reducer, {
    isTabChanged: false,
    subjectFilter: '',
    countryFilter: null,
    productFilter: '',
    dateOfCommunicationFilter: null,
    createdDateFilter: null,
    createdByFilter: '',
  });

  const [countries, setCountries] = useState<CountryModel[]>();
  const [updateResults, setUpdateResults] = useState(false);
  const [filters, setFilters] = useState(initialFilter);
  let changeCommunicationsFiltersFn: ((filters: CompositeFilterDescriptor) => void) | undefined = undefined;

  const getCountries = useCallback(async () => {
    const response = await countryApiService.getCountryList(tenant);
    setCountries(response.data?.data ?? []);
  }, [tenant]);

  useEffect(() => {
    getCountries();
    const selectedTabFroLocalStorage = localStorage.getItem('COMMUNICATIONS_ACTIVE_TAB');
    setSelectedTab(selectedTabFroLocalStorage == '1' ? 1 : 0);
  }, [getCountries]);

  const clearFilters = (isTabChanged: boolean) => {
    setCommunicationFilters({
      isTabChanged: isTabChanged,
      subjectFilter: '',
      countryFilter: null,
      productFilter: '',
      dateOfCommunicationFilter: null,
      createdDateFilter: null,
      createdByFilter: '',
    });
  };

  useEffect(() => {
    const filterValues: CompositeFilterDescriptor = {
      logic: 'and',
      filters: [
        {
          field: tableFilters.SUBJECT,
          operator: 'contains',
          value: communicationFilters.subjectFilter,
        },
        {
          field: tableFilters.COUNTRY_ID,
          operator: 'contains',
          value: communicationFilters.countryFilter?.id,
        },
        {
          field: tableFilters.PRODUCT_NAMES,
          operator: 'contains',
          value: communicationFilters.productFilter,
        },
        {
          field: tableFilters.COMMUNICATION_DATE,
          operator: 'contains',
          value: toFilterDateFormat(communicationFilters.dateOfCommunicationFilter),
        },
        {
          field: tableFilters.CREATED_DATE,
          operator: 'contains',
          value: toFilterDateFormat(communicationFilters.createdDateFilter),
        },
        {
          field: tableFilters.CREATED_BY,
          operator: 'contains',
          value: communicationFilters.createdByFilter,
        },
      ],
    };
    if (!communicationFilters.isTabChanged) {
      setUpdateResults(true);
    }
    setFilters(filterValues);
  }, [communicationFilters]);

  useEffect(() => {
    if (updateResults) {
      const timer = setTimeout(() => {
        changeCommunicationsFiltersFn?.(filters);
        setUpdateResults(false);
      }, 500);
      return () => clearTimeout(timer);
    }
  }, [changeCommunicationsFiltersFn, filters, updateResults]);

  const setChangeFilter = (changeFilter: (filter: CompositeFilterDescriptor) => void) => {
    changeCommunicationsFiltersFn = changeFilter;
  };

  return (
    <Fragment>
      <HaCommsPageTopBar>
        <PhlexBreadcrumb options={breadcrumbs} />
        <PageActions>
          <PhlexButton
            data-testid="axon-button-communication.add-button"
            label={t('CommunicationTable.AddCommunicationButton')}
            type="button"
            onClick={() => navigate(`${basepath}${routes.createCommunication}`)}
          />
        </PageActions>
      </HaCommsPageTopBar>
      <MainContent>
        <FilterPanel>
          <PhlexInput
            name="subject"
            onChange={(e) => setCommunicationFilters({ ...communicationFilters, isTabChanged: false, subjectFilter: e.value })}
            value={communicationFilters.subjectFilter}
            label={t('CommunicationTable.SubjectField')}
            width="fullwidth"
          />
          <PhlexDropdown
            name="country"
            data={countries}
            onChange={(e) => setCommunicationFilters({ ...communicationFilters, isTabChanged: false, countryFilter: e.value })}
            value={communicationFilters.countryFilter}
            textField="name"
            dataItemKey="id"
            label={t('CommunicationTable.CountryField')}
            width="fullwidth"
          />
          <PhlexInput
            name="drugProduct"
            onChange={(e) => setCommunicationFilters({ ...communicationFilters, isTabChanged: false, productFilter: e.value })}
            value={communicationFilters.productFilter}
            label={t('CommunicationTable.ProductField')}
            width="fullwidth"
          />
          <PhlexDatePicker
            name="dateOfCommunication"
            onChange={(e) => setCommunicationFilters({ ...communicationFilters, isTabChanged: false, dateOfCommunicationFilter: e.value })}
            value={communicationFilters.dateOfCommunicationFilter}
            placeholder={t('Common.DatePlaceholder')}
            format={DateFormats.ddMMMyyyy}
            label={t('CommunicationTable.DateOfCommunicationField')}
          />
          <PhlexDatePicker
            name="createdDate"
            onChange={(e) => setCommunicationFilters({ ...communicationFilters, isTabChanged: false, createdDateFilter: e.value })}
            value={communicationFilters.createdDateFilter}
            placeholder={t('Common.DatePlaceholder')}
            format={DateFormats.ddMMMyyyy}
            label={t('Common.CreatedDateField')}
          />
          <PhlexInput
            name="createdBy"
            onChange={(e) => setCommunicationFilters({ ...communicationFilters, isTabChanged: false, createdByFilter: e.value })}
            value={communicationFilters.createdByFilter}
            label={t('Common.CreatedByField')}
            width="fullwidth"
          />
          <ClearFilters name="filter_alt_off" clickHandler={() => clearFilters(false)} tooltip={t('Common.ClearAllFitlers')} />
        </FilterPanel>
        <TableWrapper>
          {countries === undefined ? (
            <PhlexTabStripSkeleton />
          ) : (
            <PhlexTabStrip
              selected={selectedTab}
              width="fullwidth"
              onSelect={(e) => {
                clearFilters(true);
                setSelectedTab(e.selected);
              }}
              tabs={[
                {
                  name: t('CommunicationTable.OpenTabField'),
                  content: (
                    <CommunicationsTable
                      isOpen={true}
                      cleanBasePath={basepath}
                      initialFilter={initialFilter}
                      setChangeFilter={setChangeFilter}
                      tabOpen={selectedTab}
                    />
                  ),
                },
                {
                  name: t('CommunicationTable.CompletedTabField'),
                  content: (
                    <CommunicationsTable
                      isOpen={false}
                      cleanBasePath={basepath}
                      initialFilter={initialFilter}
                      setChangeFilter={setChangeFilter}
                      tabOpen={selectedTab}
                    />
                  ),
                },
              ]}
            />
          )}
        </TableWrapper>
      </MainContent>
    </Fragment>
  );
};

export default withRestrictedPermissions(Component, [Permissions.ViewCommunication]);
