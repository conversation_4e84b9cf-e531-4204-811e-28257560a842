import AppError from 'components/Error/AppError';
import React, { FunctionComponent } from 'react';
import { useTranslation } from 'react-i18next';

const ForbiddenPage: FunctionComponent<{ basepath: string }> = ({ basepath }) => {
  const { t } = useTranslation();

  return (
    <AppError
      heading={t('ForbiddenPage.Heading')}
      subHeading={t('ForbiddenPage.Subheading')}
      suggestions={t('ForbiddenPage.Suggestions')}
      pageMissing={t('ForbiddenPage.AccessMissing')}
      backButton={t('ForbiddenPage.BackButton')}
      basepath={basepath}
    ></AppError>
  );
};

export default ForbiddenPage;
