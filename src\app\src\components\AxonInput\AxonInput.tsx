import React, { FC } from 'react';
import { useForm<PERSON>ontext, useController } from 'react-hook-form';

import { PhlexInput } from 'phlex-core-ui';
import { IPhlexInputProps } from 'phlex-core-ui/build/src/controls/PhlexInput/PhlexInput';
import { InputChangeEvent } from '@progress/kendo-react-inputs';
import { useTranslation } from 'react-i18next';

interface IRHFPhlexInputProps extends IPhlexInputProps {
  disableDefaultOnChange?: boolean;
  urlValidation?: boolean;
}
const RHFPhlexInput: FC<IRHFPhlexInputProps> = ({
  name,
  onChange,
  required,
  onBlur,
  disableDefaultOnChange,
  label,
  urlValidation,
  ...rest
}) => {
  const { t } = useTranslation();
  const { control, trigger } = useFormContext();
  const {
    field: { ...inputProps },
  } = useController({
    name,
    control,
    defaultValue: rest.value,
    rules: {
      required: required ? t('Forms.RequiredField', { field: label }) : '',
      pattern: urlValidation
        ? {
            value: /^(?:(?:https?|ftp):\/\/)(?:www\.)?[a-z0-9-]+(?:\.[a-z0-9-]+)+[^\s]*$/,
            message: t('Forms.InvalidUrl'),
          }
        : { value: /.*/, message: '' },
    },
  });

  const onChangeHandler = (e: InputChangeEvent) => {
    if (!disableDefaultOnChange) inputProps.onChange(e);
    onChange && onChange(e);
    trigger(name);
  };

  const onBlurHandler = (e: React.FocusEvent<HTMLInputElement>) => {
    inputProps.onBlur();
    onBlur && onBlur(e);
  };

  return (
    <PhlexInput
      {...rest}
      required={required}
      value={inputProps.value}
      onChange={onChangeHandler}
      onBlur={onBlurHandler}
      name={name}
      label={label}
    />
  );
};
export default RHFPhlexInput;
