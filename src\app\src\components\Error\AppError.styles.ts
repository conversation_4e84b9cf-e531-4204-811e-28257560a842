import styled from 'styled-components';
import { PhlexButton } from 'phlex-core-ui';

export default {
  Wrapper: styled.div`
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: auto;
    max-width: 50rem;
    min-height: calc(100vh - 6.5rem);
  `,
  Logo: styled.div`
    background-image: ${(props) => 'url(/assets/' + props.theme.logo + ')'};
    background-position: center;
    background-repeat: no-repeat;
    background-size: 100%;
    height: 1.25rem;
    width: 4.5rem;
  `,
  Heading: styled.h2`
    font-size: 3rem;
    margin-top: ${(props) => props.theme.spaceM};
    margin-bottom: ${(props) => props.theme.spaceXL};
  `,
  Subheading: styled.p`
    font-weight: ${(props) => props.theme.bold};
  `,
  BackButton: styled(PhlexButton)`
    margin-top: ${(props) => props.theme.spaceM};
  `,
  Image: styled.img`
    width: 12rem;
  `,
};
