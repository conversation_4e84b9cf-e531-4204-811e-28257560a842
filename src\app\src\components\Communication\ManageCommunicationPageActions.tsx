import React, { FC, Fragment, useContext, useState } from 'react';
import { PhlexButton, PhlexConfirmationDialog } from 'phlex-core-ui';
import { useTranslation } from 'react-i18next';
import { ManageCommunicationPageActionsProps } from './communication-form-types';
import CommunicationContext from 'context/CommunicationContext';
import { isCommunicationInEditState } from './communication-service';

const ManageCommunicationPageActionsComponent: FC<ManageCommunicationPageActionsProps> = ({
  isFormSubmitting,
  onCommunicationDelete,
  onCommunicationCreate,
  onCommunicationComplete,
  onCommunicationReinstate,
}) => {
  const { t } = useTranslation();
  const { communication, isNewCommunication, globalEditModeState, triggerFormValidation } = useContext(CommunicationContext);

  const [isDeleteCommunicationDialogOpen, setIsDeleteCommunicationDialogOpen] = useState(false);
  const [isCompleteCommunicationDialogOpen, setIsCompleteCommunicationDialogOpen] = useState(false);
  const [isReinstateCommunicationDialogOpen, setIsReinstateCommunicationDialogOpen] = useState(false);

  const handleDeleteCommunication = async () => {
    if (!(await triggerFormValidation())) return;

    setIsDeleteCommunicationDialogOpen(true);
  };

  const handleCompleteCommunication = async () => {
    if (!(await triggerFormValidation())) return;

    setIsCompleteCommunicationDialogOpen(true);
  };

  const handleCreateCommunication = async () => {
    if (!(await triggerFormValidation())) return;

    onCommunicationCreate();
  };

  const handleReinstateCommunication = () => {
    setIsReinstateCommunicationDialogOpen(true);
  };

  const reinstate = () => {
    onCommunicationReinstate().then(() => setIsReinstateCommunicationDialogOpen(false));
  };

  return (
    <Fragment>
      {!communication?.id && (
        <PhlexButton
          label={t('Forms.CreateButton')}
          type="button"
          onClick={handleCreateCommunication}
          hidden={!isNewCommunication}
          disabled={isFormSubmitting}
        />
      )}
      {!communication?.isCompleted && (
        <PhlexButton
          color="tertiary"
          label={t('Forms.DeleteButton')}
          type="button"
          onClick={handleDeleteCommunication}
          hidden={isNewCommunication}
          disabled={isFormSubmitting || isCommunicationInEditState(globalEditModeState)}
        />
      )}
      {!communication?.isCompleted && !isNewCommunication && (
        <PhlexButton
          label={t('Forms.CompleteButton')}
          onClick={handleCompleteCommunication}
          disabled={isCommunicationInEditState(globalEditModeState)}
          type="button"
        />
      )}
      {communication?.isCompleted && !isNewCommunication && (
        <PhlexButton
          label={t('Forms.ReinstateButton')}
          onClick={handleReinstateCommunication}
          disabled={isCommunicationInEditState(globalEditModeState)}
          type="button"
        />
      )}
      <PhlexConfirmationDialog
        isOpen={isDeleteCommunicationDialogOpen ?? false}
        onConfirm={onCommunicationDelete}
        onCancel={() => setIsDeleteCommunicationDialogOpen(false)}
        heading={t('CommunicationModals.DeleteCommunicationHeading')}
        description={t('CommunicationModals.DeleteCommunicationDescription')}
      />
      <PhlexConfirmationDialog
        isOpen={isCompleteCommunicationDialogOpen ?? false}
        onConfirm={onCommunicationComplete}
        onCancel={() => setIsCompleteCommunicationDialogOpen(false)}
        heading={t('CommunicationModals.CompleteCommunicationHeading')}
        description={t('CommunicationModals.CompleteCommunicationDescription')}
      />
      <PhlexConfirmationDialog
        isOpen={isReinstateCommunicationDialogOpen ?? false}
        onConfirm={reinstate}
        onCancel={() => setIsReinstateCommunicationDialogOpen(false)}
        heading={t('CommunicationModals.ReinstateCommunicationHeading')}
        description={t('CommunicationModals.ReinstateCommunicationDescription')}
      />
    </Fragment>
  );
};

export default ManageCommunicationPageActionsComponent;
