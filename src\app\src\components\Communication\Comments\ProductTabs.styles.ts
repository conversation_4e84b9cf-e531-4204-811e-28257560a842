import { PhlexLayout, PhlexSlideoutLink } from 'phlex-core-ui';
import styled from 'styled-components';

const { InputValidationWithTooltip } = PhlexLayout;

interface ProductTabProps {
  readonly isCurrent?: boolean;
  readonly isDisabled?: boolean;
}

export default {
  TabContent: styled.div`
    display: flex;
    margin-bottom: 1rem;
  `,
  ProductTabs: styled.div`
    background-color: ${(props) => props.theme.colors.backgroundPri};
    border-radius: ${(props) => props.theme.radius};
    box-shadow: ${(props) => props.theme.shadow};
    box-sizing: border-box;
    flex: 1;
    margin-bottom: 3rem;
    padding-left: 1rem;
    padding-right: 1rem;
    padding-top: 0.25rem;
    padding-bottom: 1rem;
    width: 25rem;
  `,
  ProductTab: styled(PhlexSlideoutLink)<ProductTabProps>`
    background-color: ${(props) => props.isCurrent && props.theme.colors.backgroundTer};
    font-weight: ${(props) => props.isCurrent && props.theme.bold};
    filter: ${(props) => props.isDisabled && 'grayscale(1)'};
    margin-left: 0;
    margin-right: 0;
    flex: 2;
  `,
  GeneralGuidance: styled.div`
    margin-bottom: ${(props) => props.theme.spaceS};
  `,
  CommentsSection: styled.div`
    display: flex;
    flex-direction: column;
  `,
  HeadingWrapper: styled.div`
    padding-left: 0.5rem;
  `,
  SectionBorder: styled.div`
    border-bottom: ${({ theme }) => `${theme.border} ${theme.colors.borderPri}`};
    box-shadow: ${({ theme }) => theme.shadow};
    margin-top: 1rem;
    margin-bottom: 0.5rem;
  `,
  Heading: styled.h2`
    font-size: ${(props) => props.theme.headingM};
    font-weight: ${(props) => props.theme.bold};
    margin-top: 0.5rem;
    margin-bottom: 1rem;
  `,
  SectionWrapper: styled.div`
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-left: 0.5rem;
    position: relative;
  `,
  SubHeading: styled.h2`
    font-size: ${(props) => props.theme.headingS};
    font-weight: ${(props) => props.theme.bold};
    margin-top: 1rem;
    margin-bottom: 1rem;
  `,
  HeadingActions: styled.div``,
  ValidationError: styled(InputValidationWithTooltip)`
    && {
      margin-right: 3.5rem;
      top: 1.125rem;
    }
  `,
};
