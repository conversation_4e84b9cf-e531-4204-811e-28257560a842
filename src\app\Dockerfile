ARG versionNo=0.0.0.0
ARG TELERIK_LICENSE

# stage: 1 — install & build
FROM node:20-alpine AS react-build
ARG versionNo
ARG TELERIK_LICENSE
LABEL autodelete=${versionNo}

WORKDIR /app
COPY ./src/app ./
RUN npm install
RUN npx kendo-ui-license activate
RUN npm run lint
RUN npm run build

# stage: 2 — run tests
FROM react-build as testrunner
ARG versionNo
ARG TELERIK_LICENSE
LABEL autodelete=${versionNo}

WORKDIR /app
ENTRYPOINT npm run test:ci

# stage: 3 — the production environment
FROM nginxinc/nginx-unprivileged:alpine
# Nginx config
RUN rm -rf /etc/nginx/conf.d
COPY ./src/app/config/nginx /etc/nginx

# Static build
COPY --from=react-build /app/dist /usr/share/nginx/html

# Default port exposure
EXPOSE 8080

# Start Nginx server
USER dotnetuser
CMD ["/bin/sh", "-c", "nginx -g \"daemon off;\""]