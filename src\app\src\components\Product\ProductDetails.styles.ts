import styled from 'styled-components';
import { PhlexLayout } from 'phlex-core-ui';

export default {
  Wrapper: styled.div``,
  Heading: styled.h2`
    font-size: ${(props) => props.theme.headingM};
    font-weight: ${(props) => props.theme.bold};
    margin-top: 2rem;
    margin-bottom: 1rem;
  `,
  Sticky: styled.div`
    position: sticky;
    top: 8.25rem;
  `,
  Form: styled(PhlexLayout.FormWrapper)`
    background-color: ${(props) => props.theme.colors.backgroundPri};
    border-radius: ${(props) => props.theme.radius};
    box-shadow: ${(props) => props.theme.shadow};
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    gap: ${(props) => props.theme.spaceL};
    margin-top: 0;
    padding: 1.5rem;
    width: 25rem;

    input {
      margin-bottom: 0;
    }
  `,
  AssociateCommentWarning: styled.div`
    color: ${(props) => props.theme.colors.textInvalid};
    font-size: ${(props) => props.theme.textS};
    line-height: ${(props) => props.theme.lineHeightHeading};
  `,
};
