import { SwitchChangeEvent } from '@progress/kendo-react-inputs';
import { AxonSwitch } from 'components/AxonSwitch';
import CommunicationContext from 'context/CommunicationContext';
import { PhlexLayout } from 'phlex-core-ui';
import React, { FC, useContext, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { CommentTextProps } from '../communication-form-types';
import components from './CommentText.styles';

const CommentTextComponent: FC<CommentTextProps> = (props: CommentTextProps) => {
  const {
    TextAreaContainer,
    LabelWrapper,
    QuestionIncludedContainer,
    QuestionIncludedSwitchWrapper,
    StyledTextArea,
    StyledTextAreaWrapper,
  } = components;
  const { commentIndex, methods, field, setQuestionIncludedValue, isCommentInEditMode } = props;
  const { communication } = useContext(CommunicationContext);
  const [isQuestionIncluded, setIsQuestionIncluded] = useState(field.isQuestionIncluded);
  const { InputLabel } = PhlexLayout;
  const { t } = useTranslation();

  const onQuestionIncludedChanged = (index: number, val: boolean) => {
    setIsQuestionIncluded(val);
    if (setQuestionIncludedValue) {
      setQuestionIncludedValue(index, val);
    }
  };

  return (
    <TextAreaContainer>
      <QuestionIncludedContainer>
        <LabelWrapper>
          <InputLabel>{t('ManageCommunication.QuestionIncludedLabel')}</InputLabel>
        </LabelWrapper>
        <QuestionIncludedSwitchWrapper>
          <AxonSwitch
            data-testid={`axon-input.comments.isQuestionIncluded_${commentIndex}`}
            name={`comments.${commentIndex}].isQuestionIncluded`}
            checked={field?.isQuestionIncluded ?? undefined}
            onChange={(e: SwitchChangeEvent) => onQuestionIncludedChanged(commentIndex, e.value)}
            disabled={communication?.isCompleted || !isCommentInEditMode}
          />
        </QuestionIncludedSwitchWrapper>
      </QuestionIncludedContainer>
      {isQuestionIncluded === false && (
        <StyledTextArea
          data-testid={`axon-input.comments.description_${commentIndex}`}
          name={`comments.${commentIndex}.description`}
          value={field.description ?? ''}
          label={t('ManageCommunication.Description')}
          width="fullwidth"
          required={true}
          validationMessage={methods.formState.errors.comments?.[commentIndex]?.description?.message ?? undefined}
          rows={5}
          readOnly={communication?.isCompleted || !isCommentInEditMode}
        />
      )}
      {isQuestionIncluded === true && (
        <StyledTextAreaWrapper>
          <StyledTextArea
            data-testid={`axon-input.comments.question_${commentIndex}`}
            name={`comments.${commentIndex}].question`}
            value={field.question ?? ''}
            label={t('ManageCommunication.Question')}
            width="fullwidth"
            required={true}
            validationMessage={methods.formState.errors.comments?.[commentIndex]?.question?.message ?? undefined}
            rows={5}
            disabled={communication?.isCompleted || !isCommentInEditMode}
          />
          <StyledTextArea
            data-testid={`axon-input.comments.response_${commentIndex}`}
            name={`comments.${commentIndex}].response`}
            value={field.response ?? ''}
            label={t('ManageCommunication.Answer')}
            width="fullwidth"
            required={true}
            validationMessage={methods.formState.errors.comments?.[commentIndex]?.response?.message ?? undefined}
            rows={5}
            disabled={communication?.isCompleted || !isCommentInEditMode}
          />
        </StyledTextAreaWrapper>
      )}
    </TextAreaContainer>
  );
};

export default CommentTextComponent;
