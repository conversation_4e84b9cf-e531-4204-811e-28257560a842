import styled from 'styled-components';

export default {
  SearchCommunicationOverview: styled.div`
    background-color: ${(props) => props.theme.colors.backgroundPri};
    border-radius: ${(props) => props.theme.radius};
    box-shadow: ${(props) => props.theme.shadow};
    display: flex;
    flex: 1;
    flex-direction: column;
    overflow: auto;
    padding: 0.5rem;
  `,
  SearchCommunicationOverviewContainer: styled.div`
    display: flex;
    flex: 1;
    flex-direction: column;
    margin-right: 1rem;
    margin-top: 3rem;
    margin-bottom: 4rem;
  `,
  SearchOverviewTitle: styled.h3`
    font-size: ${(props) => props.theme.headingM};
    font-weight: ${(props) => props.theme.bold};
    margin-top: 0;
    margin-bottom: 1rem;
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  `,
  ApplicationNumbersContainer: styled.div`
    margin-bottom: 0.5rem;
    border-radius: ${(props) => props.theme.radius};
    border: 1px solid ${(props) => props.theme.colors.borderPri};
    &:hover {
      background-color: ${(props) => props.theme.colors.backgroundTer};
    }
  `,
  SubmissionNumbersContainer: styled.div`
    border-top: 1px solid ${(props) => props.theme.colors.borderPri};
    padding: 1rem;
  `,
  ApplicationNumberWrapper: styled.div`
    padding: 1rem;
  `,
  ApplicationLabelWrapper: styled.div`
    font-weight: ${(props) => props.theme.bold};
    padding-bottom: 0.25rem;
  `,
  ValueWrapper: styled.div`
    padding-bottom: 0.25rem;
    line-height: 1.25;
  `,
};
